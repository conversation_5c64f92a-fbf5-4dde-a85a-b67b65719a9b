;(function() {

    var undefined;

    var VERSION = '4.17.4';

    var LARGE_ARRAY_SIZE = 200;

    var CORE_ERROR_TEXT = 'Unsupported core-js use. Try https://npms.io/search?q=ponyfill.',
        FUNC_ERROR_TEXT = 'Expected a function';

    var HASH_UNDEFINED = '__lodash_hash_undefined__';

    var MAX_MEMOIZE_SIZE = 500;

    var PLACEHOLDER = '__lodash_placeholder__';

    var CLONE_DEEP_FLAG = 1,
        CLONE_FLAT_FLAG = 2,
        CLONE_SYMBOLS_FLAG = 4;

    var COMPARE_PARTIAL_FLAG = 1,
        COMPARE_UNORDERED_FLAG = 2;

    var WRAP_BIND_FLAG = 1,
        WRAP_BIND_KEY_FLAG = 2,
        WRAP_CURRY_BOUND_FLAG = 4,
        WRAP_CURRY_FLAG = 8,
        WRAP_CURRY_RIGHT_FLAG = 16,
        WRAP_PARTIAL_FLAG = 32,
        WRAP_PARTIAL_RIGHT_FLAG = 64,
        WRAP_ARY_FLAG = 128,
        WRAP_REARG_FLAG = 256,
        WRAP_FLIP_FLAG = 512;

    var DEFAULT_TRUNC_LENGTH = 30,
        DEFAULT_TRUNC_OMISSION = '...';

    var HOT_COUNT = 800,
        HOT_SPAN = 16;

    var LAZY_FILTER_FLAG = 1,
        LAZY_MAP_FLAG = 2,
        LAZY_WHILE_FLAG = 3;

    var INFINITY = 1 / 0,
        MAX_SAFE_INTEGER = 9007199254740991,
        MAX_INTEGER = 1.7976931348623157e+308,
        NAN = 0 / 0;

    var MAX_ARRAY_LENGTH = 4294967295,
        MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1,
        HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH >>> 1;

    var wrapFlags = [
        ['ary', WRAP_ARY_FLAG],
        ['bind', WRAP_BIND_FLAG],
        ['bindKey', WRAP_BIND_KEY_FLAG],
        ['curry', WRAP_CURRY_FLAG],
        ['curryRight', WRAP_CURRY_RIGHT_FLAG],
        ['flip', WRAP_FLIP_FLAG],
        ['partial', WRAP_PARTIAL_FLAG],
        ['partialRight', WRAP_PARTIAL_RIGHT_FLAG],
        ['rearg', WRAP_REARG_FLAG]
    ];

    var argsTag = '[object Arguments]',
        arrayTag = '[object Array]',
        asyncTag = '[object AsyncFunction]',
        boolTag = '[object Boolean]',
        dateTag = '[object Date]',
        domExcTag = '[object DOMException]',
        errorTag = '[object Error]',
        funcTag = '[object Function]',
        genTag = '[object GeneratorFunction]',
        mapTag = '[object Map]',
        numberTag = '[object Number]',
        nullTag = '[object Null]',
        objectTag = '[object Object]',
        promiseTag = '[object Promise]',
        proxyTag = '[object Proxy]',
        regexpTag = '[object RegExp]',
        setTag = '[object Set]',
        stringTag = '[object String]',
        symbolTag = '[object Symbol]',
        undefinedTag = '[object Undefined]',
        weakMapTag = '[object WeakMap]',
        weakSetTag = '[object WeakSet]';

    var arrayBufferTag = '[object ArrayBuffer]',
        dataViewTag = '[object DataView]',
        float32Tag = '[object Float32Array]',
        float64Tag = '[object Float64Array]',
        int8Tag = '[object Int8Array]',
        int16Tag = '[object Int16Array]',
        int32Tag = '[object Int32Array]',
        uint8Tag = '[object Uint8Array]',
        uint8ClampedTag = '[object Uint8ClampedArray]',
        uint16Tag = '[object Uint16Array]',
        uint32Tag = '[object Uint32Array]';

    var reEmptyStringLeading = /\b__p \+= '';/g,
        reEmptyStringMiddle = /\b(__p \+=) '' \+/g,
        reEmptyStringTrailing = /(__e\(.*?\)|\b__t\)) \+\n'';/g;

    var reEscapedHtml = /&(?:amp|lt|gt|quot|#39);/g,
        reUnescapedHtml = /[&<>"']/g,
        reHasEscapedHtml = RegExp(reEscapedHtml.source),
        reHasUnescapedHtml = RegExp(reUnescapedHtml.source);

    var reEscape = /<%-([\s\S]+?)%>/g,
        reEvaluate = /<%([\s\S]+?)%>/g,
        reInterpolate = /<%=([\s\S]+?)%>/g;

    var reIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,
        reIsPlainProp = /^\w*$/,
        reLeadingDot = /^\./,
        rePropName = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;

    var reRegExpChar = /[\\^$.*+?()[\]{}|]/g,
        reHasRegExpChar = RegExp(reRegExpChar.source);

    var reTrim = /^\s+|\s+$/g,
        reTrimStart = /^\s+/,
        reTrimEnd = /\s+$/;

    var reWrapComment = /\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,
        reWrapDetails = /\{\n\/\* \[wrapped with (.+)\] \*/,
        reSplitDetails = /,? & /;

    var reAsciiWord = /[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;

    var reEscapeChar = /\\(\\)?/g;

    var reEsTemplate = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g;

    var reFlags = /\w*$/;

    var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;

    var reIsBinary = /^0b[01]+$/i;

    var reIsHostCtor = /^\[object .+?Constructor\]$/;

    var reIsOctal = /^0o[0-7]+$/i;

    var reIsUint = /^(?:0|[1-9]\d*)$/;

    var reLatin = /[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g;

    var reNoMatch = /($^)/;

    var reUnescapedString = /['\n\r\u2028\u2029\\]/g;

    var rsAstralRange = '\\ud800-\\udfff',
        rsComboMarksRange = '\\u0300-\\u036f',
        reComboHalfMarksRange = '\\ufe20-\\ufe2f',
        rsComboSymbolsRange = '\\u20d0-\\u20ff',
        rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,
        rsDingbatRange = '\\u2700-\\u27bf',
        rsLowerRange = 'a-z\\xdf-\\xf6\\xf8-\\xff',
        rsMathOpRange = '\\xac\\xb1\\xd7\\xf7',
        rsNonCharRange = '\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf',
        rsPunctuationRange = '\\u2000-\\u206f',
        rsSpaceRange = ' \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000',
        rsUpperRange = 'A-Z\\xc0-\\xd6\\xd8-\\xde',
        rsVarRange = '\\ufe0e\\ufe0f',
        rsBreakRange = rsMathOpRange + rsNonCharRange + rsPunctuationRange + rsSpaceRange;

    var rsApos = "['\u2019]",
        rsAstral = '[' + rsAstralRange + ']',
        rsBreak = '[' + rsBreakRange + ']',
        rsCombo = '[' + rsComboRange + ']',
        rsDigits = '\\d+',
        rsDingbat = '[' + rsDingbatRange + ']',
        rsLower = '[' + rsLowerRange + ']',
        rsMisc = '[^' + rsAstralRange + rsBreakRange + rsDigits + rsDingbatRange + rsLowerRange + rsUpperRange + ']',
        rsFitz = '\\ud83c[\\udffb-\\udfff]',
        rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',
        rsNonAstral = '[^' + rsAstralRange + ']',
        rsRegional = '(?:\\ud83c[\\udde6-\\uddff]){2}',
        rsSurrPair = '[\\ud800-\\udbff][\\udc00-\\udfff]',
        rsUpper = '[' + rsUpperRange + ']',
        rsZWJ = '\\u200d';

    var rsMiscLower = '(?:' + rsLower + '|' + rsMisc + ')',
        rsMiscUpper = '(?:' + rsUpper + '|' + rsMisc + ')',
        rsOptContrLower = '(?:' + rsApos + '(?:d|ll|m|re|s|t|ve))?',
        rsOptContrUpper = '(?:' + rsApos + '(?:D|LL|M|RE|S|T|VE))?',
        reOptMod = rsModifier + '?',
        rsOptVar = '[' + rsVarRange + ']?',
        rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',
        rsOrdLower = '\\d*(?:(?:1st|2nd|3rd|(?![123])\\dth)\\b)',
        rsOrdUpper = '\\d*(?:(?:1ST|2ND|3RD|(?![123])\\dTH)\\b)',
        rsSeq = rsOptVar + reOptMod + rsOptJoin,
        rsEmoji = '(?:' + [rsDingbat, rsRegional, rsSurrPair].join('|') + ')' + rsSeq,
        rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';

    var reApos = RegExp(rsApos, 'g');

    var reComboMark = RegExp(rsCombo, 'g');

    var reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');

    var reUnicodeWord = RegExp([
        rsUpper + '?' + rsLower + '+' + rsOptContrLower + '(?=' + [rsBreak, rsUpper, '$'].join('|') + ')',
        rsMiscUpper + '+' + rsOptContrUpper + '(?=' + [rsBreak, rsUpper + rsMiscLower, '$'].join('|') + ')',
        rsUpper + '?' + rsMiscLower + '+' + rsOptContrLower,
        rsUpper + '+' + rsOptContrUpper,
        rsOrdUpper,
        rsOrdLower,
        rsDigits,
        rsEmoji
    ].join('|'), 'g');

    var reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange  + rsComboRange + rsVarRange + ']');

    var reHasUnicodeWord = /[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;

    var contextProps = [
        'Array', 'Buffer', 'DataView', 'Date', 'Error', 'Float32Array', 'Float64Array',
        'Function', 'Int8Array', 'Int16Array', 'Int32Array', 'Map', 'Math', 'Object',
        'Promise', 'RegExp', 'Set', 'String', 'Symbol', 'TypeError', 'Uint8Array',
        'Uint8ClampedArray', 'Uint16Array', 'Uint32Array', 'WeakMap',
        '_', 'clearTimeout', 'isFinite', 'parseInt', 'setTimeout'
    ];

    var templateCounter = -1;

    var typedArrayTags = {};
    typedArrayTags[float32Tag] = typedArrayTags[float64Tag] =
        typedArrayTags[int8Tag] = typedArrayTags[int16Tag] =
            typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =
                typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =
                    typedArrayTags[uint32Tag] = true;
    typedArrayTags[argsTag] = typedArrayTags[arrayTag] =
        typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =
            typedArrayTags[dataViewTag] = typedArrayTags[dateTag] =
                typedArrayTags[errorTag] = typedArrayTags[funcTag] =
                    typedArrayTags[mapTag] = typedArrayTags[numberTag] =
                        typedArrayTags[objectTag] = typedArrayTags[regexpTag] =
                            typedArrayTags[setTag] = typedArrayTags[stringTag] =
                                typedArrayTags[weakMapTag] = false;

    var cloneableTags = {};
    cloneableTags[argsTag] = cloneableTags[arrayTag] =
        cloneableTags[arrayBufferTag] = cloneableTags[dataViewTag] =
            cloneableTags[boolTag] = cloneableTags[dateTag] =
                cloneableTags[float32Tag] = cloneableTags[float64Tag] =
                    cloneableTags[int8Tag] = cloneableTags[int16Tag] =
                        cloneableTags[int32Tag] = cloneableTags[mapTag] =
                            cloneableTags[numberTag] = cloneableTags[objectTag] =
                                cloneableTags[regexpTag] = cloneableTags[setTag] =
                                    cloneableTags[stringTag] = cloneableTags[symbolTag] =
                                        cloneableTags[uint8Tag] = cloneableTags[uint8ClampedTag] =
                                            cloneableTags[uint16Tag] = cloneableTags[uint32Tag] = true;
    cloneableTags[errorTag] = cloneableTags[funcTag] =
        cloneableTags[weakMapTag] = false;

    var deburredLetters = {
        '\xc0': 'A',  '\xc1': 'A', '\xc2': 'A', '\xc3': 'A', '\xc4': 'A', '\xc5': 'A',
        '\xe0': 'a',  '\xe1': 'a', '\xe2': 'a', '\xe3': 'a', '\xe4': 'a', '\xe5': 'a',
        '\xc7': 'C',  '\xe7': 'c',
        '\xd0': 'D',  '\xf0': 'd',
        '\xc8': 'E',  '\xc9': 'E', '\xca': 'E', '\xcb': 'E',
        '\xe8': 'e',  '\xe9': 'e', '\xea': 'e', '\xeb': 'e',
        '\xcc': 'I',  '\xcd': 'I', '\xce': 'I', '\xcf': 'I',
        '\xec': 'i',  '\xed': 'i', '\xee': 'i', '\xef': 'i',
        '\xd1': 'N',  '\xf1': 'n',
        '\xd2': 'O',  '\xd3': 'O', '\xd4': 'O', '\xd5': 'O', '\xd6': 'O', '\xd8': 'O',
        '\xf2': 'o',  '\xf3': 'o', '\xf4': 'o', '\xf5': 'o', '\xf6': 'o', '\xf8': 'o',
        '\xd9': 'U',  '\xda': 'U', '\xdb': 'U', '\xdc': 'U',
        '\xf9': 'u',  '\xfa': 'u', '\xfb': 'u', '\xfc': 'u',
        '\xdd': 'Y',  '\xfd': 'y', '\xff': 'y',
        '\xc6': 'Ae', '\xe6': 'ae',
        '\xde': 'Th', '\xfe': 'th',
        '\xdf': 'ss',
        '\u0100': 'A',  '\u0102': 'A', '\u0104': 'A',
        '\u0101': 'a',  '\u0103': 'a', '\u0105': 'a',
        '\u0106': 'C',  '\u0108': 'C', '\u010a': 'C', '\u010c': 'C',
        '\u0107': 'c',  '\u0109': 'c', '\u010b': 'c', '\u010d': 'c',
        '\u010e': 'D',  '\u0110': 'D', '\u010f': 'd', '\u0111': 'd',
        '\u0112': 'E',  '\u0114': 'E', '\u0116': 'E', '\u0118': 'E', '\u011a': 'E',
        '\u0113': 'e',  '\u0115': 'e', '\u0117': 'e', '\u0119': 'e', '\u011b': 'e',
        '\u011c': 'G',  '\u011e': 'G', '\u0120': 'G', '\u0122': 'G',
        '\u011d': 'g',  '\u011f': 'g', '\u0121': 'g', '\u0123': 'g',
        '\u0124': 'H',  '\u0126': 'H', '\u0125': 'h', '\u0127': 'h',
        '\u0128': 'I',  '\u012a': 'I', '\u012c': 'I', '\u012e': 'I', '\u0130': 'I',
        '\u0129': 'i',  '\u012b': 'i', '\u012d': 'i', '\u012f': 'i', '\u0131': 'i',
        '\u0134': 'J',  '\u0135': 'j',
        '\u0136': 'K',  '\u0137': 'k', '\u0138': 'k',
        '\u0139': 'L',  '\u013b': 'L', '\u013d': 'L', '\u013f': 'L', '\u0141': 'L',
        '\u013a': 'l',  '\u013c': 'l', '\u013e': 'l', '\u0140': 'l', '\u0142': 'l',
        '\u0143': 'N',  '\u0145': 'N', '\u0147': 'N', '\u014a': 'N',
        '\u0144': 'n',  '\u0146': 'n', '\u0148': 'n', '\u014b': 'n',
        '\u014c': 'O',  '\u014e': 'O', '\u0150': 'O',
        '\u014d': 'o',  '\u014f': 'o', '\u0151': 'o',
        '\u0154': 'R',  '\u0156': 'R', '\u0158': 'R',
        '\u0155': 'r',  '\u0157': 'r', '\u0159': 'r',
        '\u015a': 'S',  '\u015c': 'S', '\u015e': 'S', '\u0160': 'S',
        '\u015b': 's',  '\u015d': 's', '\u015f': 's', '\u0161': 's',
        '\u0162': 'T',  '\u0164': 'T', '\u0166': 'T',
        '\u0163': 't',  '\u0165': 't', '\u0167': 't',
        '\u0168': 'U',  '\u016a': 'U', '\u016c': 'U', '\u016e': 'U', '\u0170': 'U', '\u0172': 'U',
        '\u0169': 'u',  '\u016b': 'u', '\u016d': 'u', '\u016f': 'u', '\u0171': 'u', '\u0173': 'u',
        '\u0174': 'W',  '\u0175': 'w',
        '\u0176': 'Y',  '\u0177': 'y', '\u0178': 'Y',
        '\u0179': 'Z',  '\u017b': 'Z', '\u017d': 'Z',
        '\u017a': 'z',  '\u017c': 'z', '\u017e': 'z',
        '\u0132': 'IJ', '\u0133': 'ij',
        '\u0152': 'Oe', '\u0153': 'oe',
        '\u0149': "'n", '\u017f': 's'
    };

    var htmlEscapes = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
    };

    var htmlUnescapes = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'"
    };

    var stringEscapes = {
        '\\': '\\',
        "'": "'",
        '\n': 'n',
        '\r': 'r',
        '\u2028': 'u2028',
        '\u2029': 'u2029'
    };

    var freeParseFloat = parseFloat,
        freeParseInt = parseInt;

    var freeGlobal = typeof global == 'object' && global && global.Object === Object && global;

    var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

    var root = freeGlobal || freeSelf || Function('return this')();

    var freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;

    var freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;

    var moduleExports = freeModule && freeModule.exports === freeExports;

    var freeProcess = moduleExports && freeGlobal.process;

    var nodeUtil = (function() {
        try {
            return freeProcess && freeProcess.binding && freeProcess.binding('util');
        } catch (e) {}
    }());

    var nodeIsArrayBuffer = nodeUtil && nodeUtil.isArrayBuffer,
        nodeIsDate = nodeUtil && nodeUtil.isDate,
        nodeIsMap = nodeUtil && nodeUtil.isMap,
        nodeIsRegExp = nodeUtil && nodeUtil.isRegExp,
        nodeIsSet = nodeUtil && nodeUtil.isSet,
        nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;


    function addMapEntry(map, pair) {
        map.set(pair[0], pair[1]);
        return map;
    }

    function addSetEntry(set, value) {
        set.add(value);
        return set;
    }

    function apply(func, thisArg, args) {
        switch (args.length) {
            case 0: return func.call(thisArg);
            case 1: return func.call(thisArg, args[0]);
            case 2: return func.call(thisArg, args[0], args[1]);
            case 3: return func.call(thisArg, args[0], args[1], args[2]);
        }
        return func.apply(thisArg, args);
    }

    function arrayAggregator(array, setter, iteratee, accumulator) {
        var index = -1,
            length = array == null ? 0 : array.length;

        while (++index < length) {
            var value = array[index];
            setter(accumulator, value, iteratee(value), array);
        }
        return accumulator;
    }

    function arrayEach(array, iteratee) {
        var index = -1,
            length = array == null ? 0 : array.length;

        while (++index < length) {
            if (iteratee(array[index], index, array) === false) {
                break;
            }
        }
        return array;
    }

    function arrayEachRight(array, iteratee) {
        var length = array == null ? 0 : array.length;

        while (length--) {
            if (iteratee(array[length], length, array) === false) {
                break;
            }
        }
        return array;
    }

    function arrayEvery(array, predicate) {
        var index = -1,
            length = array == null ? 0 : array.length;

        while (++index < length) {
            if (!predicate(array[index], index, array)) {
                return false;
            }
        }
        return true;
    }

    function arrayFilter(array, predicate) {
        var index = -1,
            length = array == null ? 0 : array.length,
            resIndex = 0,
            result = [];

        while (++index < length) {
            var value = array[index];
            if (predicate(value, index, array)) {
                result[resIndex++] = value;
            }
        }
        return result;
    }

    function arrayIncludes(array, value) {
        var length = array == null ? 0 : array.length;
        return !!length && baseIndexOf(array, value, 0) > -1;
    }

    function arrayIncludesWith(array, value, comparator) {
        var index = -1,
            length = array == null ? 0 : array.length;

        while (++index < length) {
            if (comparator(value, array[index])) {
                return true;
            }
        }
        return false;
    }

    function arrayMap(array, iteratee) {
        var index = -1,
            length = array == null ? 0 : array.length,
            result = Array(length);

        while (++index < length) {
            result[index] = iteratee(array[index], index, array);
        }
        return result;
    }

    function arrayPush(array, values) {
        var index = -1,
            length = values.length,
            offset = array.length;

        while (++index < length) {
            array[offset + index] = values[index];
        }
        return array;
    }

    function arrayReduce(array, iteratee, accumulator, initAccum) {
        var index = -1,
            length = array == null ? 0 : array.length;

        if (initAccum && length) {
            accumulator = array[++index];
        }
        while (++index < length) {
            accumulator = iteratee(accumulator, array[index], index, array);
        }
        return accumulator;
    }

    function arrayReduceRight(array, iteratee, accumulator, initAccum) {
        var length = array == null ? 0 : array.length;
        if (initAccum && length) {
            accumulator = array[--length];
        }
        while (length--) {
            accumulator = iteratee(accumulator, array[length], length, array);
        }
        return accumulator;
    }

    function arraySome(array, predicate) {
        var index = -1,
            length = array == null ? 0 : array.length;

        while (++index < length) {
            if (predicate(array[index], index, array)) {
                return true;
            }
        }
        return false;
    }

    var asciiSize = baseProperty('length');

    function asciiToArray(string) {
        return string.split('');
    }

    function asciiWords(string) {
        return string.match(reAsciiWord) || [];
    }

    function baseFindKey(collection, predicate, eachFunc) {
        var result;
        eachFunc(collection, function(value, key, collection) {
            if (predicate(value, key, collection)) {
                result = key;
                return false;
            }
        });
        return result;
    }

    function baseFindIndex(array, predicate, fromIndex, fromRight) {
        var length = array.length,
            index = fromIndex + (fromRight ? 1 : -1);

        while ((fromRight ? index-- : ++index < length)) {
            if (predicate(array[index], index, array)) {
                return index;
            }
        }
        return -1;
    }

    function baseIndexOf(array, value, fromIndex) {
        return value === value
            ? strictIndexOf(array, value, fromIndex)
            : baseFindIndex(array, baseIsNaN, fromIndex);
    }

    function baseIndexOfWith(array, value, fromIndex, comparator) {
        var index = fromIndex - 1,
            length = array.length;

        while (++index < length) {
            if (comparator(array[index], value)) {
                return index;
            }
        }
        return -1;
    }

    function baseIsNaN(value) {
        return value !== value;
    }

    function baseMean(array, iteratee) {
        var length = array == null ? 0 : array.length;
        return length ? (baseSum(array, iteratee) / length) : NAN;
    }

    function baseProperty(key) {
        return function(object) {
            return object == null ? undefined : object[key];
        };
    }

    function basePropertyOf(object) {
        return function(key) {
            return object == null ? undefined : object[key];
        };
    }

    function baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {
        eachFunc(collection, function(value, index, collection) {
            accumulator = initAccum
                ? (initAccum = false, value)
                : iteratee(accumulator, value, index, collection);
        });
        return accumulator;
    }

    function baseSortBy(array, comparer) {
        var length = array.length;

        array.sort(comparer);
        while (length--) {
            array[length] = array[length].value;
        }
        return array;
    }

    function baseSum(array, iteratee) {
        var result,
            index = -1,
            length = array.length;

        while (++index < length) {
            var current = iteratee(array[index]);
            if (current !== undefined) {
                result = result === undefined ? current : (result + current);
            }
        }
        return result;
    }

    function baseTimes(n, iteratee) {
        var index = -1,
            result = Array(n);

        while (++index < n) {
            result[index] = iteratee(index);
        }
        return result;
    }

    function baseToPairs(object, props) {
        return arrayMap(props, function(key) {
            return [key, object[key]];
        });
    }

    function baseUnary(func) {
        return function(value) {
            return func(value);
        };
    }

    function baseValues(object, props) {
        return arrayMap(props, function(key) {
            return object[key];
        });
    }

    function cacheHas(cache, key) {
        return cache.has(key);
    }

    function charsStartIndex(strSymbols, chrSymbols) {
        var index = -1,
            length = strSymbols.length;

        while (++index < length && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {}
        return index;
    }

    function charsEndIndex(strSymbols, chrSymbols) {
        var index = strSymbols.length;

        while (index-- && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {}
        return index;
    }

    function countHolders(array, placeholder) {
        var length = array.length,
            result = 0;

        while (length--) {
            if (array[length] === placeholder) {
                ++result;
            }
        }
        return result;
    }

    var deburrLetter = basePropertyOf(deburredLetters);

    var escapeHtmlChar = basePropertyOf(htmlEscapes);

    function escapeStringChar(chr) {
        return '\\' + stringEscapes[chr];
    }

    function getValue(object, key) {
        return object == null ? undefined : object[key];
    }

    function hasUnicode(string) {
        return reHasUnicode.test(string);
    }

    function hasUnicodeWord(string) {
        return reHasUnicodeWord.test(string);
    }

    function iteratorToArray(iterator) {
        var data,
            result = [];

        while (!(data = iterator.next()).done) {
            result.push(data.value);
        }
        return result;
    }

    function mapToArray(map) {
        var index = -1,
            result = Array(map.size);

        map.forEach(function(value, key) {
            result[++index] = [key, value];
        });
        return result;
    }

    function overArg(func, transform) {
        return function(arg) {
            return func(transform(arg));
        };
    }

    function replaceHolders(array, placeholder) {
        var index = -1,
            length = array.length,
            resIndex = 0,
            result = [];

        while (++index < length) {
            var value = array[index];
            if (value === placeholder || value === PLACEHOLDER) {
                array[index] = PLACEHOLDER;
                result[resIndex++] = index;
            }
        }
        return result;
    }

    function setToArray(set) {
        var index = -1,
            result = Array(set.size);

        set.forEach(function(value) {
            result[++index] = value;
        });
        return result;
    }

    function setToPairs(set) {
        var index = -1,
            result = Array(set.size);

        set.forEach(function(value) {
            result[++index] = [value, value];
        });
        return result;
    }

    function strictIndexOf(array, value, fromIndex) {
        var index = fromIndex - 1,
            length = array.length;

        while (++index < length) {
            if (array[index] === value) {
                return index;
            }
        }
        return -1;
    }

    function strictLastIndexOf(array, value, fromIndex) {
        var index = fromIndex + 1;
        while (index--) {
            if (array[index] === value) {
                return index;
            }
        }
        return index;
    }

    function stringSize(string) {
        return hasUnicode(string)
            ? unicodeSize(string)
            : asciiSize(string);
    }

    function stringToArray(string) {
        return hasUnicode(string)
            ? unicodeToArray(string)
            : asciiToArray(string);
    }

    var unescapeHtmlChar = basePropertyOf(htmlUnescapes);

    function unicodeSize(string) {
        var result = reUnicode.lastIndex = 0;
        while (reUnicode.test(string)) {
            ++result;
        }
        return result;
    }

    function unicodeToArray(string) {
        return string.match(reUnicode) || [];
    }

    function unicodeWords(string) {
        return string.match(reUnicodeWord) || [];
    }


    var runInContext = (function runInContext(context) {
        context = context == null ? root : _.defaults(root.Object(), context, _.pick(root, contextProps));

        var Array = context.Array,
            Date = context.Date,
            Error = context.Error,
            Function = context.Function,
            Math = context.Math,
            Object = context.Object,
            RegExp = context.RegExp,
            String = context.String,
            TypeError = context.TypeError;

        var arrayProto = Array.prototype,
            funcProto = Function.prototype,
            objectProto = Object.prototype;

        var coreJsData = context['__core-js_shared__'];

        var funcToString = funcProto.toString;

        var hasOwnProperty = objectProto.hasOwnProperty;

        var idCounter = 0;

        var maskSrcKey = (function() {
            var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');
            return uid ? ('Symbol(src)_1.' + uid) : '';
        }());

        var nativeObjectToString = objectProto.toString;

        var objectCtorString = funcToString.call(Object);

        var oldDash = root._;

        var reIsNative = RegExp('^' +
            funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\$&')
                .replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, '$1.*?') + '$'
        );

        var Buffer = moduleExports ? context.Buffer : undefined,
            Symbol = context.Symbol,
            Uint8Array = context.Uint8Array,
            allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined,
            getPrototype = overArg(Object.getPrototypeOf, Object),
            objectCreate = Object.create,
            propertyIsEnumerable = objectProto.propertyIsEnumerable,
            splice = arrayProto.splice,
            spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined,
            symIterator = Symbol ? Symbol.iterator : undefined,
            symToStringTag = Symbol ? Symbol.toStringTag : undefined;

        var defineProperty = (function() {
            try {
                var func = getNative(Object, 'defineProperty');
                func({}, '', {});
                return func;
            } catch (e) {}
        }());

        var ctxClearTimeout = context.clearTimeout !== root.clearTimeout && context.clearTimeout,
            ctxNow = Date && Date.now !== root.Date.now && Date.now,
            ctxSetTimeout = context.setTimeout !== root.setTimeout && context.setTimeout;

        var nativeCeil = Math.ceil,
            nativeFloor = Math.floor,
            nativeGetSymbols = Object.getOwnPropertySymbols,
            nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined,
            nativeIsFinite = context.isFinite,
            nativeJoin = arrayProto.join,
            nativeKeys = overArg(Object.keys, Object),
            nativeMax = Math.max,
            nativeMin = Math.min,
            nativeNow = Date.now,
            nativeParseInt = context.parseInt,
            nativeRandom = Math.random,
            nativeReverse = arrayProto.reverse;

        var DataView = getNative(context, 'DataView'),
            Map = getNative(context, 'Map'),
            Promise = getNative(context, 'Promise'),
            Set = getNative(context, 'Set'),
            WeakMap = getNative(context, 'WeakMap'),
            nativeCreate = getNative(Object, 'create');

        var metaMap = WeakMap && new WeakMap;

        var realNames = {};

        var dataViewCtorString = toSource(DataView),
            mapCtorString = toSource(Map),
            promiseCtorString = toSource(Promise),
            setCtorString = toSource(Set),
            weakMapCtorString = toSource(WeakMap);

        var symbolProto = Symbol ? Symbol.prototype : undefined,
            symbolValueOf = symbolProto ? symbolProto.valueOf : undefined,
            symbolToString = symbolProto ? symbolProto.toString : undefined;


        function lodash(value) {
            if (isObjectLike(value) && !isArray(value) && !(value instanceof LazyWrapper)) {
                if (value instanceof LodashWrapper) {
                    return value;
                }
                if (hasOwnProperty.call(value, '__wrapped__')) {
                    return wrapperClone(value);
                }
            }
            return new LodashWrapper(value);
        }

        var baseCreate = (function() {
            function object() {}
            return function(proto) {
                if (!isObject(proto)) {
                    return {};
                }
                if (objectCreate) {
                    return objectCreate(proto);
                }
                object.prototype = proto;
                var result = new object;
                object.prototype = undefined;
                return result;
            };
        }());

        function baseLodash() {
        }

        function LodashWrapper(value, chainAll) {
            this.__wrapped__ = value;
            this.__actions__ = [];
            this.__chain__ = !!chainAll;
            this.__index__ = 0;
            this.__values__ = undefined;
        }

        lodash.templateSettings = {

            'escape': reEscape,

            'evaluate': reEvaluate,

            'interpolate': reInterpolate,

            'variable': '',

            'imports': {

                '_': lodash
            }
        };

        lodash.prototype = baseLodash.prototype;
        lodash.prototype.constructor = lodash;

        LodashWrapper.prototype = baseCreate(baseLodash.prototype);
        LodashWrapper.prototype.constructor = LodashWrapper;


        function LazyWrapper(value) {
            this.__wrapped__ = value;
            this.__actions__ = [];
            this.__dir__ = 1;
            this.__filtered__ = false;
            this.__iteratees__ = [];
            this.__takeCount__ = MAX_ARRAY_LENGTH;
            this.__views__ = [];
        }

        function lazyClone() {
            var result = new LazyWrapper(this.__wrapped__);
            result.__actions__ = copyArray(this.__actions__);
            result.__dir__ = this.__dir__;
            result.__filtered__ = this.__filtered__;
            result.__iteratees__ = copyArray(this.__iteratees__);
            result.__takeCount__ = this.__takeCount__;
            result.__views__ = copyArray(this.__views__);
            return result;
        }

        function lazyReverse() {
            if (this.__filtered__) {
                var result = new LazyWrapper(this);
                result.__dir__ = -1;
                result.__filtered__ = true;
            } else {
                result = this.clone();
                result.__dir__ *= -1;
            }
            return result;
        }

        function lazyValue() {
            var array = this.__wrapped__.value(),
                dir = this.__dir__,
                isArr = isArray(array),
                isRight = dir < 0,
                arrLength = isArr ? array.length : 0,
                view = getView(0, arrLength, this.__views__),
                start = view.start,
                end = view.end,
                length = end - start,
                index = isRight ? end : (start - 1),
                iteratees = this.__iteratees__,
                iterLength = iteratees.length,
                resIndex = 0,
                takeCount = nativeMin(length, this.__takeCount__);

            if (!isArr || (!isRight && arrLength == length && takeCount == length)) {
                return baseWrapperValue(array, this.__actions__);
            }
            var result = [];

            outer:
                while (length-- && resIndex < takeCount) {
                    index += dir;

                    var iterIndex = -1,
                        value = array[index];

                    while (++iterIndex < iterLength) {
                        var data = iteratees[iterIndex],
                            iteratee = data.iteratee,
                            type = data.type,
                            computed = iteratee(value);

                        if (type == LAZY_MAP_FLAG) {
                            value = computed;
                        } else if (!computed) {
                            if (type == LAZY_FILTER_FLAG) {
                                continue outer;
                            } else {
                                break outer;
                            }
                        }
                    }
                    result[resIndex++] = value;
                }
            return result;
        }

        LazyWrapper.prototype = baseCreate(baseLodash.prototype);
        LazyWrapper.prototype.constructor = LazyWrapper;


        function Hash(entries) {
            var index = -1,
                length = entries == null ? 0 : entries.length;

            this.clear();
            while (++index < length) {
                var entry = entries[index];
                this.set(entry[0], entry[1]);
            }
        }

        function hashClear() {
            this.__data__ = nativeCreate ? nativeCreate(null) : {};
            this.size = 0;
        }

        function hashDelete(key) {
            var result = this.has(key) && delete this.__data__[key];
            this.size -= result ? 1 : 0;
            return result;
        }

        function hashGet(key) {
            var data = this.__data__;
            if (nativeCreate) {
                var result = data[key];
                return result === HASH_UNDEFINED ? undefined : result;
            }
            return hasOwnProperty.call(data, key) ? data[key] : undefined;
        }

        function hashHas(key) {
            var data = this.__data__;
            return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);
        }

        function hashSet(key, value) {
            var data = this.__data__;
            this.size += this.has(key) ? 0 : 1;
            data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;
            return this;
        }

        Hash.prototype.clear = hashClear;
        Hash.prototype['delete'] = hashDelete;
        Hash.prototype.get = hashGet;
        Hash.prototype.has = hashHas;
        Hash.prototype.set = hashSet;


        function ListCache(entries) {
            var index = -1,
                length = entries == null ? 0 : entries.length;

            this.clear();
            while (++index < length) {
                var entry = entries[index];
                this.set(entry[0], entry[1]);
            }
        }

        function listCacheClear() {
            this.__data__ = [];
            this.size = 0;
        }

        function listCacheDelete(key) {
            var data = this.__data__,
                index = assocIndexOf(data, key);

            if (index < 0) {
                return false;
            }
            var lastIndex = data.length - 1;
            if (index == lastIndex) {
                data.pop();
            } else {
                splice.call(data, index, 1);
            }
            --this.size;
            return true;
        }

        function listCacheGet(key) {
            var data = this.__data__,
                index = assocIndexOf(data, key);

            return index < 0 ? undefined : data[index][1];
        }

        function listCacheHas(key) {
            return assocIndexOf(this.__data__, key) > -1;
        }

        function listCacheSet(key, value) {
            var data = this.__data__,
                index = assocIndexOf(data, key);

            if (index < 0) {
                ++this.size;
                data.push([key, value]);
            } else {
                data[index][1] = value;
            }
            return this;
        }

        ListCache.prototype.clear = listCacheClear;
        ListCache.prototype['delete'] = listCacheDelete;
        ListCache.prototype.get = listCacheGet;
        ListCache.prototype.has = listCacheHas;
        ListCache.prototype.set = listCacheSet;


        function MapCache(entries) {
            var index = -1,
                length = entries == null ? 0 : entries.length;

            this.clear();
            while (++index < length) {
                var entry = entries[index];
                this.set(entry[0], entry[1]);
            }
        }

        function mapCacheClear() {
            this.size = 0;
            this.__data__ = {
                'hash': new Hash,
                'map': new (Map || ListCache),
                'string': new Hash
            };
        }

        function mapCacheDelete(key) {
            var result = getMapData(this, key)['delete'](key);
            this.size -= result ? 1 : 0;
            return result;
        }

        function mapCacheGet(key) {
            return getMapData(this, key).get(key);
        }

        function mapCacheHas(key) {
            return getMapData(this, key).has(key);
        }

        function mapCacheSet(key, value) {
            var data = getMapData(this, key),
                size = data.size;

            data.set(key, value);
            this.size += data.size == size ? 0 : 1;
            return this;
        }

        MapCache.prototype.clear = mapCacheClear;
        MapCache.prototype['delete'] = mapCacheDelete;
        MapCache.prototype.get = mapCacheGet;
        MapCache.prototype.has = mapCacheHas;
        MapCache.prototype.set = mapCacheSet;


        function SetCache(values) {
            var index = -1,
                length = values == null ? 0 : values.length;

            this.__data__ = new MapCache;
            while (++index < length) {
                this.add(values[index]);
            }
        }

        function setCacheAdd(value) {
            this.__data__.set(value, HASH_UNDEFINED);
            return this;
        }

        function setCacheHas(value) {
            return this.__data__.has(value);
        }

        SetCache.prototype.add = SetCache.prototype.push = setCacheAdd;
        SetCache.prototype.has = setCacheHas;


        function Stack(entries) {
            var data = this.__data__ = new ListCache(entries);
            this.size = data.size;
        }

        function stackClear() {
            this.__data__ = new ListCache;
            this.size = 0;
        }

        function stackDelete(key) {
            var data = this.__data__,
                result = data['delete'](key);

            this.size = data.size;
            return result;
        }

        function stackGet(key) {
            return this.__data__.get(key);
        }

        function stackHas(key) {
            return this.__data__.has(key);
        }

        function stackSet(key, value) {
            var data = this.__data__;
            if (data instanceof ListCache) {
                var pairs = data.__data__;
                if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {
                    pairs.push([key, value]);
                    this.size = ++data.size;
                    return this;
                }
                data = this.__data__ = new MapCache(pairs);
            }
            data.set(key, value);
            this.size = data.size;
            return this;
        }

        Stack.prototype.clear = stackClear;
        Stack.prototype['delete'] = stackDelete;
        Stack.prototype.get = stackGet;
        Stack.prototype.has = stackHas;
        Stack.prototype.set = stackSet;


        function arrayLikeKeys(value, inherited) {
            var isArr = isArray(value),
                isArg = !isArr && isArguments(value),
                isBuff = !isArr && !isArg && isBuffer(value),
                isType = !isArr && !isArg && !isBuff && isTypedArray(value),
                skipIndexes = isArr || isArg || isBuff || isType,
                result = skipIndexes ? baseTimes(value.length, String) : [],
                length = result.length;

            for (var key in value) {
                if ((inherited || hasOwnProperty.call(value, key)) &&
                    !(skipIndexes && (
                        key == 'length' ||
                        (isBuff && (key == 'offset' || key == 'parent')) ||
                        (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||
                        isIndex(key, length)
                    ))) {
                    result.push(key);
                }
            }
            return result;
        }

        function arraySample(array) {
            var length = array.length;
            return length ? array[baseRandom(0, length - 1)] : undefined;
        }

        function arraySampleSize(array, n) {
            return shuffleSelf(copyArray(array), baseClamp(n, 0, array.length));
        }

        function arrayShuffle(array) {
            return shuffleSelf(copyArray(array));
        }

        function assignMergeValue(object, key, value) {
            if ((value !== undefined && !eq(object[key], value)) ||
                (value === undefined && !(key in object))) {
                baseAssignValue(object, key, value);
            }
        }

        function assignValue(object, key, value) {
            var objValue = object[key];
            if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||
                (value === undefined && !(key in object))) {
                baseAssignValue(object, key, value);
            }
        }

        function assocIndexOf(array, key) {
            var length = array.length;
            while (length--) {
                if (eq(array[length][0], key)) {
                    return length;
                }
            }
            return -1;
        }

        function baseAggregator(collection, setter, iteratee, accumulator) {
            baseEach(collection, function(value, key, collection) {
                setter(accumulator, value, iteratee(value), collection);
            });
            return accumulator;
        }

        function baseAssign(object, source) {
            return object && copyObject(source, keys(source), object);
        }

        function baseAssignIn(object, source) {
            return object && copyObject(source, keysIn(source), object);
        }

        function baseAssignValue(object, key, value) {
            if (key == '__proto__' && defineProperty) {
                defineProperty(object, key, {
                    'configurable': true,
                    'enumerable': true,
                    'value': value,
                    'writable': true
                });
            } else {
                object[key] = value;
            }
        }

        function baseAt(object, paths) {
            var index = -1,
                length = paths.length,
                result = Array(length),
                skip = object == null;

            while (++index < length) {
                result[index] = skip ? undefined : get(object, paths[index]);
            }
            return result;
        }

        function baseClamp(number, lower, upper) {
            if (number === number) {
                if (upper !== undefined) {
                    number = number <= upper ? number : upper;
                }
                if (lower !== undefined) {
                    number = number >= lower ? number : lower;
                }
            }
            return number;
        }

        function baseClone(value, bitmask, customizer, key, object, stack) {
            var result,
                isDeep = bitmask & CLONE_DEEP_FLAG,
                isFlat = bitmask & CLONE_FLAT_FLAG,
                isFull = bitmask & CLONE_SYMBOLS_FLAG;

            if (customizer) {
                result = object ? customizer(value, key, object, stack) : customizer(value);
            }
            if (result !== undefined) {
                return result;
            }
            if (!isObject(value)) {
                return value;
            }
            var isArr = isArray(value);
            if (isArr) {
                result = initCloneArray(value);
                if (!isDeep) {
                    return copyArray(value, result);
                }
            } else {
                var tag = getTag(value),
                    isFunc = tag == funcTag || tag == genTag;

                if (isBuffer(value)) {
                    return cloneBuffer(value, isDeep);
                }
                if (tag == objectTag || tag == argsTag || (isFunc && !object)) {
                    result = (isFlat || isFunc) ? {} : initCloneObject(value);
                    if (!isDeep) {
                        return isFlat
                            ? copySymbolsIn(value, baseAssignIn(result, value))
                            : copySymbols(value, baseAssign(result, value));
                    }
                } else {
                    if (!cloneableTags[tag]) {
                        return object ? value : {};
                    }
                    result = initCloneByTag(value, tag, baseClone, isDeep);
                }
            }
            stack || (stack = new Stack);
            var stacked = stack.get(value);
            if (stacked) {
                return stacked;
            }
            stack.set(value, result);

            var keysFunc = isFull
                ? (isFlat ? getAllKeysIn : getAllKeys)
                : (isFlat ? keysIn : keys);

            var props = isArr ? undefined : keysFunc(value);
            arrayEach(props || value, function(subValue, key) {
                if (props) {
                    key = subValue;
                    subValue = value[key];
                }
                assignValue(result, key, baseClone(subValue, bitmask, customizer, key, value, stack));
            });
            return result;
        }

        function baseConforms(source) {
            var props = keys(source);
            return function(object) {
                return baseConformsTo(object, source, props);
            };
        }

        function baseConformsTo(object, source, props) {
            var length = props.length;
            if (object == null) {
                return !length;
            }
            object = Object(object);
            while (length--) {
                var key = props[length],
                    predicate = source[key],
                    value = object[key];

                if ((value === undefined && !(key in object)) || !predicate(value)) {
                    return false;
                }
            }
            return true;
        }

        function baseDelay(func, wait, args) {
            if (typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            return setTimeout(function() { func.apply(undefined, args); }, wait);
        }

        function baseDifference(array, values, iteratee, comparator) {
            var index = -1,
                includes = arrayIncludes,
                isCommon = true,
                length = array.length,
                result = [],
                valuesLength = values.length;

            if (!length) {
                return result;
            }
            if (iteratee) {
                values = arrayMap(values, baseUnary(iteratee));
            }
            if (comparator) {
                includes = arrayIncludesWith;
                isCommon = false;
            }
            else if (values.length >= LARGE_ARRAY_SIZE) {
                includes = cacheHas;
                isCommon = false;
                values = new SetCache(values);
            }
            outer:
                while (++index < length) {
                    var value = array[index],
                        computed = iteratee == null ? value : iteratee(value);

                    value = (comparator || value !== 0) ? value : 0;
                    if (isCommon && computed === computed) {
                        var valuesIndex = valuesLength;
                        while (valuesIndex--) {
                            if (values[valuesIndex] === computed) {
                                continue outer;
                            }
                        }
                        result.push(value);
                    }
                    else if (!includes(values, computed, comparator)) {
                        result.push(value);
                    }
                }
            return result;
        }

        var baseEach = createBaseEach(baseForOwn);

        var baseEachRight = createBaseEach(baseForOwnRight, true);

        function baseEvery(collection, predicate) {
            var result = true;
            baseEach(collection, function(value, index, collection) {
                result = !!predicate(value, index, collection);
                return result;
            });
            return result;
        }

        function baseExtremum(array, iteratee, comparator) {
            var index = -1,
                length = array.length;

            while (++index < length) {
                var value = array[index],
                    current = iteratee(value);

                if (current != null && (computed === undefined
                            ? (current === current && !isSymbol(current))
                            : comparator(current, computed)
                    )) {
                    var computed = current,
                        result = value;
                }
            }
            return result;
        }

        function baseFill(array, value, start, end) {
            var length = array.length;

            start = toInteger(start);
            if (start < 0) {
                start = -start > length ? 0 : (length + start);
            }
            end = (end === undefined || end > length) ? length : toInteger(end);
            if (end < 0) {
                end += length;
            }
            end = start > end ? 0 : toLength(end);
            while (start < end) {
                array[start++] = value;
            }
            return array;
        }

        function baseFilter(collection, predicate) {
            var result = [];
            baseEach(collection, function(value, index, collection) {
                if (predicate(value, index, collection)) {
                    result.push(value);
                }
            });
            return result;
        }

        function baseFlatten(array, depth, predicate, isStrict, result) {
            var index = -1,
                length = array.length;

            predicate || (predicate = isFlattenable);
            result || (result = []);

            while (++index < length) {
                var value = array[index];
                if (depth > 0 && predicate(value)) {
                    if (depth > 1) {
                        baseFlatten(value, depth - 1, predicate, isStrict, result);
                    } else {
                        arrayPush(result, value);
                    }
                } else if (!isStrict) {
                    result[result.length] = value;
                }
            }
            return result;
        }

        var baseFor = createBaseFor();

        var baseForRight = createBaseFor(true);

        function baseForOwn(object, iteratee) {
            return object && baseFor(object, iteratee, keys);
        }

        function baseForOwnRight(object, iteratee) {
            return object && baseForRight(object, iteratee, keys);
        }

        function baseFunctions(object, props) {
            return arrayFilter(props, function(key) {
                return isFunction(object[key]);
            });
        }

        function baseGet(object, path) {
            path = castPath(path, object);

            var index = 0,
                length = path.length;

            while (object != null && index < length) {
                object = object[toKey(path[index++])];
            }
            return (index && index == length) ? object : undefined;
        }

        function baseGetAllKeys(object, keysFunc, symbolsFunc) {
            var result = keysFunc(object);
            return isArray(object) ? result : arrayPush(result, symbolsFunc(object));
        }

        function baseGetTag(value) {
            if (value == null) {
                return value === undefined ? undefinedTag : nullTag;
            }
            return (symToStringTag && symToStringTag in Object(value))
                ? getRawTag(value)
                : objectToString(value);
        }

        function baseGt(value, other) {
            return value > other;
        }

        function baseHas(object, key) {
            return object != null && hasOwnProperty.call(object, key);
        }

        function baseHasIn(object, key) {
            return object != null && key in Object(object);
        }

        function baseInRange(number, start, end) {
            return number >= nativeMin(start, end) && number < nativeMax(start, end);
        }

        function baseIntersection(arrays, iteratee, comparator) {
            var includes = comparator ? arrayIncludesWith : arrayIncludes,
                length = arrays[0].length,
                othLength = arrays.length,
                othIndex = othLength,
                caches = Array(othLength),
                maxLength = Infinity,
                result = [];

            while (othIndex--) {
                var array = arrays[othIndex];
                if (othIndex && iteratee) {
                    array = arrayMap(array, baseUnary(iteratee));
                }
                maxLength = nativeMin(array.length, maxLength);
                caches[othIndex] = !comparator && (iteratee || (length >= 120 && array.length >= 120))
                    ? new SetCache(othIndex && array)
                    : undefined;
            }
            array = arrays[0];

            var index = -1,
                seen = caches[0];

            outer:
                while (++index < length && result.length < maxLength) {
                    var value = array[index],
                        computed = iteratee ? iteratee(value) : value;

                    value = (comparator || value !== 0) ? value : 0;
                    if (!(seen
                                ? cacheHas(seen, computed)
                                : includes(result, computed, comparator)
                        )) {
                        othIndex = othLength;
                        while (--othIndex) {
                            var cache = caches[othIndex];
                            if (!(cache
                                    ? cacheHas(cache, computed)
                                    : includes(arrays[othIndex], computed, comparator))
                            ) {
                                continue outer;
                            }
                        }
                        if (seen) {
                            seen.push(computed);
                        }
                        result.push(value);
                    }
                }
            return result;
        }

        function baseInverter(object, setter, iteratee, accumulator) {
            baseForOwn(object, function(value, key, object) {
                setter(accumulator, iteratee(value), key, object);
            });
            return accumulator;
        }

        function baseInvoke(object, path, args) {
            path = castPath(path, object);
            object = parent(object, path);
            var func = object == null ? object : object[toKey(last(path))];
            return func == null ? undefined : apply(func, object, args);
        }

        function baseIsArguments(value) {
            return isObjectLike(value) && baseGetTag(value) == argsTag;
        }

        function baseIsArrayBuffer(value) {
            return isObjectLike(value) && baseGetTag(value) == arrayBufferTag;
        }

        function baseIsDate(value) {
            return isObjectLike(value) && baseGetTag(value) == dateTag;
        }

        function baseIsEqual(value, other, bitmask, customizer, stack) {
            if (value === other) {
                return true;
            }
            if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {
                return value !== value && other !== other;
            }
            return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);
        }

        function baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {
            var objIsArr = isArray(object),
                othIsArr = isArray(other),
                objTag = objIsArr ? arrayTag : getTag(object),
                othTag = othIsArr ? arrayTag : getTag(other);

            objTag = objTag == argsTag ? objectTag : objTag;
            othTag = othTag == argsTag ? objectTag : othTag;

            var objIsObj = objTag == objectTag,
                othIsObj = othTag == objectTag,
                isSameTag = objTag == othTag;

            if (isSameTag && isBuffer(object)) {
                if (!isBuffer(other)) {
                    return false;
                }
                objIsArr = true;
                objIsObj = false;
            }
            if (isSameTag && !objIsObj) {
                stack || (stack = new Stack);
                return (objIsArr || isTypedArray(object))
                    ? equalArrays(object, other, bitmask, customizer, equalFunc, stack)
                    : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);
            }
            if (!(bitmask & COMPARE_PARTIAL_FLAG)) {
                var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),
                    othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');

                if (objIsWrapped || othIsWrapped) {
                    var objUnwrapped = objIsWrapped ? object.value() : object,
                        othUnwrapped = othIsWrapped ? other.value() : other;

                    stack || (stack = new Stack);
                    return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);
                }
            }
            if (!isSameTag) {
                return false;
            }
            stack || (stack = new Stack);
            return equalObjects(object, other, bitmask, customizer, equalFunc, stack);
        }

        function baseIsMap(value) {
            return isObjectLike(value) && getTag(value) == mapTag;
        }

        function baseIsMatch(object, source, matchData, customizer) {
            var index = matchData.length,
                length = index,
                noCustomizer = !customizer;

            if (object == null) {
                return !length;
            }
            object = Object(object);
            while (index--) {
                var data = matchData[index];
                if ((noCustomizer && data[2])
                        ? data[1] !== object[data[0]]
                        : !(data[0] in object)
                ) {
                    return false;
                }
            }
            while (++index < length) {
                data = matchData[index];
                var key = data[0],
                    objValue = object[key],
                    srcValue = data[1];

                if (noCustomizer && data[2]) {
                    if (objValue === undefined && !(key in object)) {
                        return false;
                    }
                } else {
                    var stack = new Stack;
                    if (customizer) {
                        var result = customizer(objValue, srcValue, key, object, source, stack);
                    }
                    if (!(result === undefined
                                ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack)
                                : result
                        )) {
                        return false;
                    }
                }
            }
            return true;
        }

        function baseIsNative(value) {
            if (!isObject(value) || isMasked(value)) {
                return false;
            }
            var pattern = isFunction(value) ? reIsNative : reIsHostCtor;
            return pattern.test(toSource(value));
        }

        function baseIsRegExp(value) {
            return isObjectLike(value) && baseGetTag(value) == regexpTag;
        }

        function baseIsSet(value) {
            return isObjectLike(value) && getTag(value) == setTag;
        }

        function baseIsTypedArray(value) {
            return isObjectLike(value) &&
                isLength(value.length) && !!typedArrayTags[baseGetTag(value)];
        }

        function baseIteratee(value) {
            if (typeof value == 'function') {
                return value;
            }
            if (value == null) {
                return identity;
            }
            if (typeof value == 'object') {
                return isArray(value)
                    ? baseMatchesProperty(value[0], value[1])
                    : baseMatches(value);
            }
            return property(value);
        }

        function baseKeys(object) {
            if (!isPrototype(object)) {
                return nativeKeys(object);
            }
            var result = [];
            for (var key in Object(object)) {
                if (hasOwnProperty.call(object, key) && key != 'constructor') {
                    result.push(key);
                }
            }
            return result;
        }

        function baseKeysIn(object) {
            if (!isObject(object)) {
                return nativeKeysIn(object);
            }
            var isProto = isPrototype(object),
                result = [];

            for (var key in object) {
                if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {
                    result.push(key);
                }
            }
            return result;
        }

        function baseLt(value, other) {
            return value < other;
        }

        function baseMap(collection, iteratee) {
            var index = -1,
                result = isArrayLike(collection) ? Array(collection.length) : [];

            baseEach(collection, function(value, key, collection) {
                result[++index] = iteratee(value, key, collection);
            });
            return result;
        }

        function baseMatches(source) {
            var matchData = getMatchData(source);
            if (matchData.length == 1 && matchData[0][2]) {
                return matchesStrictComparable(matchData[0][0], matchData[0][1]);
            }
            return function(object) {
                return object === source || baseIsMatch(object, source, matchData);
            };
        }

        function baseMatchesProperty(path, srcValue) {
            if (isKey(path) && isStrictComparable(srcValue)) {
                return matchesStrictComparable(toKey(path), srcValue);
            }
            return function(object) {
                var objValue = get(object, path);
                return (objValue === undefined && objValue === srcValue)
                    ? hasIn(object, path)
                    : baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG);
            };
        }

        function baseMerge(object, source, srcIndex, customizer, stack) {
            if (object === source) {
                return;
            }
            baseFor(source, function(srcValue, key) {
                if (isObject(srcValue)) {
                    stack || (stack = new Stack);
                    baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);
                }
                else {
                    var newValue = customizer
                        ? customizer(object[key], srcValue, (key + ''), object, source, stack)
                        : undefined;

                    if (newValue === undefined) {
                        newValue = srcValue;
                    }
                    assignMergeValue(object, key, newValue);
                }
            }, keysIn);
        }

        function baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {
            var objValue = object[key],
                srcValue = source[key],
                stacked = stack.get(srcValue);

            if (stacked) {
                assignMergeValue(object, key, stacked);
                return;
            }
            var newValue = customizer
                ? customizer(objValue, srcValue, (key + ''), object, source, stack)
                : undefined;

            var isCommon = newValue === undefined;

            if (isCommon) {
                var isArr = isArray(srcValue),
                    isBuff = !isArr && isBuffer(srcValue),
                    isTyped = !isArr && !isBuff && isTypedArray(srcValue);

                newValue = srcValue;
                if (isArr || isBuff || isTyped) {
                    if (isArray(objValue)) {
                        newValue = objValue;
                    }
                    else if (isArrayLikeObject(objValue)) {
                        newValue = copyArray(objValue);
                    }
                    else if (isBuff) {
                        isCommon = false;
                        newValue = cloneBuffer(srcValue, true);
                    }
                    else if (isTyped) {
                        isCommon = false;
                        newValue = cloneTypedArray(srcValue, true);
                    }
                    else {
                        newValue = [];
                    }
                }
                else if (isPlainObject(srcValue) || isArguments(srcValue)) {
                    newValue = objValue;
                    if (isArguments(objValue)) {
                        newValue = toPlainObject(objValue);
                    }
                    else if (!isObject(objValue) || (srcIndex && isFunction(objValue))) {
                        newValue = initCloneObject(srcValue);
                    }
                }
                else {
                    isCommon = false;
                }
            }
            if (isCommon) {
                stack.set(srcValue, newValue);
                mergeFunc(newValue, srcValue, srcIndex, customizer, stack);
                stack['delete'](srcValue);
            }
            assignMergeValue(object, key, newValue);
        }

        function baseNth(array, n) {
            var length = array.length;
            if (!length) {
                return;
            }
            n += n < 0 ? length : 0;
            return isIndex(n, length) ? array[n] : undefined;
        }

        function baseOrderBy(collection, iteratees, orders) {
            var index = -1;
            iteratees = arrayMap(iteratees.length ? iteratees : [identity], baseUnary(getIteratee()));

            var result = baseMap(collection, function(value, key, collection) {
                var criteria = arrayMap(iteratees, function(iteratee) {
                    return iteratee(value);
                });
                return { 'criteria': criteria, 'index': ++index, 'value': value };
            });

            return baseSortBy(result, function(object, other) {
                return compareMultiple(object, other, orders);
            });
        }

        function basePick(object, paths) {
            return basePickBy(object, paths, function(value, path) {
                return hasIn(object, path);
            });
        }

        function basePickBy(object, paths, predicate) {
            var index = -1,
                length = paths.length,
                result = {};

            while (++index < length) {
                var path = paths[index],
                    value = baseGet(object, path);

                if (predicate(value, path)) {
                    baseSet(result, castPath(path, object), value);
                }
            }
            return result;
        }

        function basePropertyDeep(path) {
            return function(object) {
                return baseGet(object, path);
            };
        }

        function basePullAll(array, values, iteratee, comparator) {
            var indexOf = comparator ? baseIndexOfWith : baseIndexOf,
                index = -1,
                length = values.length,
                seen = array;

            if (array === values) {
                values = copyArray(values);
            }
            if (iteratee) {
                seen = arrayMap(array, baseUnary(iteratee));
            }
            while (++index < length) {
                var fromIndex = 0,
                    value = values[index],
                    computed = iteratee ? iteratee(value) : value;

                while ((fromIndex = indexOf(seen, computed, fromIndex, comparator)) > -1) {
                    if (seen !== array) {
                        splice.call(seen, fromIndex, 1);
                    }
                    splice.call(array, fromIndex, 1);
                }
            }
            return array;
        }

        function basePullAt(array, indexes) {
            var length = array ? indexes.length : 0,
                lastIndex = length - 1;

            while (length--) {
                var index = indexes[length];
                if (length == lastIndex || index !== previous) {
                    var previous = index;
                    if (isIndex(index)) {
                        splice.call(array, index, 1);
                    } else {
                        baseUnset(array, index);
                    }
                }
            }
            return array;
        }

        function baseRandom(lower, upper) {
            return lower + nativeFloor(nativeRandom() * (upper - lower + 1));
        }

        function baseRange(start, end, step, fromRight) {
            var index = -1,
                length = nativeMax(nativeCeil((end - start) / (step || 1)), 0),
                result = Array(length);

            while (length--) {
                result[fromRight ? length : ++index] = start;
                start += step;
            }
            return result;
        }

        function baseRepeat(string, n) {
            var result = '';
            if (!string || n < 1 || n > MAX_SAFE_INTEGER) {
                return result;
            }
            do {
                if (n % 2) {
                    result += string;
                }
                n = nativeFloor(n / 2);
                if (n) {
                    string += string;
                }
            } while (n);

            return result;
        }

        function baseRest(func, start) {
            return setToString(overRest(func, start, identity), func + '');
        }

        function baseSample(collection) {
            return arraySample(values(collection));
        }

        function baseSampleSize(collection, n) {
            var array = values(collection);
            return shuffleSelf(array, baseClamp(n, 0, array.length));
        }

        function baseSet(object, path, value, customizer) {
            if (!isObject(object)) {
                return object;
            }
            path = castPath(path, object);

            var index = -1,
                length = path.length,
                lastIndex = length - 1,
                nested = object;

            while (nested != null && ++index < length) {
                var key = toKey(path[index]),
                    newValue = value;

                if (index != lastIndex) {
                    var objValue = nested[key];
                    newValue = customizer ? customizer(objValue, key, nested) : undefined;
                    if (newValue === undefined) {
                        newValue = isObject(objValue)
                            ? objValue
                            : (isIndex(path[index + 1]) ? [] : {});
                    }
                }
                assignValue(nested, key, newValue);
                nested = nested[key];
            }
            return object;
        }

        var baseSetData = !metaMap ? identity : function(func, data) {
                metaMap.set(func, data);
                return func;
            };

        var baseSetToString = !defineProperty ? identity : function(func, string) {
                return defineProperty(func, 'toString', {
                    'configurable': true,
                    'enumerable': false,
                    'value': constant(string),
                    'writable': true
                });
            };

        function baseShuffle(collection) {
            return shuffleSelf(values(collection));
        }

        function baseSlice(array, start, end) {
            var index = -1,
                length = array.length;

            if (start < 0) {
                start = -start > length ? 0 : (length + start);
            }
            end = end > length ? length : end;
            if (end < 0) {
                end += length;
            }
            length = start > end ? 0 : ((end - start) >>> 0);
            start >>>= 0;

            var result = Array(length);
            while (++index < length) {
                result[index] = array[index + start];
            }
            return result;
        }

        function baseSome(collection, predicate) {
            var result;

            baseEach(collection, function(value, index, collection) {
                result = predicate(value, index, collection);
                return !result;
            });
            return !!result;
        }

        function baseSortedIndex(array, value, retHighest) {
            var low = 0,
                high = array == null ? low : array.length;

            if (typeof value == 'number' && value === value && high <= HALF_MAX_ARRAY_LENGTH) {
                while (low < high) {
                    var mid = (low + high) >>> 1,
                        computed = array[mid];

                    if (computed !== null && !isSymbol(computed) &&
                        (retHighest ? (computed <= value) : (computed < value))) {
                        low = mid + 1;
                    } else {
                        high = mid;
                    }
                }
                return high;
            }
            return baseSortedIndexBy(array, value, identity, retHighest);
        }

        function baseSortedIndexBy(array, value, iteratee, retHighest) {
            value = iteratee(value);

            var low = 0,
                high = array == null ? 0 : array.length,
                valIsNaN = value !== value,
                valIsNull = value === null,
                valIsSymbol = isSymbol(value),
                valIsUndefined = value === undefined;

            while (low < high) {
                var mid = nativeFloor((low + high) / 2),
                    computed = iteratee(array[mid]),
                    othIsDefined = computed !== undefined,
                    othIsNull = computed === null,
                    othIsReflexive = computed === computed,
                    othIsSymbol = isSymbol(computed);

                if (valIsNaN) {
                    var setLow = retHighest || othIsReflexive;
                } else if (valIsUndefined) {
                    setLow = othIsReflexive && (retHighest || othIsDefined);
                } else if (valIsNull) {
                    setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);
                } else if (valIsSymbol) {
                    setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);
                } else if (othIsNull || othIsSymbol) {
                    setLow = false;
                } else {
                    setLow = retHighest ? (computed <= value) : (computed < value);
                }
                if (setLow) {
                    low = mid + 1;
                } else {
                    high = mid;
                }
            }
            return nativeMin(high, MAX_ARRAY_INDEX);
        }

        function baseSortedUniq(array, iteratee) {
            var index = -1,
                length = array.length,
                resIndex = 0,
                result = [];

            while (++index < length) {
                var value = array[index],
                    computed = iteratee ? iteratee(value) : value;

                if (!index || !eq(computed, seen)) {
                    var seen = computed;
                    result[resIndex++] = value === 0 ? 0 : value;
                }
            }
            return result;
        }

        function baseToNumber(value) {
            if (typeof value == 'number') {
                return value;
            }
            if (isSymbol(value)) {
                return NAN;
            }
            return +value;
        }

        function baseToString(value) {
            if (typeof value == 'string') {
                return value;
            }
            if (isArray(value)) {
                return arrayMap(value, baseToString) + '';
            }
            if (isSymbol(value)) {
                return symbolToString ? symbolToString.call(value) : '';
            }
            var result = (value + '');
            return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;
        }

        function baseUniq(array, iteratee, comparator) {
            var index = -1,
                includes = arrayIncludes,
                length = array.length,
                isCommon = true,
                result = [],
                seen = result;

            if (comparator) {
                isCommon = false;
                includes = arrayIncludesWith;
            }
            else if (length >= LARGE_ARRAY_SIZE) {
                var set = iteratee ? null : createSet(array);
                if (set) {
                    return setToArray(set);
                }
                isCommon = false;
                includes = cacheHas;
                seen = new SetCache;
            }
            else {
                seen = iteratee ? [] : result;
            }
            outer:
                while (++index < length) {
                    var value = array[index],
                        computed = iteratee ? iteratee(value) : value;

                    value = (comparator || value !== 0) ? value : 0;
                    if (isCommon && computed === computed) {
                        var seenIndex = seen.length;
                        while (seenIndex--) {
                            if (seen[seenIndex] === computed) {
                                continue outer;
                            }
                        }
                        if (iteratee) {
                            seen.push(computed);
                        }
                        result.push(value);
                    }
                    else if (!includes(seen, computed, comparator)) {
                        if (seen !== result) {
                            seen.push(computed);
                        }
                        result.push(value);
                    }
                }
            return result;
        }

        function baseUnset(object, path) {
            path = castPath(path, object);
            object = parent(object, path);
            return object == null || delete object[toKey(last(path))];
        }

        function baseUpdate(object, path, updater, customizer) {
            return baseSet(object, path, updater(baseGet(object, path)), customizer);
        }

        function baseWhile(array, predicate, isDrop, fromRight) {
            var length = array.length,
                index = fromRight ? length : -1;

            while ((fromRight ? index-- : ++index < length) &&
            predicate(array[index], index, array)) {}

            return isDrop
                ? baseSlice(array, (fromRight ? 0 : index), (fromRight ? index + 1 : length))
                : baseSlice(array, (fromRight ? index + 1 : 0), (fromRight ? length : index));
        }

        function baseWrapperValue(value, actions) {
            var result = value;
            if (result instanceof LazyWrapper) {
                result = result.value();
            }
            return arrayReduce(actions, function(result, action) {
                return action.func.apply(action.thisArg, arrayPush([result], action.args));
            }, result);
        }

        function baseXor(arrays, iteratee, comparator) {
            var length = arrays.length;
            if (length < 2) {
                return length ? baseUniq(arrays[0]) : [];
            }
            var index = -1,
                result = Array(length);

            while (++index < length) {
                var array = arrays[index],
                    othIndex = -1;

                while (++othIndex < length) {
                    if (othIndex != index) {
                        result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);
                    }
                }
            }
            return baseUniq(baseFlatten(result, 1), iteratee, comparator);
        }

        function baseZipObject(props, values, assignFunc) {
            var index = -1,
                length = props.length,
                valsLength = values.length,
                result = {};

            while (++index < length) {
                var value = index < valsLength ? values[index] : undefined;
                assignFunc(result, props[index], value);
            }
            return result;
        }

        function castArrayLikeObject(value) {
            return isArrayLikeObject(value) ? value : [];
        }

        function castFunction(value) {
            return typeof value == 'function' ? value : identity;
        }

        function castPath(value, object) {
            if (isArray(value)) {
                return value;
            }
            return isKey(value, object) ? [value] : stringToPath(toString(value));
        }

        var castRest = baseRest;

        function castSlice(array, start, end) {
            var length = array.length;
            end = end === undefined ? length : end;
            return (!start && end >= length) ? array : baseSlice(array, start, end);
        }

        var clearTimeout = ctxClearTimeout || function(id) {
                return root.clearTimeout(id);
            };

        function cloneBuffer(buffer, isDeep) {
            if (isDeep) {
                return buffer.slice();
            }
            var length = buffer.length,
                result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);

            buffer.copy(result);
            return result;
        }

        function cloneArrayBuffer(arrayBuffer) {
            var result = new arrayBuffer.constructor(arrayBuffer.byteLength);
            new Uint8Array(result).set(new Uint8Array(arrayBuffer));
            return result;
        }

        function cloneDataView(dataView, isDeep) {
            var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;
            return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);
        }

        function cloneMap(map, isDeep, cloneFunc) {
            var array = isDeep ? cloneFunc(mapToArray(map), CLONE_DEEP_FLAG) : mapToArray(map);
            return arrayReduce(array, addMapEntry, new map.constructor);
        }

        function cloneRegExp(regexp) {
            var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));
            result.lastIndex = regexp.lastIndex;
            return result;
        }

        function cloneSet(set, isDeep, cloneFunc) {
            var array = isDeep ? cloneFunc(setToArray(set), CLONE_DEEP_FLAG) : setToArray(set);
            return arrayReduce(array, addSetEntry, new set.constructor);
        }

        function cloneSymbol(symbol) {
            return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};
        }

        function cloneTypedArray(typedArray, isDeep) {
            var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;
            return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
        }

        function compareAscending(value, other) {
            if (value !== other) {
                var valIsDefined = value !== undefined,
                    valIsNull = value === null,
                    valIsReflexive = value === value,
                    valIsSymbol = isSymbol(value);

                var othIsDefined = other !== undefined,
                    othIsNull = other === null,
                    othIsReflexive = other === other,
                    othIsSymbol = isSymbol(other);

                if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||
                    (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||
                    (valIsNull && othIsDefined && othIsReflexive) ||
                    (!valIsDefined && othIsReflexive) ||
                    !valIsReflexive) {
                    return 1;
                }
                if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||
                    (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||
                    (othIsNull && valIsDefined && valIsReflexive) ||
                    (!othIsDefined && valIsReflexive) ||
                    !othIsReflexive) {
                    return -1;
                }
            }
            return 0;
        }

        function compareMultiple(object, other, orders) {
            var index = -1,
                objCriteria = object.criteria,
                othCriteria = other.criteria,
                length = objCriteria.length,
                ordersLength = orders.length;

            while (++index < length) {
                var result = compareAscending(objCriteria[index], othCriteria[index]);
                if (result) {
                    if (index >= ordersLength) {
                        return result;
                    }
                    var order = orders[index];
                    return result * (order == 'desc' ? -1 : 1);
                }
            }
            return object.index - other.index;
        }

        function composeArgs(args, partials, holders, isCurried) {
            var argsIndex = -1,
                argsLength = args.length,
                holdersLength = holders.length,
                leftIndex = -1,
                leftLength = partials.length,
                rangeLength = nativeMax(argsLength - holdersLength, 0),
                result = Array(leftLength + rangeLength),
                isUncurried = !isCurried;

            while (++leftIndex < leftLength) {
                result[leftIndex] = partials[leftIndex];
            }
            while (++argsIndex < holdersLength) {
                if (isUncurried || argsIndex < argsLength) {
                    result[holders[argsIndex]] = args[argsIndex];
                }
            }
            while (rangeLength--) {
                result[leftIndex++] = args[argsIndex++];
            }
            return result;
        }

        function composeArgsRight(args, partials, holders, isCurried) {
            var argsIndex = -1,
                argsLength = args.length,
                holdersIndex = -1,
                holdersLength = holders.length,
                rightIndex = -1,
                rightLength = partials.length,
                rangeLength = nativeMax(argsLength - holdersLength, 0),
                result = Array(rangeLength + rightLength),
                isUncurried = !isCurried;

            while (++argsIndex < rangeLength) {
                result[argsIndex] = args[argsIndex];
            }
            var offset = argsIndex;
            while (++rightIndex < rightLength) {
                result[offset + rightIndex] = partials[rightIndex];
            }
            while (++holdersIndex < holdersLength) {
                if (isUncurried || argsIndex < argsLength) {
                    result[offset + holders[holdersIndex]] = args[argsIndex++];
                }
            }
            return result;
        }

        function copyArray(source, array) {
            var index = -1,
                length = source.length;

            array || (array = Array(length));
            while (++index < length) {
                array[index] = source[index];
            }
            return array;
        }

        function copyObject(source, props, object, customizer) {
            var isNew = !object;
            object || (object = {});

            var index = -1,
                length = props.length;

            while (++index < length) {
                var key = props[index];

                var newValue = customizer
                    ? customizer(object[key], source[key], key, object, source)
                    : undefined;

                if (newValue === undefined) {
                    newValue = source[key];
                }
                if (isNew) {
                    baseAssignValue(object, key, newValue);
                } else {
                    assignValue(object, key, newValue);
                }
            }
            return object;
        }

        function copySymbols(source, object) {
            return copyObject(source, getSymbols(source), object);
        }

        function copySymbolsIn(source, object) {
            return copyObject(source, getSymbolsIn(source), object);
        }

        function createAggregator(setter, initializer) {
            return function(collection, iteratee) {
                var func = isArray(collection) ? arrayAggregator : baseAggregator,
                    accumulator = initializer ? initializer() : {};

                return func(collection, setter, getIteratee(iteratee, 2), accumulator);
            };
        }

        function createAssigner(assigner) {
            return baseRest(function(object, sources) {
                var index = -1,
                    length = sources.length,
                    customizer = length > 1 ? sources[length - 1] : undefined,
                    guard = length > 2 ? sources[2] : undefined;

                customizer = (assigner.length > 3 && typeof customizer == 'function')
                    ? (length--, customizer)
                    : undefined;

                if (guard && isIterateeCall(sources[0], sources[1], guard)) {
                    customizer = length < 3 ? undefined : customizer;
                    length = 1;
                }
                object = Object(object);
                while (++index < length) {
                    var source = sources[index];
                    if (source) {
                        assigner(object, source, index, customizer);
                    }
                }
                return object;
            });
        }

        function createBaseEach(eachFunc, fromRight) {
            return function(collection, iteratee) {
                if (collection == null) {
                    return collection;
                }
                if (!isArrayLike(collection)) {
                    return eachFunc(collection, iteratee);
                }
                var length = collection.length,
                    index = fromRight ? length : -1,
                    iterable = Object(collection);

                while ((fromRight ? index-- : ++index < length)) {
                    if (iteratee(iterable[index], index, iterable) === false) {
                        break;
                    }
                }
                return collection;
            };
        }

        function createBaseFor(fromRight) {
            return function(object, iteratee, keysFunc) {
                var index = -1,
                    iterable = Object(object),
                    props = keysFunc(object),
                    length = props.length;

                while (length--) {
                    var key = props[fromRight ? length : ++index];
                    if (iteratee(iterable[key], key, iterable) === false) {
                        break;
                    }
                }
                return object;
            };
        }

        function createBind(func, bitmask, thisArg) {
            var isBind = bitmask & WRAP_BIND_FLAG,
                Ctor = createCtor(func);

            function wrapper() {
                var fn = (this && this !== root && this instanceof wrapper) ? Ctor : func;
                return fn.apply(isBind ? thisArg : this, arguments);
            }
            return wrapper;
        }

        function createCaseFirst(methodName) {
            return function(string) {
                string = toString(string);

                var strSymbols = hasUnicode(string)
                    ? stringToArray(string)
                    : undefined;

                var chr = strSymbols
                    ? strSymbols[0]
                    : string.charAt(0);

                var trailing = strSymbols
                    ? castSlice(strSymbols, 1).join('')
                    : string.slice(1);

                return chr[methodName]() + trailing;
            };
        }

        function createCompounder(callback) {
            return function(string) {
                return arrayReduce(words(deburr(string).replace(reApos, '')), callback, '');
            };
        }

        function createCtor(Ctor) {
            return function() {
                var args = arguments;
                switch (args.length) {
                    case 0: return new Ctor;
                    case 1: return new Ctor(args[0]);
                    case 2: return new Ctor(args[0], args[1]);
                    case 3: return new Ctor(args[0], args[1], args[2]);
                    case 4: return new Ctor(args[0], args[1], args[2], args[3]);
                    case 5: return new Ctor(args[0], args[1], args[2], args[3], args[4]);
                    case 6: return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5]);
                    case 7: return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5], args[6]);
                }
                var thisBinding = baseCreate(Ctor.prototype),
                    result = Ctor.apply(thisBinding, args);

                return isObject(result) ? result : thisBinding;
            };
        }

        function createCurry(func, bitmask, arity) {
            var Ctor = createCtor(func);

            function wrapper() {
                var length = arguments.length,
                    args = Array(length),
                    index = length,
                    placeholder = getHolder(wrapper);

                while (index--) {
                    args[index] = arguments[index];
                }
                var holders = (length < 3 && args[0] !== placeholder && args[length - 1] !== placeholder)
                    ? []
                    : replaceHolders(args, placeholder);

                length -= holders.length;
                if (length < arity) {
                    return createRecurry(
                        func, bitmask, createHybrid, wrapper.placeholder, undefined,
                        args, holders, undefined, undefined, arity - length);
                }
                var fn = (this && this !== root && this instanceof wrapper) ? Ctor : func;
                return apply(fn, this, args);
            }
            return wrapper;
        }

        function createFind(findIndexFunc) {
            return function(collection, predicate, fromIndex) {
                var iterable = Object(collection);
                if (!isArrayLike(collection)) {
                    var iteratee = getIteratee(predicate, 3);
                    collection = keys(collection);
                    predicate = function(key) { return iteratee(iterable[key], key, iterable); };
                }
                var index = findIndexFunc(collection, predicate, fromIndex);
                return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;
            };
        }

        function createFlow(fromRight) {
            return flatRest(function(funcs) {
                var length = funcs.length,
                    index = length,
                    prereq = LodashWrapper.prototype.thru;

                if (fromRight) {
                    funcs.reverse();
                }
                while (index--) {
                    var func = funcs[index];
                    if (typeof func != 'function') {
                        throw new TypeError(FUNC_ERROR_TEXT);
                    }
                    if (prereq && !wrapper && getFuncName(func) == 'wrapper') {
                        var wrapper = new LodashWrapper([], true);
                    }
                }
                index = wrapper ? index : length;
                while (++index < length) {
                    func = funcs[index];

                    var funcName = getFuncName(func),
                        data = funcName == 'wrapper' ? getData(func) : undefined;

                    if (data && isLaziable(data[0]) &&
                        data[1] == (WRAP_ARY_FLAG | WRAP_CURRY_FLAG | WRAP_PARTIAL_FLAG | WRAP_REARG_FLAG) &&
                        !data[4].length && data[9] == 1
                    ) {
                        wrapper = wrapper[getFuncName(data[0])].apply(wrapper, data[3]);
                    } else {
                        wrapper = (func.length == 1 && isLaziable(func))
                            ? wrapper[funcName]()
                            : wrapper.thru(func);
                    }
                }
                return function() {
                    var args = arguments,
                        value = args[0];

                    if (wrapper && args.length == 1 && isArray(value)) {
                        return wrapper.plant(value).value();
                    }
                    var index = 0,
                        result = length ? funcs[index].apply(this, args) : value;

                    while (++index < length) {
                        result = funcs[index].call(this, result);
                    }
                    return result;
                };
            });
        }

        function createHybrid(func, bitmask, thisArg, partials, holders, partialsRight, holdersRight, argPos, ary, arity) {
            var isAry = bitmask & WRAP_ARY_FLAG,
                isBind = bitmask & WRAP_BIND_FLAG,
                isBindKey = bitmask & WRAP_BIND_KEY_FLAG,
                isCurried = bitmask & (WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG),
                isFlip = bitmask & WRAP_FLIP_FLAG,
                Ctor = isBindKey ? undefined : createCtor(func);

            function wrapper() {
                var length = arguments.length,
                    args = Array(length),
                    index = length;

                while (index--) {
                    args[index] = arguments[index];
                }
                if (isCurried) {
                    var placeholder = getHolder(wrapper),
                        holdersCount = countHolders(args, placeholder);
                }
                if (partials) {
                    args = composeArgs(args, partials, holders, isCurried);
                }
                if (partialsRight) {
                    args = composeArgsRight(args, partialsRight, holdersRight, isCurried);
                }
                length -= holdersCount;
                if (isCurried && length < arity) {
                    var newHolders = replaceHolders(args, placeholder);
                    return createRecurry(
                        func, bitmask, createHybrid, wrapper.placeholder, thisArg,
                        args, newHolders, argPos, ary, arity - length
                    );
                }
                var thisBinding = isBind ? thisArg : this,
                    fn = isBindKey ? thisBinding[func] : func;

                length = args.length;
                if (argPos) {
                    args = reorder(args, argPos);
                } else if (isFlip && length > 1) {
                    args.reverse();
                }
                if (isAry && ary < length) {
                    args.length = ary;
                }
                if (this && this !== root && this instanceof wrapper) {
                    fn = Ctor || createCtor(fn);
                }
                return fn.apply(thisBinding, args);
            }
            return wrapper;
        }

        function createInverter(setter, toIteratee) {
            return function(object, iteratee) {
                return baseInverter(object, setter, toIteratee(iteratee), {});
            };
        }

        function createMathOperation(operator, defaultValue) {
            return function(value, other) {
                var result;
                if (value === undefined && other === undefined) {
                    return defaultValue;
                }
                if (value !== undefined) {
                    result = value;
                }
                if (other !== undefined) {
                    if (result === undefined) {
                        return other;
                    }
                    if (typeof value == 'string' || typeof other == 'string') {
                        value = baseToString(value);
                        other = baseToString(other);
                    } else {
                        value = baseToNumber(value);
                        other = baseToNumber(other);
                    }
                    result = operator(value, other);
                }
                return result;
            };
        }

        function createOver(arrayFunc) {
            return flatRest(function(iteratees) {
                iteratees = arrayMap(iteratees, baseUnary(getIteratee()));
                return baseRest(function(args) {
                    var thisArg = this;
                    return arrayFunc(iteratees, function(iteratee) {
                        return apply(iteratee, thisArg, args);
                    });
                });
            });
        }

        function createPadding(length, chars) {
            chars = chars === undefined ? ' ' : baseToString(chars);

            var charsLength = chars.length;
            if (charsLength < 2) {
                return charsLength ? baseRepeat(chars, length) : chars;
            }
            var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));
            return hasUnicode(chars)
                ? castSlice(stringToArray(result), 0, length).join('')
                : result.slice(0, length);
        }

        function createPartial(func, bitmask, thisArg, partials) {
            var isBind = bitmask & WRAP_BIND_FLAG,
                Ctor = createCtor(func);

            function wrapper() {
                var argsIndex = -1,
                    argsLength = arguments.length,
                    leftIndex = -1,
                    leftLength = partials.length,
                    args = Array(leftLength + argsLength),
                    fn = (this && this !== root && this instanceof wrapper) ? Ctor : func;

                while (++leftIndex < leftLength) {
                    args[leftIndex] = partials[leftIndex];
                }
                while (argsLength--) {
                    args[leftIndex++] = arguments[++argsIndex];
                }
                return apply(fn, isBind ? thisArg : this, args);
            }
            return wrapper;
        }

        function createRange(fromRight) {
            return function(start, end, step) {
                if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {
                    end = step = undefined;
                }
                start = toFinite(start);
                if (end === undefined) {
                    end = start;
                    start = 0;
                } else {
                    end = toFinite(end);
                }
                step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);
                return baseRange(start, end, step, fromRight);
            };
        }

        function createRelationalOperation(operator) {
            return function(value, other) {
                if (!(typeof value == 'string' && typeof other == 'string')) {
                    value = toNumber(value);
                    other = toNumber(other);
                }
                return operator(value, other);
            };
        }

        function createRecurry(func, bitmask, wrapFunc, placeholder, thisArg, partials, holders, argPos, ary, arity) {
            var isCurry = bitmask & WRAP_CURRY_FLAG,
                newHolders = isCurry ? holders : undefined,
                newHoldersRight = isCurry ? undefined : holders,
                newPartials = isCurry ? partials : undefined,
                newPartialsRight = isCurry ? undefined : partials;

            bitmask |= (isCurry ? WRAP_PARTIAL_FLAG : WRAP_PARTIAL_RIGHT_FLAG);
            bitmask &= ~(isCurry ? WRAP_PARTIAL_RIGHT_FLAG : WRAP_PARTIAL_FLAG);

            if (!(bitmask & WRAP_CURRY_BOUND_FLAG)) {
                bitmask &= ~(WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG);
            }
            var newData = [
                func, bitmask, thisArg, newPartials, newHolders, newPartialsRight,
                newHoldersRight, argPos, ary, arity
            ];

            var result = wrapFunc.apply(undefined, newData);
            if (isLaziable(func)) {
                setData(result, newData);
            }
            result.placeholder = placeholder;
            return setWrapToString(result, func, bitmask);
        }

        function createRound(methodName) {
            var func = Math[methodName];
            return function(number, precision) {
                number = toNumber(number);
                precision = precision == null ? 0 : nativeMin(toInteger(precision), 292);
                if (precision) {
                    var pair = (toString(number) + 'e').split('e'),
                        value = func(pair[0] + 'e' + (+pair[1] + precision));

                    pair = (toString(value) + 'e').split('e');
                    return +(pair[0] + 'e' + (+pair[1] - precision));
                }
                return func(number);
            };
        }

        var createSet = !(Set && (1 / setToArray(new Set([,-0]))[1]) == INFINITY) ? noop : function(values) {
                return new Set(values);
            };

        function createToPairs(keysFunc) {
            return function(object) {
                var tag = getTag(object);
                if (tag == mapTag) {
                    return mapToArray(object);
                }
                if (tag == setTag) {
                    return setToPairs(object);
                }
                return baseToPairs(object, keysFunc(object));
            };
        }

        function createWrap(func, bitmask, thisArg, partials, holders, argPos, ary, arity) {
            var isBindKey = bitmask & WRAP_BIND_KEY_FLAG;
            if (!isBindKey && typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            var length = partials ? partials.length : 0;
            if (!length) {
                bitmask &= ~(WRAP_PARTIAL_FLAG | WRAP_PARTIAL_RIGHT_FLAG);
                partials = holders = undefined;
            }
            ary = ary === undefined ? ary : nativeMax(toInteger(ary), 0);
            arity = arity === undefined ? arity : toInteger(arity);
            length -= holders ? holders.length : 0;

            if (bitmask & WRAP_PARTIAL_RIGHT_FLAG) {
                var partialsRight = partials,
                    holdersRight = holders;

                partials = holders = undefined;
            }
            var data = isBindKey ? undefined : getData(func);

            var newData = [
                func, bitmask, thisArg, partials, holders, partialsRight, holdersRight,
                argPos, ary, arity
            ];

            if (data) {
                mergeData(newData, data);
            }
            func = newData[0];
            bitmask = newData[1];
            thisArg = newData[2];
            partials = newData[3];
            holders = newData[4];
            arity = newData[9] = newData[9] === undefined
                ? (isBindKey ? 0 : func.length)
                : nativeMax(newData[9] - length, 0);

            if (!arity && bitmask & (WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG)) {
                bitmask &= ~(WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG);
            }
            if (!bitmask || bitmask == WRAP_BIND_FLAG) {
                var result = createBind(func, bitmask, thisArg);
            } else if (bitmask == WRAP_CURRY_FLAG || bitmask == WRAP_CURRY_RIGHT_FLAG) {
                result = createCurry(func, bitmask, arity);
            } else if ((bitmask == WRAP_PARTIAL_FLAG || bitmask == (WRAP_BIND_FLAG | WRAP_PARTIAL_FLAG)) && !holders.length) {
                result = createPartial(func, bitmask, thisArg, partials);
            } else {
                result = createHybrid.apply(undefined, newData);
            }
            var setter = data ? baseSetData : setData;
            return setWrapToString(setter(result, newData), func, bitmask);
        }

        function customDefaultsAssignIn(objValue, srcValue, key, object) {
            if (objValue === undefined ||
                (eq(objValue, objectProto[key]) && !hasOwnProperty.call(object, key))) {
                return srcValue;
            }
            return objValue;
        }

        function customDefaultsMerge(objValue, srcValue, key, object, source, stack) {
            if (isObject(objValue) && isObject(srcValue)) {
                stack.set(srcValue, objValue);
                baseMerge(objValue, srcValue, undefined, customDefaultsMerge, stack);
                stack['delete'](srcValue);
            }
            return objValue;
        }

        function customOmitClone(value) {
            return isPlainObject(value) ? undefined : value;
        }

        function equalArrays(array, other, bitmask, customizer, equalFunc, stack) {
            var isPartial = bitmask & COMPARE_PARTIAL_FLAG,
                arrLength = array.length,
                othLength = other.length;

            if (arrLength != othLength && !(isPartial && othLength > arrLength)) {
                return false;
            }
            var stacked = stack.get(array);
            if (stacked && stack.get(other)) {
                return stacked == other;
            }
            var index = -1,
                result = true,
                seen = (bitmask & COMPARE_UNORDERED_FLAG) ? new SetCache : undefined;

            stack.set(array, other);
            stack.set(other, array);

            while (++index < arrLength) {
                var arrValue = array[index],
                    othValue = other[index];

                if (customizer) {
                    var compared = isPartial
                        ? customizer(othValue, arrValue, index, other, array, stack)
                        : customizer(arrValue, othValue, index, array, other, stack);
                }
                if (compared !== undefined) {
                    if (compared) {
                        continue;
                    }
                    result = false;
                    break;
                }
                if (seen) {
                    if (!arraySome(other, function(othValue, othIndex) {
                            if (!cacheHas(seen, othIndex) &&
                                (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {
                                return seen.push(othIndex);
                            }
                        })) {
                        result = false;
                        break;
                    }
                } else if (!(
                        arrValue === othValue ||
                        equalFunc(arrValue, othValue, bitmask, customizer, stack)
                    )) {
                    result = false;
                    break;
                }
            }
            stack['delete'](array);
            stack['delete'](other);
            return result;
        }

        function equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {
            switch (tag) {
                case dataViewTag:
                    if ((object.byteLength != other.byteLength) ||
                        (object.byteOffset != other.byteOffset)) {
                        return false;
                    }
                    object = object.buffer;
                    other = other.buffer;

                case arrayBufferTag:
                    if ((object.byteLength != other.byteLength) ||
                        !equalFunc(new Uint8Array(object), new Uint8Array(other))) {
                        return false;
                    }
                    return true;

                case boolTag:
                case dateTag:
                case numberTag:
                    return eq(+object, +other);

                case errorTag:
                    return object.name == other.name && object.message == other.message;

                case regexpTag:
                case stringTag:
                    return object == (other + '');

                case mapTag:
                    var convert = mapToArray;

                case setTag:
                    var isPartial = bitmask & COMPARE_PARTIAL_FLAG;
                    convert || (convert = setToArray);

                    if (object.size != other.size && !isPartial) {
                        return false;
                    }
                    var stacked = stack.get(object);
                    if (stacked) {
                        return stacked == other;
                    }
                    bitmask |= COMPARE_UNORDERED_FLAG;

                    stack.set(object, other);
                    var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);
                    stack['delete'](object);
                    return result;

                case symbolTag:
                    if (symbolValueOf) {
                        return symbolValueOf.call(object) == symbolValueOf.call(other);
                    }
            }
            return false;
        }

        function equalObjects(object, other, bitmask, customizer, equalFunc, stack) {
            var isPartial = bitmask & COMPARE_PARTIAL_FLAG,
                objProps = getAllKeys(object),
                objLength = objProps.length,
                othProps = getAllKeys(other),
                othLength = othProps.length;

            if (objLength != othLength && !isPartial) {
                return false;
            }
            var index = objLength;
            while (index--) {
                var key = objProps[index];
                if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {
                    return false;
                }
            }
            var stacked = stack.get(object);
            if (stacked && stack.get(other)) {
                return stacked == other;
            }
            var result = true;
            stack.set(object, other);
            stack.set(other, object);

            var skipCtor = isPartial;
            while (++index < objLength) {
                key = objProps[index];
                var objValue = object[key],
                    othValue = other[key];

                if (customizer) {
                    var compared = isPartial
                        ? customizer(othValue, objValue, key, other, object, stack)
                        : customizer(objValue, othValue, key, object, other, stack);
                }
                if (!(compared === undefined
                            ? (objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack))
                            : compared
                    )) {
                    result = false;
                    break;
                }
                skipCtor || (skipCtor = key == 'constructor');
            }
            if (result && !skipCtor) {
                var objCtor = object.constructor,
                    othCtor = other.constructor;

                if (objCtor != othCtor &&
                    ('constructor' in object && 'constructor' in other) &&
                    !(typeof objCtor == 'function' && objCtor instanceof objCtor &&
                    typeof othCtor == 'function' && othCtor instanceof othCtor)) {
                    result = false;
                }
            }
            stack['delete'](object);
            stack['delete'](other);
            return result;
        }

        function flatRest(func) {
            return setToString(overRest(func, undefined, flatten), func + '');
        }

        function getAllKeys(object) {
            return baseGetAllKeys(object, keys, getSymbols);
        }

        function getAllKeysIn(object) {
            return baseGetAllKeys(object, keysIn, getSymbolsIn);
        }

        var getData = !metaMap ? noop : function(func) {
                return metaMap.get(func);
            };

        function getFuncName(func) {
            var result = (func.name + ''),
                array = realNames[result],
                length = hasOwnProperty.call(realNames, result) ? array.length : 0;

            while (length--) {
                var data = array[length],
                    otherFunc = data.func;
                if (otherFunc == null || otherFunc == func) {
                    return data.name;
                }
            }
            return result;
        }

        function getHolder(func) {
            var object = hasOwnProperty.call(lodash, 'placeholder') ? lodash : func;
            return object.placeholder;
        }

        function getIteratee() {
            var result = lodash.iteratee || iteratee;
            result = result === iteratee ? baseIteratee : result;
            return arguments.length ? result(arguments[0], arguments[1]) : result;
        }

        function getMapData(map, key) {
            var data = map.__data__;
            return isKeyable(key)
                ? data[typeof key == 'string' ? 'string' : 'hash']
                : data.map;
        }

        function getMatchData(object) {
            var result = keys(object),
                length = result.length;

            while (length--) {
                var key = result[length],
                    value = object[key];

                result[length] = [key, value, isStrictComparable(value)];
            }
            return result;
        }

        function getNative(object, key) {
            var value = getValue(object, key);
            return baseIsNative(value) ? value : undefined;
        }

        function getRawTag(value) {
            var isOwn = hasOwnProperty.call(value, symToStringTag),
                tag = value[symToStringTag];

            try {
                value[symToStringTag] = undefined;
                var unmasked = true;
            } catch (e) {}

            var result = nativeObjectToString.call(value);
            if (unmasked) {
                if (isOwn) {
                    value[symToStringTag] = tag;
                } else {
                    delete value[symToStringTag];
                }
            }
            return result;
        }

        var getSymbols = !nativeGetSymbols ? stubArray : function(object) {
                if (object == null) {
                    return [];
                }
                object = Object(object);
                return arrayFilter(nativeGetSymbols(object), function(symbol) {
                    return propertyIsEnumerable.call(object, symbol);
                });
            };

        var getSymbolsIn = !nativeGetSymbols ? stubArray : function(object) {
                var result = [];
                while (object) {
                    arrayPush(result, getSymbols(object));
                    object = getPrototype(object);
                }
                return result;
            };

        var getTag = baseGetTag;

        if ((DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag) ||
            (Map && getTag(new Map) != mapTag) ||
            (Promise && getTag(Promise.resolve()) != promiseTag) ||
            (Set && getTag(new Set) != setTag) ||
            (WeakMap && getTag(new WeakMap) != weakMapTag)) {
            getTag = function(value) {
                var result = baseGetTag(value),
                    Ctor = result == objectTag ? value.constructor : undefined,
                    ctorString = Ctor ? toSource(Ctor) : '';

                if (ctorString) {
                    switch (ctorString) {
                        case dataViewCtorString: return dataViewTag;
                        case mapCtorString: return mapTag;
                        case promiseCtorString: return promiseTag;
                        case setCtorString: return setTag;
                        case weakMapCtorString: return weakMapTag;
                    }
                }
                return result;
            };
        }

        function getView(start, end, transforms) {
            var index = -1,
                length = transforms.length;

            while (++index < length) {
                var data = transforms[index],
                    size = data.size;

                switch (data.type) {
                    case 'drop':      start += size; break;
                    case 'dropRight': end -= size; break;
                    case 'take':      end = nativeMin(end, start + size); break;
                    case 'takeRight': start = nativeMax(start, end - size); break;
                }
            }
            return { 'start': start, 'end': end };
        }

        function getWrapDetails(source) {
            var match = source.match(reWrapDetails);
            return match ? match[1].split(reSplitDetails) : [];
        }

        function hasPath(object, path, hasFunc) {
            path = castPath(path, object);

            var index = -1,
                length = path.length,
                result = false;

            while (++index < length) {
                var key = toKey(path[index]);
                if (!(result = object != null && hasFunc(object, key))) {
                    break;
                }
                object = object[key];
            }
            if (result || ++index != length) {
                return result;
            }
            length = object == null ? 0 : object.length;
            return !!length && isLength(length) && isIndex(key, length) &&
                (isArray(object) || isArguments(object));
        }

        function initCloneArray(array) {
            var length = array.length,
                result = array.constructor(length);

            if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {
                result.index = array.index;
                result.input = array.input;
            }
            return result;
        }

        function initCloneObject(object) {
            return (typeof object.constructor == 'function' && !isPrototype(object))
                ? baseCreate(getPrototype(object))
                : {};
        }

        function initCloneByTag(object, tag, cloneFunc, isDeep) {
            var Ctor = object.constructor;
            switch (tag) {
                case arrayBufferTag:
                    return cloneArrayBuffer(object);

                case boolTag:
                case dateTag:
                    return new Ctor(+object);

                case dataViewTag:
                    return cloneDataView(object, isDeep);

                case float32Tag: case float64Tag:
                case int8Tag: case int16Tag: case int32Tag:
                case uint8Tag: case uint8ClampedTag: case uint16Tag: case uint32Tag:
                return cloneTypedArray(object, isDeep);

                case mapTag:
                    return cloneMap(object, isDeep, cloneFunc);

                case numberTag:
                case stringTag:
                    return new Ctor(object);

                case regexpTag:
                    return cloneRegExp(object);

                case setTag:
                    return cloneSet(object, isDeep, cloneFunc);

                case symbolTag:
                    return cloneSymbol(object);
            }
        }

        function insertWrapDetails(source, details) {
            var length = details.length;
            if (!length) {
                return source;
            }
            var lastIndex = length - 1;
            details[lastIndex] = (length > 1 ? '& ' : '') + details[lastIndex];
            details = details.join(length > 2 ? ', ' : ' ');
            return source.replace(reWrapComment, '{\n/* [wrapped with ' + details + '] */\n');
        }

        function isFlattenable(value) {
            return isArray(value) || isArguments(value) ||
                !!(spreadableSymbol && value && value[spreadableSymbol]);
        }

        function isIndex(value, length) {
            length = length == null ? MAX_SAFE_INTEGER : length;
            return !!length &&
                (typeof value == 'number' || reIsUint.test(value)) &&
                (value > -1 && value % 1 == 0 && value < length);
        }

        function isIterateeCall(value, index, object) {
            if (!isObject(object)) {
                return false;
            }
            var type = typeof index;
            if (type == 'number'
                    ? (isArrayLike(object) && isIndex(index, object.length))
                    : (type == 'string' && index in object)
            ) {
                return eq(object[index], value);
            }
            return false;
        }

        function isKey(value, object) {
            if (isArray(value)) {
                return false;
            }
            var type = typeof value;
            if (type == 'number' || type == 'symbol' || type == 'boolean' ||
                value == null || isSymbol(value)) {
                return true;
            }
            return reIsPlainProp.test(value) || !reIsDeepProp.test(value) ||
                (object != null && value in Object(object));
        }

        function isKeyable(value) {
            var type = typeof value;
            return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')
                ? (value !== '__proto__')
                : (value === null);
        }

        function isLaziable(func) {
            var funcName = getFuncName(func),
                other = lodash[funcName];

            if (typeof other != 'function' || !(funcName in LazyWrapper.prototype)) {
                return false;
            }
            if (func === other) {
                return true;
            }
            var data = getData(other);
            return !!data && func === data[0];
        }

        function isMasked(func) {
            return !!maskSrcKey && (maskSrcKey in func);
        }

        var isMaskable = coreJsData ? isFunction : stubFalse;

        function isPrototype(value) {
            var Ctor = value && value.constructor,
                proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;

            return value === proto;
        }

        function isStrictComparable(value) {
            return value === value && !isObject(value);
        }

        function matchesStrictComparable(key, srcValue) {
            return function(object) {
                if (object == null) {
                    return false;
                }
                return object[key] === srcValue &&
                    (srcValue !== undefined || (key in Object(object)));
            };
        }

        function memoizeCapped(func) {
            var result = memoize(func, function(key) {
                if (cache.size === MAX_MEMOIZE_SIZE) {
                    cache.clear();
                }
                return key;
            });

            var cache = result.cache;
            return result;
        }

        function mergeData(data, source) {
            var bitmask = data[1],
                srcBitmask = source[1],
                newBitmask = bitmask | srcBitmask,
                isCommon = newBitmask < (WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG | WRAP_ARY_FLAG);

            var isCombo =
                ((srcBitmask == WRAP_ARY_FLAG) && (bitmask == WRAP_CURRY_FLAG)) ||
                ((srcBitmask == WRAP_ARY_FLAG) && (bitmask == WRAP_REARG_FLAG) && (data[7].length <= source[8])) ||
                ((srcBitmask == (WRAP_ARY_FLAG | WRAP_REARG_FLAG)) && (source[7].length <= source[8]) && (bitmask == WRAP_CURRY_FLAG));

            if (!(isCommon || isCombo)) {
                return data;
            }
            if (srcBitmask & WRAP_BIND_FLAG) {
                data[2] = source[2];
                newBitmask |= bitmask & WRAP_BIND_FLAG ? 0 : WRAP_CURRY_BOUND_FLAG;
            }
            var value = source[3];
            if (value) {
                var partials = data[3];
                data[3] = partials ? composeArgs(partials, value, source[4]) : value;
                data[4] = partials ? replaceHolders(data[3], PLACEHOLDER) : source[4];
            }
            value = source[5];
            if (value) {
                partials = data[5];
                data[5] = partials ? composeArgsRight(partials, value, source[6]) : value;
                data[6] = partials ? replaceHolders(data[5], PLACEHOLDER) : source[6];
            }
            value = source[7];
            if (value) {
                data[7] = value;
            }
            if (srcBitmask & WRAP_ARY_FLAG) {
                data[8] = data[8] == null ? source[8] : nativeMin(data[8], source[8]);
            }
            if (data[9] == null) {
                data[9] = source[9];
            }
            data[0] = source[0];
            data[1] = newBitmask;

            return data;
        }

        function nativeKeysIn(object) {
            var result = [];
            if (object != null) {
                for (var key in Object(object)) {
                    result.push(key);
                }
            }
            return result;
        }

        function objectToString(value) {
            return nativeObjectToString.call(value);
        }

        function overRest(func, start, transform) {
            start = nativeMax(start === undefined ? (func.length - 1) : start, 0);
            return function() {
                var args = arguments,
                    index = -1,
                    length = nativeMax(args.length - start, 0),
                    array = Array(length);

                while (++index < length) {
                    array[index] = args[start + index];
                }
                index = -1;
                var otherArgs = Array(start + 1);
                while (++index < start) {
                    otherArgs[index] = args[index];
                }
                otherArgs[start] = transform(array);
                return apply(func, this, otherArgs);
            };
        }

        function parent(object, path) {
            return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));
        }

        function reorder(array, indexes) {
            var arrLength = array.length,
                length = nativeMin(indexes.length, arrLength),
                oldArray = copyArray(array);

            while (length--) {
                var index = indexes[length];
                array[length] = isIndex(index, arrLength) ? oldArray[index] : undefined;
            }
            return array;
        }

        var setData = shortOut(baseSetData);

        var setTimeout = ctxSetTimeout || function(func, wait) {
                return root.setTimeout(func, wait);
            };

        var setToString = shortOut(baseSetToString);

        function setWrapToString(wrapper, reference, bitmask) {
            var source = (reference + '');
            return setToString(wrapper, insertWrapDetails(source, updateWrapDetails(getWrapDetails(source), bitmask)));
        }

        function shortOut(func) {
            var count = 0,
                lastCalled = 0;

            return function() {
                var stamp = nativeNow(),
                    remaining = HOT_SPAN - (stamp - lastCalled);

                lastCalled = stamp;
                if (remaining > 0) {
                    if (++count >= HOT_COUNT) {
                        return arguments[0];
                    }
                } else {
                    count = 0;
                }
                return func.apply(undefined, arguments);
            };
        }

        function shuffleSelf(array, size) {
            var index = -1,
                length = array.length,
                lastIndex = length - 1;

            size = size === undefined ? length : size;
            while (++index < size) {
                var rand = baseRandom(index, lastIndex),
                    value = array[rand];

                array[rand] = array[index];
                array[index] = value;
            }
            array.length = size;
            return array;
        }

        var stringToPath = memoizeCapped(function(string) {
            var result = [];
            if (reLeadingDot.test(string)) {
                result.push('');
            }
            string.replace(rePropName, function(match, number, quote, string) {
                result.push(quote ? string.replace(reEscapeChar, '$1') : (number || match));
            });
            return result;
        });

        function toKey(value) {
            if (typeof value == 'string' || isSymbol(value)) {
                return value;
            }
            var result = (value + '');
            return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;
        }

        function toSource(func) {
            if (func != null) {
                try {
                    return funcToString.call(func);
                } catch (e) {}
                try {
                    return (func + '');
                } catch (e) {}
            }
            return '';
        }

        function updateWrapDetails(details, bitmask) {
            arrayEach(wrapFlags, function(pair) {
                var value = '_.' + pair[0];
                if ((bitmask & pair[1]) && !arrayIncludes(details, value)) {
                    details.push(value);
                }
            });
            return details.sort();
        }

        function wrapperClone(wrapper) {
            if (wrapper instanceof LazyWrapper) {
                return wrapper.clone();
            }
            var result = new LodashWrapper(wrapper.__wrapped__, wrapper.__chain__);
            result.__actions__ = copyArray(wrapper.__actions__);
            result.__index__  = wrapper.__index__;
            result.__values__ = wrapper.__values__;
            return result;
        }


        function chunk(array, size, guard) {
            if ((guard ? isIterateeCall(array, size, guard) : size === undefined)) {
                size = 1;
            } else {
                size = nativeMax(toInteger(size), 0);
            }
            var length = array == null ? 0 : array.length;
            if (!length || size < 1) {
                return [];
            }
            var index = 0,
                resIndex = 0,
                result = Array(nativeCeil(length / size));

            while (index < length) {
                result[resIndex++] = baseSlice(array, index, (index += size));
            }
            return result;
        }

        function compact(array) {
            var index = -1,
                length = array == null ? 0 : array.length,
                resIndex = 0,
                result = [];

            while (++index < length) {
                var value = array[index];
                if (value) {
                    result[resIndex++] = value;
                }
            }
            return result;
        }

        function concat() {
            var length = arguments.length;
            if (!length) {
                return [];
            }
            var args = Array(length - 1),
                array = arguments[0],
                index = length;

            while (index--) {
                args[index - 1] = arguments[index];
            }
            return arrayPush(isArray(array) ? copyArray(array) : [array], baseFlatten(args, 1));
        }

        var difference = baseRest(function(array, values) {
            return isArrayLikeObject(array)
                ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true))
                : [];
        });

        var differenceBy = baseRest(function(array, values) {
            var iteratee = last(values);
            if (isArrayLikeObject(iteratee)) {
                iteratee = undefined;
            }
            return isArrayLikeObject(array)
                ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true), getIteratee(iteratee, 2))
                : [];
        });

        var differenceWith = baseRest(function(array, values) {
            var comparator = last(values);
            if (isArrayLikeObject(comparator)) {
                comparator = undefined;
            }
            return isArrayLikeObject(array)
                ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true), undefined, comparator)
                : [];
        });

        function drop(array, n, guard) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return [];
            }
            n = (guard || n === undefined) ? 1 : toInteger(n);
            return baseSlice(array, n < 0 ? 0 : n, length);
        }

        function dropRight(array, n, guard) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return [];
            }
            n = (guard || n === undefined) ? 1 : toInteger(n);
            n = length - n;
            return baseSlice(array, 0, n < 0 ? 0 : n);
        }

        function dropRightWhile(array, predicate) {
            return (array && array.length)
                ? baseWhile(array, getIteratee(predicate, 3), true, true)
                : [];
        }

        function dropWhile(array, predicate) {
            return (array && array.length)
                ? baseWhile(array, getIteratee(predicate, 3), true)
                : [];
        }

        function fill(array, value, start, end) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return [];
            }
            if (start && typeof start != 'number' && isIterateeCall(array, value, start)) {
                start = 0;
                end = length;
            }
            return baseFill(array, value, start, end);
        }

        function findIndex(array, predicate, fromIndex) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return -1;
            }
            var index = fromIndex == null ? 0 : toInteger(fromIndex);
            if (index < 0) {
                index = nativeMax(length + index, 0);
            }
            return baseFindIndex(array, getIteratee(predicate, 3), index);
        }

        function findLastIndex(array, predicate, fromIndex) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return -1;
            }
            var index = length - 1;
            if (fromIndex !== undefined) {
                index = toInteger(fromIndex);
                index = fromIndex < 0
                    ? nativeMax(length + index, 0)
                    : nativeMin(index, length - 1);
            }
            return baseFindIndex(array, getIteratee(predicate, 3), index, true);
        }

        function flatten(array) {
            var length = array == null ? 0 : array.length;
            return length ? baseFlatten(array, 1) : [];
        }

        function flattenDeep(array) {
            var length = array == null ? 0 : array.length;
            return length ? baseFlatten(array, INFINITY) : [];
        }

        function flattenDepth(array, depth) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return [];
            }
            depth = depth === undefined ? 1 : toInteger(depth);
            return baseFlatten(array, depth);
        }

        function fromPairs(pairs) {
            var index = -1,
                length = pairs == null ? 0 : pairs.length,
                result = {};

            while (++index < length) {
                var pair = pairs[index];
                result[pair[0]] = pair[1];
            }
            return result;
        }

        function head(array) {
            return (array && array.length) ? array[0] : undefined;
        }

        function indexOf(array, value, fromIndex) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return -1;
            }
            var index = fromIndex == null ? 0 : toInteger(fromIndex);
            if (index < 0) {
                index = nativeMax(length + index, 0);
            }
            return baseIndexOf(array, value, index);
        }

        function initial(array) {
            var length = array == null ? 0 : array.length;
            return length ? baseSlice(array, 0, -1) : [];
        }

        var intersection = baseRest(function(arrays) {
            var mapped = arrayMap(arrays, castArrayLikeObject);
            return (mapped.length && mapped[0] === arrays[0])
                ? baseIntersection(mapped)
                : [];
        });

        var intersectionBy = baseRest(function(arrays) {
            var iteratee = last(arrays),
                mapped = arrayMap(arrays, castArrayLikeObject);

            if (iteratee === last(mapped)) {
                iteratee = undefined;
            } else {
                mapped.pop();
            }
            return (mapped.length && mapped[0] === arrays[0])
                ? baseIntersection(mapped, getIteratee(iteratee, 2))
                : [];
        });

        var intersectionWith = baseRest(function(arrays) {
            var comparator = last(arrays),
                mapped = arrayMap(arrays, castArrayLikeObject);

            comparator = typeof comparator == 'function' ? comparator : undefined;
            if (comparator) {
                mapped.pop();
            }
            return (mapped.length && mapped[0] === arrays[0])
                ? baseIntersection(mapped, undefined, comparator)
                : [];
        });

        function join(array, separator) {
            return array == null ? '' : nativeJoin.call(array, separator);
        }

        function last(array) {
            var length = array == null ? 0 : array.length;
            return length ? array[length - 1] : undefined;
        }

        function lastIndexOf(array, value, fromIndex) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return -1;
            }
            var index = length;
            if (fromIndex !== undefined) {
                index = toInteger(fromIndex);
                index = index < 0 ? nativeMax(length + index, 0) : nativeMin(index, length - 1);
            }
            return value === value
                ? strictLastIndexOf(array, value, index)
                : baseFindIndex(array, baseIsNaN, index, true);
        }

        function nth(array, n) {
            return (array && array.length) ? baseNth(array, toInteger(n)) : undefined;
        }

        var pull = baseRest(pullAll);

        function pullAll(array, values) {
            return (array && array.length && values && values.length)
                ? basePullAll(array, values)
                : array;
        }

        function pullAllBy(array, values, iteratee) {
            return (array && array.length && values && values.length)
                ? basePullAll(array, values, getIteratee(iteratee, 2))
                : array;
        }

        function pullAllWith(array, values, comparator) {
            return (array && array.length && values && values.length)
                ? basePullAll(array, values, undefined, comparator)
                : array;
        }

        var pullAt = flatRest(function(array, indexes) {
            var length = array == null ? 0 : array.length,
                result = baseAt(array, indexes);

            basePullAt(array, arrayMap(indexes, function(index) {
                return isIndex(index, length) ? +index : index;
            }).sort(compareAscending));

            return result;
        });

        function remove(array, predicate) {
            var result = [];
            if (!(array && array.length)) {
                return result;
            }
            var index = -1,
                indexes = [],
                length = array.length;

            predicate = getIteratee(predicate, 3);
            while (++index < length) {
                var value = array[index];
                if (predicate(value, index, array)) {
                    result.push(value);
                    indexes.push(index);
                }
            }
            basePullAt(array, indexes);
            return result;
        }

        function reverse(array) {
            return array == null ? array : nativeReverse.call(array);
        }

        function slice(array, start, end) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return [];
            }
            if (end && typeof end != 'number' && isIterateeCall(array, start, end)) {
                start = 0;
                end = length;
            }
            else {
                start = start == null ? 0 : toInteger(start);
                end = end === undefined ? length : toInteger(end);
            }
            return baseSlice(array, start, end);
        }

        function sortedIndex(array, value) {
            return baseSortedIndex(array, value);
        }

        function sortedIndexBy(array, value, iteratee) {
            return baseSortedIndexBy(array, value, getIteratee(iteratee, 2));
        }

        function sortedIndexOf(array, value) {
            var length = array == null ? 0 : array.length;
            if (length) {
                var index = baseSortedIndex(array, value);
                if (index < length && eq(array[index], value)) {
                    return index;
                }
            }
            return -1;
        }

        function sortedLastIndex(array, value) {
            return baseSortedIndex(array, value, true);
        }

        function sortedLastIndexBy(array, value, iteratee) {
            return baseSortedIndexBy(array, value, getIteratee(iteratee, 2), true);
        }

        function sortedLastIndexOf(array, value) {
            var length = array == null ? 0 : array.length;
            if (length) {
                var index = baseSortedIndex(array, value, true) - 1;
                if (eq(array[index], value)) {
                    return index;
                }
            }
            return -1;
        }

        function sortedUniq(array) {
            return (array && array.length)
                ? baseSortedUniq(array)
                : [];
        }

        function sortedUniqBy(array, iteratee) {
            return (array && array.length)
                ? baseSortedUniq(array, getIteratee(iteratee, 2))
                : [];
        }

        function tail(array) {
            var length = array == null ? 0 : array.length;
            return length ? baseSlice(array, 1, length) : [];
        }

        function take(array, n, guard) {
            if (!(array && array.length)) {
                return [];
            }
            n = (guard || n === undefined) ? 1 : toInteger(n);
            return baseSlice(array, 0, n < 0 ? 0 : n);
        }

        function takeRight(array, n, guard) {
            var length = array == null ? 0 : array.length;
            if (!length) {
                return [];
            }
            n = (guard || n === undefined) ? 1 : toInteger(n);
            n = length - n;
            return baseSlice(array, n < 0 ? 0 : n, length);
        }

        function takeRightWhile(array, predicate) {
            return (array && array.length)
                ? baseWhile(array, getIteratee(predicate, 3), false, true)
                : [];
        }

        function takeWhile(array, predicate) {
            return (array && array.length)
                ? baseWhile(array, getIteratee(predicate, 3))
                : [];
        }

        var union = baseRest(function(arrays) {
            return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));
        });

        var unionBy = baseRest(function(arrays) {
            var iteratee = last(arrays);
            if (isArrayLikeObject(iteratee)) {
                iteratee = undefined;
            }
            return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), getIteratee(iteratee, 2));
        });

        var unionWith = baseRest(function(arrays) {
            var comparator = last(arrays);
            comparator = typeof comparator == 'function' ? comparator : undefined;
            return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), undefined, comparator);
        });

        function uniq(array) {
            return (array && array.length) ? baseUniq(array) : [];
        }

        function uniqBy(array, iteratee) {
            return (array && array.length) ? baseUniq(array, getIteratee(iteratee, 2)) : [];
        }

        function uniqWith(array, comparator) {
            comparator = typeof comparator == 'function' ? comparator : undefined;
            return (array && array.length) ? baseUniq(array, undefined, comparator) : [];
        }

        function unzip(array) {
            if (!(array && array.length)) {
                return [];
            }
            var length = 0;
            array = arrayFilter(array, function(group) {
                if (isArrayLikeObject(group)) {
                    length = nativeMax(group.length, length);
                    return true;
                }
            });
            return baseTimes(length, function(index) {
                return arrayMap(array, baseProperty(index));
            });
        }

        function unzipWith(array, iteratee) {
            if (!(array && array.length)) {
                return [];
            }
            var result = unzip(array);
            if (iteratee == null) {
                return result;
            }
            return arrayMap(result, function(group) {
                return apply(iteratee, undefined, group);
            });
        }

        var without = baseRest(function(array, values) {
            return isArrayLikeObject(array)
                ? baseDifference(array, values)
                : [];
        });

        var xor = baseRest(function(arrays) {
            return baseXor(arrayFilter(arrays, isArrayLikeObject));
        });

        var xorBy = baseRest(function(arrays) {
            var iteratee = last(arrays);
            if (isArrayLikeObject(iteratee)) {
                iteratee = undefined;
            }
            return baseXor(arrayFilter(arrays, isArrayLikeObject), getIteratee(iteratee, 2));
        });

        var xorWith = baseRest(function(arrays) {
            var comparator = last(arrays);
            comparator = typeof comparator == 'function' ? comparator : undefined;
            return baseXor(arrayFilter(arrays, isArrayLikeObject), undefined, comparator);
        });

        var zip = baseRest(unzip);

        function zipObject(props, values) {
            return baseZipObject(props || [], values || [], assignValue);
        }

        function zipObjectDeep(props, values) {
            return baseZipObject(props || [], values || [], baseSet);
        }

        var zipWith = baseRest(function(arrays) {
            var length = arrays.length,
                iteratee = length > 1 ? arrays[length - 1] : undefined;

            iteratee = typeof iteratee == 'function' ? (arrays.pop(), iteratee) : undefined;
            return unzipWith(arrays, iteratee);
        });


        function chain(value) {
            var result = lodash(value);
            result.__chain__ = true;
            return result;
        }

        function tap(value, interceptor) {
            interceptor(value);
            return value;
        }

        function thru(value, interceptor) {
            return interceptor(value);
        }

        var wrapperAt = flatRest(function(paths) {
            var length = paths.length,
                start = length ? paths[0] : 0,
                value = this.__wrapped__,
                interceptor = function(object) { return baseAt(object, paths); };

            if (length > 1 || this.__actions__.length ||
                !(value instanceof LazyWrapper) || !isIndex(start)) {
                return this.thru(interceptor);
            }
            value = value.slice(start, +start + (length ? 1 : 0));
            value.__actions__.push({
                'func': thru,
                'args': [interceptor],
                'thisArg': undefined
            });
            return new LodashWrapper(value, this.__chain__).thru(function(array) {
                if (length && !array.length) {
                    array.push(undefined);
                }
                return array;
            });
        });

        function wrapperChain() {
            return chain(this);
        }

        function wrapperCommit() {
            return new LodashWrapper(this.value(), this.__chain__);
        }

        function wrapperNext() {
            if (this.__values__ === undefined) {
                this.__values__ = toArray(this.value());
            }
            var done = this.__index__ >= this.__values__.length,
                value = done ? undefined : this.__values__[this.__index__++];

            return { 'done': done, 'value': value };
        }

        function wrapperToIterator() {
            return this;
        }

        function wrapperPlant(value) {
            var result,
                parent = this;

            while (parent instanceof baseLodash) {
                var clone = wrapperClone(parent);
                clone.__index__ = 0;
                clone.__values__ = undefined;
                if (result) {
                    previous.__wrapped__ = clone;
                } else {
                    result = clone;
                }
                var previous = clone;
                parent = parent.__wrapped__;
            }
            previous.__wrapped__ = value;
            return result;
        }

        function wrapperReverse() {
            var value = this.__wrapped__;
            if (value instanceof LazyWrapper) {
                var wrapped = value;
                if (this.__actions__.length) {
                    wrapped = new LazyWrapper(this);
                }
                wrapped = wrapped.reverse();
                wrapped.__actions__.push({
                    'func': thru,
                    'args': [reverse],
                    'thisArg': undefined
                });
                return new LodashWrapper(wrapped, this.__chain__);
            }
            return this.thru(reverse);
        }

        function wrapperValue() {
            return baseWrapperValue(this.__wrapped__, this.__actions__);
        }


        var countBy = createAggregator(function(result, value, key) {
            if (hasOwnProperty.call(result, key)) {
                ++result[key];
            } else {
                baseAssignValue(result, key, 1);
            }
        });

        function every(collection, predicate, guard) {
            var func = isArray(collection) ? arrayEvery : baseEvery;
            if (guard && isIterateeCall(collection, predicate, guard)) {
                predicate = undefined;
            }
            return func(collection, getIteratee(predicate, 3));
        }

        function filter(collection, predicate) {
            var func = isArray(collection) ? arrayFilter : baseFilter;
            return func(collection, getIteratee(predicate, 3));
        }

        var find = createFind(findIndex);

        var findLast = createFind(findLastIndex);

        function flatMap(collection, iteratee) {
            return baseFlatten(map(collection, iteratee), 1);
        }

        function flatMapDeep(collection, iteratee) {
            return baseFlatten(map(collection, iteratee), INFINITY);
        }

        function flatMapDepth(collection, iteratee, depth) {
            depth = depth === undefined ? 1 : toInteger(depth);
            return baseFlatten(map(collection, iteratee), depth);
        }

        function forEach(collection, iteratee) {
            var func = isArray(collection) ? arrayEach : baseEach;
            return func(collection, getIteratee(iteratee, 3));
        }

        function forEachRight(collection, iteratee) {
            var func = isArray(collection) ? arrayEachRight : baseEachRight;
            return func(collection, getIteratee(iteratee, 3));
        }

        var groupBy = createAggregator(function(result, value, key) {
            if (hasOwnProperty.call(result, key)) {
                result[key].push(value);
            } else {
                baseAssignValue(result, key, [value]);
            }
        });

        function includes(collection, value, fromIndex, guard) {
            collection = isArrayLike(collection) ? collection : values(collection);
            fromIndex = (fromIndex && !guard) ? toInteger(fromIndex) : 0;

            var length = collection.length;
            if (fromIndex < 0) {
                fromIndex = nativeMax(length + fromIndex, 0);
            }
            return isString(collection)
                ? (fromIndex <= length && collection.indexOf(value, fromIndex) > -1)
                : (!!length && baseIndexOf(collection, value, fromIndex) > -1);
        }

        var invokeMap = baseRest(function(collection, path, args) {
            var index = -1,
                isFunc = typeof path == 'function',
                result = isArrayLike(collection) ? Array(collection.length) : [];

            baseEach(collection, function(value) {
                result[++index] = isFunc ? apply(path, value, args) : baseInvoke(value, path, args);
            });
            return result;
        });

        var keyBy = createAggregator(function(result, value, key) {
            baseAssignValue(result, key, value);
        });

        function map(collection, iteratee) {
            var func = isArray(collection) ? arrayMap : baseMap;
            return func(collection, getIteratee(iteratee, 3));
        }

        function orderBy(collection, iteratees, orders, guard) {
            if (collection == null) {
                return [];
            }
            if (!isArray(iteratees)) {
                iteratees = iteratees == null ? [] : [iteratees];
            }
            orders = guard ? undefined : orders;
            if (!isArray(orders)) {
                orders = orders == null ? [] : [orders];
            }
            return baseOrderBy(collection, iteratees, orders);
        }

        var partition = createAggregator(function(result, value, key) {
            result[key ? 0 : 1].push(value);
        }, function() { return [[], []]; });

        function reduce(collection, iteratee, accumulator) {
            var func = isArray(collection) ? arrayReduce : baseReduce,
                initAccum = arguments.length < 3;

            return func(collection, getIteratee(iteratee, 4), accumulator, initAccum, baseEach);
        }

        function reduceRight(collection, iteratee, accumulator) {
            var func = isArray(collection) ? arrayReduceRight : baseReduce,
                initAccum = arguments.length < 3;

            return func(collection, getIteratee(iteratee, 4), accumulator, initAccum, baseEachRight);
        }

        function reject(collection, predicate) {
            var func = isArray(collection) ? arrayFilter : baseFilter;
            return func(collection, negate(getIteratee(predicate, 3)));
        }

        function sample(collection) {
            var func = isArray(collection) ? arraySample : baseSample;
            return func(collection);
        }

        function sampleSize(collection, n, guard) {
            if ((guard ? isIterateeCall(collection, n, guard) : n === undefined)) {
                n = 1;
            } else {
                n = toInteger(n);
            }
            var func = isArray(collection) ? arraySampleSize : baseSampleSize;
            return func(collection, n);
        }

        function shuffle(collection) {
            var func = isArray(collection) ? arrayShuffle : baseShuffle;
            return func(collection);
        }

        function size(collection) {
            if (collection == null) {
                return 0;
            }
            if (isArrayLike(collection)) {
                return isString(collection) ? stringSize(collection) : collection.length;
            }
            var tag = getTag(collection);
            if (tag == mapTag || tag == setTag) {
                return collection.size;
            }
            return baseKeys(collection).length;
        }

        function some(collection, predicate, guard) {
            var func = isArray(collection) ? arraySome : baseSome;
            if (guard && isIterateeCall(collection, predicate, guard)) {
                predicate = undefined;
            }
            return func(collection, getIteratee(predicate, 3));
        }

        var sortBy = baseRest(function(collection, iteratees) {
            if (collection == null) {
                return [];
            }
            var length = iteratees.length;
            if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {
                iteratees = [];
            } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {
                iteratees = [iteratees[0]];
            }
            return baseOrderBy(collection, baseFlatten(iteratees, 1), []);
        });


        var now = ctxNow || function() {
                return root.Date.now();
            };


        function after(n, func) {
            if (typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            n = toInteger(n);
            return function() {
                if (--n < 1) {
                    return func.apply(this, arguments);
                }
            };
        }

        function ary(func, n, guard) {
            n = guard ? undefined : n;
            n = (func && n == null) ? func.length : n;
            return createWrap(func, WRAP_ARY_FLAG, undefined, undefined, undefined, undefined, n);
        }

        function before(n, func) {
            var result;
            if (typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            n = toInteger(n);
            return function() {
                if (--n > 0) {
                    result = func.apply(this, arguments);
                }
                if (n <= 1) {
                    func = undefined;
                }
                return result;
            };
        }

        var bind = baseRest(function(func, thisArg, partials) {
            var bitmask = WRAP_BIND_FLAG;
            if (partials.length) {
                var holders = replaceHolders(partials, getHolder(bind));
                bitmask |= WRAP_PARTIAL_FLAG;
            }
            return createWrap(func, bitmask, thisArg, partials, holders);
        });

        var bindKey = baseRest(function(object, key, partials) {
            var bitmask = WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG;
            if (partials.length) {
                var holders = replaceHolders(partials, getHolder(bindKey));
                bitmask |= WRAP_PARTIAL_FLAG;
            }
            return createWrap(key, bitmask, object, partials, holders);
        });

        function curry(func, arity, guard) {
            arity = guard ? undefined : arity;
            var result = createWrap(func, WRAP_CURRY_FLAG, undefined, undefined, undefined, undefined, undefined, arity);
            result.placeholder = curry.placeholder;
            return result;
        }

        function curryRight(func, arity, guard) {
            arity = guard ? undefined : arity;
            var result = createWrap(func, WRAP_CURRY_RIGHT_FLAG, undefined, undefined, undefined, undefined, undefined, arity);
            result.placeholder = curryRight.placeholder;
            return result;
        }

        function debounce(func, wait, options) {
            var lastArgs,
                lastThis,
                maxWait,
                result,
                timerId,
                lastCallTime,
                lastInvokeTime = 0,
                leading = false,
                maxing = false,
                trailing = true;

            if (typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            wait = toNumber(wait) || 0;
            if (isObject(options)) {
                leading = !!options.leading;
                maxing = 'maxWait' in options;
                maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
                trailing = 'trailing' in options ? !!options.trailing : trailing;
            }

            function invokeFunc(time) {
                var args = lastArgs,
                    thisArg = lastThis;

                lastArgs = lastThis = undefined;
                lastInvokeTime = time;
                result = func.apply(thisArg, args);
                return result;
            }

            function leadingEdge(time) {
                lastInvokeTime = time;
                timerId = setTimeout(timerExpired, wait);
                return leading ? invokeFunc(time) : result;
            }

            function remainingWait(time) {
                var timeSinceLastCall = time - lastCallTime,
                    timeSinceLastInvoke = time - lastInvokeTime,
                    result = wait - timeSinceLastCall;

                return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;
            }

            function shouldInvoke(time) {
                var timeSinceLastCall = time - lastCallTime,
                    timeSinceLastInvoke = time - lastInvokeTime;

                return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
                (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));
            }

            function timerExpired() {
                var time = now();
                if (shouldInvoke(time)) {
                    return trailingEdge(time);
                }
                timerId = setTimeout(timerExpired, remainingWait(time));
            }

            function trailingEdge(time) {
                timerId = undefined;

                if (trailing && lastArgs) {
                    return invokeFunc(time);
                }
                lastArgs = lastThis = undefined;
                return result;
            }

            function cancel() {
                if (timerId !== undefined) {
                    clearTimeout(timerId);
                }
                lastInvokeTime = 0;
                lastArgs = lastCallTime = lastThis = timerId = undefined;
            }

            function flush() {
                return timerId === undefined ? result : trailingEdge(now());
            }

            function debounced() {
                var time = now(),
                    isInvoking = shouldInvoke(time);

                lastArgs = arguments;
                lastThis = this;
                lastCallTime = time;

                if (isInvoking) {
                    if (timerId === undefined) {
                        return leadingEdge(lastCallTime);
                    }
                    if (maxing) {
                        timerId = setTimeout(timerExpired, wait);
                        return invokeFunc(lastCallTime);
                    }
                }
                if (timerId === undefined) {
                    timerId = setTimeout(timerExpired, wait);
                }
                return result;
            }
            debounced.cancel = cancel;
            debounced.flush = flush;
            return debounced;
        }

        var defer = baseRest(function(func, args) {
            return baseDelay(func, 1, args);
        });

        var delay = baseRest(function(func, wait, args) {
            return baseDelay(func, toNumber(wait) || 0, args);
        });

        function flip(func) {
            return createWrap(func, WRAP_FLIP_FLAG);
        }

        function memoize(func, resolver) {
            if (typeof func != 'function' || (resolver != null && typeof resolver != 'function')) {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            var memoized = function() {
                var args = arguments,
                    key = resolver ? resolver.apply(this, args) : args[0],
                    cache = memoized.cache;

                if (cache.has(key)) {
                    return cache.get(key);
                }
                var result = func.apply(this, args);
                memoized.cache = cache.set(key, result) || cache;
                return result;
            };
            memoized.cache = new (memoize.Cache || MapCache);
            return memoized;
        }

        memoize.Cache = MapCache;

        function negate(predicate) {
            if (typeof predicate != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            return function() {
                var args = arguments;
                switch (args.length) {
                    case 0: return !predicate.call(this);
                    case 1: return !predicate.call(this, args[0]);
                    case 2: return !predicate.call(this, args[0], args[1]);
                    case 3: return !predicate.call(this, args[0], args[1], args[2]);
                }
                return !predicate.apply(this, args);
            };
        }

        function once(func) {
            return before(2, func);
        }

        var overArgs = castRest(function(func, transforms) {
            transforms = (transforms.length == 1 && isArray(transforms[0]))
                ? arrayMap(transforms[0], baseUnary(getIteratee()))
                : arrayMap(baseFlatten(transforms, 1), baseUnary(getIteratee()));

            var funcsLength = transforms.length;
            return baseRest(function(args) {
                var index = -1,
                    length = nativeMin(args.length, funcsLength);

                while (++index < length) {
                    args[index] = transforms[index].call(this, args[index]);
                }
                return apply(func, this, args);
            });
        });

        var partial = baseRest(function(func, partials) {
            var holders = replaceHolders(partials, getHolder(partial));
            return createWrap(func, WRAP_PARTIAL_FLAG, undefined, partials, holders);
        });

        var partialRight = baseRest(function(func, partials) {
            var holders = replaceHolders(partials, getHolder(partialRight));
            return createWrap(func, WRAP_PARTIAL_RIGHT_FLAG, undefined, partials, holders);
        });

        var rearg = flatRest(function(func, indexes) {
            return createWrap(func, WRAP_REARG_FLAG, undefined, undefined, undefined, indexes);
        });

        function rest(func, start) {
            if (typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            start = start === undefined ? start : toInteger(start);
            return baseRest(func, start);
        }

        function spread(func, start) {
            if (typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            start = start == null ? 0 : nativeMax(toInteger(start), 0);
            return baseRest(function(args) {
                var array = args[start],
                    otherArgs = castSlice(args, 0, start);

                if (array) {
                    arrayPush(otherArgs, array);
                }
                return apply(func, this, otherArgs);
            });
        }

        function throttle(func, wait, options) {
            var leading = true,
                trailing = true;

            if (typeof func != 'function') {
                throw new TypeError(FUNC_ERROR_TEXT);
            }
            if (isObject(options)) {
                leading = 'leading' in options ? !!options.leading : leading;
                trailing = 'trailing' in options ? !!options.trailing : trailing;
            }
            return debounce(func, wait, {
                'leading': leading,
                'maxWait': wait,
                'trailing': trailing
            });
        }

        function unary(func) {
            return ary(func, 1);
        }

        function wrap(value, wrapper) {
            return partial(castFunction(wrapper), value);
        }


        function castArray() {
            if (!arguments.length) {
                return [];
            }
            var value = arguments[0];
            return isArray(value) ? value : [value];
        }

        function clone(value) {
            return baseClone(value, CLONE_SYMBOLS_FLAG);
        }

        function cloneWith(value, customizer) {
            customizer = typeof customizer == 'function' ? customizer : undefined;
            return baseClone(value, CLONE_SYMBOLS_FLAG, customizer);
        }

        function cloneDeep(value) {
            return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);
        }

        function cloneDeepWith(value, customizer) {
            customizer = typeof customizer == 'function' ? customizer : undefined;
            return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG, customizer);
        }

        function conformsTo(object, source) {
            return source == null || baseConformsTo(object, source, keys(source));
        }

        function eq(value, other) {
            return value === other || (value !== value && other !== other);
        }

        var gt = createRelationalOperation(baseGt);

        var gte = createRelationalOperation(function(value, other) {
            return value >= other;
        });

        var isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {
                return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&
                    !propertyIsEnumerable.call(value, 'callee');
            };

        var isArray = Array.isArray;

        var isArrayBuffer = nodeIsArrayBuffer ? baseUnary(nodeIsArrayBuffer) : baseIsArrayBuffer;

        function isArrayLike(value) {
            return value != null && isLength(value.length) && !isFunction(value);
        }

        function isArrayLikeObject(value) {
            return isObjectLike(value) && isArrayLike(value);
        }

        function isBoolean(value) {
            return value === true || value === false ||
                (isObjectLike(value) && baseGetTag(value) == boolTag);
        }

        var isBuffer = nativeIsBuffer || stubFalse;

        var isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;

        function isElement(value) {
            return isObjectLike(value) && value.nodeType === 1 && !isPlainObject(value);
        }

        function isEmpty(value) {
            if (value == null) {
                return true;
            }
            if (isArrayLike(value) &&
                (isArray(value) || typeof value == 'string' || typeof value.splice == 'function' ||
                isBuffer(value) || isTypedArray(value) || isArguments(value))) {
                return !value.length;
            }
            var tag = getTag(value);
            if (tag == mapTag || tag == setTag) {
                return !value.size;
            }
            if (isPrototype(value)) {
                return !baseKeys(value).length;
            }
            for (var key in value) {
                if (hasOwnProperty.call(value, key)) {
                    return false;
                }
            }
            return true;
        }

        function isEqual(value, other) {
            return baseIsEqual(value, other);
        }

        function isEqualWith(value, other, customizer) {
            customizer = typeof customizer == 'function' ? customizer : undefined;
            var result = customizer ? customizer(value, other) : undefined;
            return result === undefined ? baseIsEqual(value, other, undefined, customizer) : !!result;
        }

        function isError(value) {
            if (!isObjectLike(value)) {
                return false;
            }
            var tag = baseGetTag(value);
            return tag == errorTag || tag == domExcTag ||
                (typeof value.message == 'string' && typeof value.name == 'string' && !isPlainObject(value));
        }

        function isFinite(value) {
            return typeof value == 'number' && nativeIsFinite(value);
        }

        function isFunction(value) {
            if (!isObject(value)) {
                return false;
            }
            var tag = baseGetTag(value);
            return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;
        }

        function isInteger(value) {
            return typeof value == 'number' && value == toInteger(value);
        }

        function isLength(value) {
            return typeof value == 'number' &&
                value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
        }

        function isObject(value) {
            var type = typeof value;
            return value != null && (type == 'object' || type == 'function');
        }

        function isObjectLike(value) {
            return value != null && typeof value == 'object';
        }

        var isMap = nodeIsMap ? baseUnary(nodeIsMap) : baseIsMap;

        function isMatch(object, source) {
            return object === source || baseIsMatch(object, source, getMatchData(source));
        }

        function isMatchWith(object, source, customizer) {
            customizer = typeof customizer == 'function' ? customizer : undefined;
            return baseIsMatch(object, source, getMatchData(source), customizer);
        }

        function isNaN(value) {
            return isNumber(value) && value != +value;
        }

        function isNative(value) {
            if (isMaskable(value)) {
                throw new Error(CORE_ERROR_TEXT);
            }
            return baseIsNative(value);
        }

        function isNull(value) {
            return value === null;
        }

        function isNil(value) {
            return value == null;
        }

        function isNumber(value) {
            return typeof value == 'number' ||
                (isObjectLike(value) && baseGetTag(value) == numberTag);
        }

        function isPlainObject(value) {
            if (!isObjectLike(value) || baseGetTag(value) != objectTag) {
                return false;
            }
            var proto = getPrototype(value);
            if (proto === null) {
                return true;
            }
            var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;
            return typeof Ctor == 'function' && Ctor instanceof Ctor &&
                funcToString.call(Ctor) == objectCtorString;
        }

        var isRegExp = nodeIsRegExp ? baseUnary(nodeIsRegExp) : baseIsRegExp;

        function isSafeInteger(value) {
            return isInteger(value) && value >= -MAX_SAFE_INTEGER && value <= MAX_SAFE_INTEGER;
        }

        var isSet = nodeIsSet ? baseUnary(nodeIsSet) : baseIsSet;

        function isString(value) {
            return typeof value == 'string' ||
                (!isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag);
        }

        function isSymbol(value) {
            return typeof value == 'symbol' ||
                (isObjectLike(value) && baseGetTag(value) == symbolTag);
        }

        var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;

        function isUndefined(value) {
            return value === undefined;
        }

        function isWeakMap(value) {
            return isObjectLike(value) && getTag(value) == weakMapTag;
        }

        function isWeakSet(value) {
            return isObjectLike(value) && baseGetTag(value) == weakSetTag;
        }

        var lt = createRelationalOperation(baseLt);

        var lte = createRelationalOperation(function(value, other) {
            return value <= other;
        });

        function toArray(value) {
            if (!value) {
                return [];
            }
            if (isArrayLike(value)) {
                return isString(value) ? stringToArray(value) : copyArray(value);
            }
            if (symIterator && value[symIterator]) {
                return iteratorToArray(value[symIterator]());
            }
            var tag = getTag(value),
                func = tag == mapTag ? mapToArray : (tag == setTag ? setToArray : values);

            return func(value);
        }

        function toFinite(value) {
            if (!value) {
                return value === 0 ? value : 0;
            }
            value = toNumber(value);
            if (value === INFINITY || value === -INFINITY) {
                var sign = (value < 0 ? -1 : 1);
                return sign * MAX_INTEGER;
            }
            return value === value ? value : 0;
        }

        function toInteger(value) {
            var result = toFinite(value),
                remainder = result % 1;

            return result === result ? (remainder ? result - remainder : result) : 0;
        }

        function toLength(value) {
            return value ? baseClamp(toInteger(value), 0, MAX_ARRAY_LENGTH) : 0;
        }

        function toNumber(value) {
            if (typeof value == 'number') {
                return value;
            }
            if (isSymbol(value)) {
                return NAN;
            }
            if (isObject(value)) {
                var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
                value = isObject(other) ? (other + '') : other;
            }
            if (typeof value != 'string') {
                return value === 0 ? value : +value;
            }
            value = value.replace(reTrim, '');
            var isBinary = reIsBinary.test(value);
            return (isBinary || reIsOctal.test(value))
                ? freeParseInt(value.slice(2), isBinary ? 2 : 8)
                : (reIsBadHex.test(value) ? NAN : +value);
        }

        function toPlainObject(value) {
            return copyObject(value, keysIn(value));
        }

        function toSafeInteger(value) {
            return value
                ? baseClamp(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER)
                : (value === 0 ? value : 0);
        }

        function toString(value) {
            return value == null ? '' : baseToString(value);
        }


        var assign = createAssigner(function(object, source) {
            if (isPrototype(source) || isArrayLike(source)) {
                copyObject(source, keys(source), object);
                return;
            }
            for (var key in source) {
                if (hasOwnProperty.call(source, key)) {
                    assignValue(object, key, source[key]);
                }
            }
        });

        var assignIn = createAssigner(function(object, source) {
            copyObject(source, keysIn(source), object);
        });

        var assignInWith = createAssigner(function(object, source, srcIndex, customizer) {
            copyObject(source, keysIn(source), object, customizer);
        });

        var assignWith = createAssigner(function(object, source, srcIndex, customizer) {
            copyObject(source, keys(source), object, customizer);
        });

        var at = flatRest(baseAt);

        function create(prototype, properties) {
            var result = baseCreate(prototype);
            return properties == null ? result : baseAssign(result, properties);
        }

        var defaults = baseRest(function(args) {
            args.push(undefined, customDefaultsAssignIn);
            return apply(assignInWith, undefined, args);
        });

        var defaultsDeep = baseRest(function(args) {
            args.push(undefined, customDefaultsMerge);
            return apply(mergeWith, undefined, args);
        });

        function findKey(object, predicate) {
            return baseFindKey(object, getIteratee(predicate, 3), baseForOwn);
        }

        function findLastKey(object, predicate) {
            return baseFindKey(object, getIteratee(predicate, 3), baseForOwnRight);
        }

        function forIn(object, iteratee) {
            return object == null
                ? object
                : baseFor(object, getIteratee(iteratee, 3), keysIn);
        }

        function forInRight(object, iteratee) {
            return object == null
                ? object
                : baseForRight(object, getIteratee(iteratee, 3), keysIn);
        }

        function forOwn(object, iteratee) {
            return object && baseForOwn(object, getIteratee(iteratee, 3));
        }

        function forOwnRight(object, iteratee) {
            return object && baseForOwnRight(object, getIteratee(iteratee, 3));
        }

        function functions(object) {
            return object == null ? [] : baseFunctions(object, keys(object));
        }

        function functionsIn(object) {
            return object == null ? [] : baseFunctions(object, keysIn(object));
        }

        function get(object, path, defaultValue) {
            var result = object == null ? undefined : baseGet(object, path);
            return result === undefined ? defaultValue : result;
        }

        function has(object, path) {
            return object != null && hasPath(object, path, baseHas);
        }

        function hasIn(object, path) {
            return object != null && hasPath(object, path, baseHasIn);
        }

        var invert = createInverter(function(result, value, key) {
            result[value] = key;
        }, constant(identity));

        var invertBy = createInverter(function(result, value, key) {
            if (hasOwnProperty.call(result, value)) {
                result[value].push(key);
            } else {
                result[value] = [key];
            }
        }, getIteratee);

        var invoke = baseRest(baseInvoke);

        function keys(object) {
            return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);
        }

        function keysIn(object) {
            return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);
        }

        function mapKeys(object, iteratee) {
            var result = {};
            iteratee = getIteratee(iteratee, 3);

            baseForOwn(object, function(value, key, object) {
                baseAssignValue(result, iteratee(value, key, object), value);
            });
            return result;
        }

        function mapValues(object, iteratee) {
            var result = {};
            iteratee = getIteratee(iteratee, 3);

            baseForOwn(object, function(value, key, object) {
                baseAssignValue(result, key, iteratee(value, key, object));
            });
            return result;
        }

        var merge = createAssigner(function(object, source, srcIndex) {
            baseMerge(object, source, srcIndex);
        });

        var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
            baseMerge(object, source, srcIndex, customizer);
        });

        var omit = flatRest(function(object, paths) {
            var result = {};
            if (object == null) {
                return result;
            }
            var isDeep = false;
            paths = arrayMap(paths, function(path) {
                path = castPath(path, object);
                isDeep || (isDeep = path.length > 1);
                return path;
            });
            copyObject(object, getAllKeysIn(object), result);
            if (isDeep) {
                result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);
            }
            var length = paths.length;
            while (length--) {
                baseUnset(result, paths[length]);
            }
            return result;
        });

        function omitBy(object, predicate) {
            return pickBy(object, negate(getIteratee(predicate)));
        }

        var pick = flatRest(function(object, paths) {
            return object == null ? {} : basePick(object, paths);
        });

        function pickBy(object, predicate) {
            if (object == null) {
                return {};
            }
            var props = arrayMap(getAllKeysIn(object), function(prop) {
                return [prop];
            });
            predicate = getIteratee(predicate);
            return basePickBy(object, props, function(value, path) {
                return predicate(value, path[0]);
            });
        }

        function result(object, path, defaultValue) {
            path = castPath(path, object);

            var index = -1,
                length = path.length;

            if (!length) {
                length = 1;
                object = undefined;
            }
            while (++index < length) {
                var value = object == null ? undefined : object[toKey(path[index])];
                if (value === undefined) {
                    index = length;
                    value = defaultValue;
                }
                object = isFunction(value) ? value.call(object) : value;
            }
            return object;
        }

        function set(object, path, value) {
            return object == null ? object : baseSet(object, path, value);
        }

        function setWith(object, path, value, customizer) {
            customizer = typeof customizer == 'function' ? customizer : undefined;
            return object == null ? object : baseSet(object, path, value, customizer);
        }

        var toPairs = createToPairs(keys);

        var toPairsIn = createToPairs(keysIn);

        function transform(object, iteratee, accumulator) {
            var isArr = isArray(object),
                isArrLike = isArr || isBuffer(object) || isTypedArray(object);

            iteratee = getIteratee(iteratee, 4);
            if (accumulator == null) {
                var Ctor = object && object.constructor;
                if (isArrLike) {
                    accumulator = isArr ? new Ctor : [];
                }
                else if (isObject(object)) {
                    accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};
                }
                else {
                    accumulator = {};
                }
            }
            (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object) {
                return iteratee(accumulator, value, index, object);
            });
            return accumulator;
        }

        function unset(object, path) {
            return object == null ? true : baseUnset(object, path);
        }

        function update(object, path, updater) {
            return object == null ? object : baseUpdate(object, path, castFunction(updater));
        }

        function updateWith(object, path, updater, customizer) {
            customizer = typeof customizer == 'function' ? customizer : undefined;
            return object == null ? object : baseUpdate(object, path, castFunction(updater), customizer);
        }

        function values(object) {
            return object == null ? [] : baseValues(object, keys(object));
        }

        function valuesIn(object) {
            return object == null ? [] : baseValues(object, keysIn(object));
        }


        function clamp(number, lower, upper) {
            if (upper === undefined) {
                upper = lower;
                lower = undefined;
            }
            if (upper !== undefined) {
                upper = toNumber(upper);
                upper = upper === upper ? upper : 0;
            }
            if (lower !== undefined) {
                lower = toNumber(lower);
                lower = lower === lower ? lower : 0;
            }
            return baseClamp(toNumber(number), lower, upper);
        }

        function inRange(number, start, end) {
            start = toFinite(start);
            if (end === undefined) {
                end = start;
                start = 0;
            } else {
                end = toFinite(end);
            }
            number = toNumber(number);
            return baseInRange(number, start, end);
        }

        function random(lower, upper, floating) {
            if (floating && typeof floating != 'boolean' && isIterateeCall(lower, upper, floating)) {
                upper = floating = undefined;
            }
            if (floating === undefined) {
                if (typeof upper == 'boolean') {
                    floating = upper;
                    upper = undefined;
                }
                else if (typeof lower == 'boolean') {
                    floating = lower;
                    lower = undefined;
                }
            }
            if (lower === undefined && upper === undefined) {
                lower = 0;
                upper = 1;
            }
            else {
                lower = toFinite(lower);
                if (upper === undefined) {
                    upper = lower;
                    lower = 0;
                } else {
                    upper = toFinite(upper);
                }
            }
            if (lower > upper) {
                var temp = lower;
                lower = upper;
                upper = temp;
            }
            if (floating || lower % 1 || upper % 1) {
                var rand = nativeRandom();
                return nativeMin(lower + (rand * (upper - lower + freeParseFloat('1e-' + ((rand + '').length - 1)))), upper);
            }
            return baseRandom(lower, upper);
        }


        var camelCase = createCompounder(function(result, word, index) {
            word = word.toLowerCase();
            return result + (index ? capitalize(word) : word);
        });

        function capitalize(string) {
            return upperFirst(toString(string).toLowerCase());
        }

        function deburr(string) {
            string = toString(string);
            return string && string.replace(reLatin, deburrLetter).replace(reComboMark, '');
        }

        function endsWith(string, target, position) {
            string = toString(string);
            target = baseToString(target);

            var length = string.length;
            position = position === undefined
                ? length
                : baseClamp(toInteger(position), 0, length);

            var end = position;
            position -= target.length;
            return position >= 0 && string.slice(position, end) == target;
        }

        function escape(string) {
            string = toString(string);
            return (string && reHasUnescapedHtml.test(string))
                ? string.replace(reUnescapedHtml, escapeHtmlChar)
                : string;
        }

        function escapeRegExp(string) {
            string = toString(string);
            return (string && reHasRegExpChar.test(string))
                ? string.replace(reRegExpChar, '\\$&')
                : string;
        }

        var kebabCase = createCompounder(function(result, word, index) {
            return result + (index ? '-' : '') + word.toLowerCase();
        });

        var lowerCase = createCompounder(function(result, word, index) {
            return result + (index ? ' ' : '') + word.toLowerCase();
        });

        var lowerFirst = createCaseFirst('toLowerCase');

        function pad(string, length, chars) {
            string = toString(string);
            length = toInteger(length);

            var strLength = length ? stringSize(string) : 0;
            if (!length || strLength >= length) {
                return string;
            }
            var mid = (length - strLength) / 2;
            return (
                createPadding(nativeFloor(mid), chars) +
                string +
                createPadding(nativeCeil(mid), chars)
            );
        }

        function padEnd(string, length, chars) {
            string = toString(string);
            length = toInteger(length);

            var strLength = length ? stringSize(string) : 0;
            return (length && strLength < length)
                ? (string + createPadding(length - strLength, chars))
                : string;
        }

        function padStart(string, length, chars) {
            string = toString(string);
            length = toInteger(length);

            var strLength = length ? stringSize(string) : 0;
            return (length && strLength < length)
                ? (createPadding(length - strLength, chars) + string)
                : string;
        }

        function parseInt(string, radix, guard) {
            if (guard || radix == null) {
                radix = 0;
            } else if (radix) {
                radix = +radix;
            }
            return nativeParseInt(toString(string).replace(reTrimStart, ''), radix || 0);
        }

        function repeat(string, n, guard) {
            if ((guard ? isIterateeCall(string, n, guard) : n === undefined)) {
                n = 1;
            } else {
                n = toInteger(n);
            }
            return baseRepeat(toString(string), n);
        }

        function replace() {
            var args = arguments,
                string = toString(args[0]);

            return args.length < 3 ? string : string.replace(args[1], args[2]);
        }

        var snakeCase = createCompounder(function(result, word, index) {
            return result + (index ? '_' : '') + word.toLowerCase();
        });

        function split(string, separator, limit) {
            if (limit && typeof limit != 'number' && isIterateeCall(string, separator, limit)) {
                separator = limit = undefined;
            }
            limit = limit === undefined ? MAX_ARRAY_LENGTH : limit >>> 0;
            if (!limit) {
                return [];
            }
            string = toString(string);
            if (string && (
                    typeof separator == 'string' ||
                    (separator != null && !isRegExp(separator))
                )) {
                separator = baseToString(separator);
                if (!separator && hasUnicode(string)) {
                    return castSlice(stringToArray(string), 0, limit);
                }
            }
            return string.split(separator, limit);
        }

        var startCase = createCompounder(function(result, word, index) {
            return result + (index ? ' ' : '') + upperFirst(word);
        });

        function startsWith(string, target, position) {
            string = toString(string);
            position = position == null
                ? 0
                : baseClamp(toInteger(position), 0, string.length);

            target = baseToString(target);
            return string.slice(position, position + target.length) == target;
        }

        function template(string, options, guard) {
            var settings = lodash.templateSettings;

            if (guard && isIterateeCall(string, options, guard)) {
                options = undefined;
            }
            string = toString(string);
            options = assignInWith({}, options, settings, customDefaultsAssignIn);

            var imports = assignInWith({}, options.imports, settings.imports, customDefaultsAssignIn),
                importsKeys = keys(imports),
                importsValues = baseValues(imports, importsKeys);

            var isEscaping,
                isEvaluating,
                index = 0,
                interpolate = options.interpolate || reNoMatch,
                source = "__p += '";

            var reDelimiters = RegExp(
                (options.escape || reNoMatch).source + '|' +
                interpolate.source + '|' +
                (interpolate === reInterpolate ? reEsTemplate : reNoMatch).source + '|' +
                (options.evaluate || reNoMatch).source + '|$'
                , 'g');

            var sourceURL = '//# sourceURL=' +
                ('sourceURL' in options
                        ? options.sourceURL
                        : ('lodash.templateSources[' + (++templateCounter) + ']')
                ) + '\n';

            string.replace(reDelimiters, function(match, escapeValue, interpolateValue, esTemplateValue, evaluateValue, offset) {
                interpolateValue || (interpolateValue = esTemplateValue);

                source += string.slice(index, offset).replace(reUnescapedString, escapeStringChar);

                if (escapeValue) {
                    isEscaping = true;
                    source += "' +\n__e(" + escapeValue + ") +\n'";
                }
                if (evaluateValue) {
                    isEvaluating = true;
                    source += "';\n" + evaluateValue + ";\n__p += '";
                }
                if (interpolateValue) {
                    source += "' +\n((__t = (" + interpolateValue + ")) == null ? '' : __t) +\n'";
                }
                index = offset + match.length;

                return match;
            });

            source += "';\n";

            var variable = options.variable;
            if (!variable) {
                source = 'with (obj) {\n' + source + '\n}\n';
            }
            source = (isEvaluating ? source.replace(reEmptyStringLeading, '') : source)
                .replace(reEmptyStringMiddle, '$1')
                .replace(reEmptyStringTrailing, '$1;');

            source = 'function(' + (variable || 'obj') + ') {\n' +
                (variable
                        ? ''
                        : 'obj || (obj = {});\n'
                ) +
                "var __t, __p = ''" +
                (isEscaping
                        ? ', __e = _.escape'
                        : ''
                ) +
                (isEvaluating
                        ? ', __j = Array.prototype.join;\n' +
                        "function print() { __p += __j.call(arguments, '') }\n"
                        : ';\n'
                ) +
                source +
                'return __p\n}';

            var result = attempt(function() {
                return Function(importsKeys, sourceURL + 'return ' + source)
                    .apply(undefined, importsValues);
            });

            result.source = source;
            if (isError(result)) {
                throw result;
            }
            return result;
        }

        function toLower(value) {
            return toString(value).toLowerCase();
        }

        function toUpper(value) {
            return toString(value).toUpperCase();
        }

        function trim(string, chars, guard) {
            string = toString(string);
            if (string && (guard || chars === undefined)) {
                return string.replace(reTrim, '');
            }
            if (!string || !(chars = baseToString(chars))) {
                return string;
            }
            var strSymbols = stringToArray(string),
                chrSymbols = stringToArray(chars),
                start = charsStartIndex(strSymbols, chrSymbols),
                end = charsEndIndex(strSymbols, chrSymbols) + 1;

            return castSlice(strSymbols, start, end).join('');
        }

        function trimEnd(string, chars, guard) {
            string = toString(string);
            if (string && (guard || chars === undefined)) {
                return string.replace(reTrimEnd, '');
            }
            if (!string || !(chars = baseToString(chars))) {
                return string;
            }
            var strSymbols = stringToArray(string),
                end = charsEndIndex(strSymbols, stringToArray(chars)) + 1;

            return castSlice(strSymbols, 0, end).join('');
        }

        function trimStart(string, chars, guard) {
            string = toString(string);
            if (string && (guard || chars === undefined)) {
                return string.replace(reTrimStart, '');
            }
            if (!string || !(chars = baseToString(chars))) {
                return string;
            }
            var strSymbols = stringToArray(string),
                start = charsStartIndex(strSymbols, stringToArray(chars));

            return castSlice(strSymbols, start).join('');
        }

        function truncate(string, options) {
            var length = DEFAULT_TRUNC_LENGTH,
                omission = DEFAULT_TRUNC_OMISSION;

            if (isObject(options)) {
                var separator = 'separator' in options ? options.separator : separator;
                length = 'length' in options ? toInteger(options.length) : length;
                omission = 'omission' in options ? baseToString(options.omission) : omission;
            }
            string = toString(string);

            var strLength = string.length;
            if (hasUnicode(string)) {
                var strSymbols = stringToArray(string);
                strLength = strSymbols.length;
            }
            if (length >= strLength) {
                return string;
            }
            var end = length - stringSize(omission);
            if (end < 1) {
                return omission;
            }
            var result = strSymbols
                ? castSlice(strSymbols, 0, end).join('')
                : string.slice(0, end);

            if (separator === undefined) {
                return result + omission;
            }
            if (strSymbols) {
                end += (result.length - end);
            }
            if (isRegExp(separator)) {
                if (string.slice(end).search(separator)) {
                    var match,
                        substring = result;

                    if (!separator.global) {
                        separator = RegExp(separator.source, toString(reFlags.exec(separator)) + 'g');
                    }
                    separator.lastIndex = 0;
                    while ((match = separator.exec(substring))) {
                        var newEnd = match.index;
                    }
                    result = result.slice(0, newEnd === undefined ? end : newEnd);
                }
            } else if (string.indexOf(baseToString(separator), end) != end) {
                var index = result.lastIndexOf(separator);
                if (index > -1) {
                    result = result.slice(0, index);
                }
            }
            return result + omission;
        }

        function unescape(string) {
            string = toString(string);
            return (string && reHasEscapedHtml.test(string))
                ? string.replace(reEscapedHtml, unescapeHtmlChar)
                : string;
        }

        var upperCase = createCompounder(function(result, word, index) {
            return result + (index ? ' ' : '') + word.toUpperCase();
        });

        var upperFirst = createCaseFirst('toUpperCase');

        function words(string, pattern, guard) {
            string = toString(string);
            pattern = guard ? undefined : pattern;

            if (pattern === undefined) {
                return hasUnicodeWord(string) ? unicodeWords(string) : asciiWords(string);
            }
            return string.match(pattern) || [];
        }


        var attempt = baseRest(function(func, args) {
            try {
                return apply(func, undefined, args);
            } catch (e) {
                return isError(e) ? e : new Error(e);
            }
        });

        var bindAll = flatRest(function(object, methodNames) {
            arrayEach(methodNames, function(key) {
                key = toKey(key);
                baseAssignValue(object, key, bind(object[key], object));
            });
            return object;
        });

        function cond(pairs) {
            var length = pairs == null ? 0 : pairs.length,
                toIteratee = getIteratee();

            pairs = !length ? [] : arrayMap(pairs, function(pair) {
                    if (typeof pair[1] != 'function') {
                        throw new TypeError(FUNC_ERROR_TEXT);
                    }
                    return [toIteratee(pair[0]), pair[1]];
                });

            return baseRest(function(args) {
                var index = -1;
                while (++index < length) {
                    var pair = pairs[index];
                    if (apply(pair[0], this, args)) {
                        return apply(pair[1], this, args);
                    }
                }
            });
        }

        function conforms(source) {
            return baseConforms(baseClone(source, CLONE_DEEP_FLAG));
        }

        function constant(value) {
            return function() {
                return value;
            };
        }

        function defaultTo(value, defaultValue) {
            return (value == null || value !== value) ? defaultValue : value;
        }

        var flow = createFlow();

        var flowRight = createFlow(true);

        function identity(value) {
            return value;
        }

        function iteratee(func) {
            return baseIteratee(typeof func == 'function' ? func : baseClone(func, CLONE_DEEP_FLAG));
        }

        function matches(source) {
            return baseMatches(baseClone(source, CLONE_DEEP_FLAG));
        }

        function matchesProperty(path, srcValue) {
            return baseMatchesProperty(path, baseClone(srcValue, CLONE_DEEP_FLAG));
        }

        var method = baseRest(function(path, args) {
            return function(object) {
                return baseInvoke(object, path, args);
            };
        });

        var methodOf = baseRest(function(object, args) {
            return function(path) {
                return baseInvoke(object, path, args);
            };
        });

        function mixin(object, source, options) {
            var props = keys(source),
                methodNames = baseFunctions(source, props);

            if (options == null &&
                !(isObject(source) && (methodNames.length || !props.length))) {
                options = source;
                source = object;
                object = this;
                methodNames = baseFunctions(source, keys(source));
            }
            var chain = !(isObject(options) && 'chain' in options) || !!options.chain,
                isFunc = isFunction(object);

            arrayEach(methodNames, function(methodName) {
                var func = source[methodName];
                object[methodName] = func;
                if (isFunc) {
                    object.prototype[methodName] = function() {
                        var chainAll = this.__chain__;
                        if (chain || chainAll) {
                            var result = object(this.__wrapped__),
                                actions = result.__actions__ = copyArray(this.__actions__);

                            actions.push({ 'func': func, 'args': arguments, 'thisArg': object });
                            result.__chain__ = chainAll;
                            return result;
                        }
                        return func.apply(object, arrayPush([this.value()], arguments));
                    };
                }
            });

            return object;
        }

        function noConflict() {
            if (root._ === this) {
                root._ = oldDash;
            }
            return this;
        }

        function noop() {
        }

        function nthArg(n) {
            n = toInteger(n);
            return baseRest(function(args) {
                return baseNth(args, n);
            });
        }

        var over = createOver(arrayMap);

        var overEvery = createOver(arrayEvery);

        var overSome = createOver(arraySome);

        function property(path) {
            return isKey(path) ? baseProperty(toKey(path)) : basePropertyDeep(path);
        }

        function propertyOf(object) {
            return function(path) {
                return object == null ? undefined : baseGet(object, path);
            };
        }

        var range = createRange();

        var rangeRight = createRange(true);

        function stubArray() {
            return [];
        }

        function stubFalse() {
            return false;
        }

        function stubObject() {
            return {};
        }

        function stubString() {
            return '';
        }

        function stubTrue() {
            return true;
        }

        function times(n, iteratee) {
            n = toInteger(n);
            if (n < 1 || n > MAX_SAFE_INTEGER) {
                return [];
            }
            var index = MAX_ARRAY_LENGTH,
                length = nativeMin(n, MAX_ARRAY_LENGTH);

            iteratee = getIteratee(iteratee);
            n -= MAX_ARRAY_LENGTH;

            var result = baseTimes(length, iteratee);
            while (++index < n) {
                iteratee(index);
            }
            return result;
        }

        function toPath(value) {
            if (isArray(value)) {
                return arrayMap(value, toKey);
            }
            return isSymbol(value) ? [value] : copyArray(stringToPath(toString(value)));
        }

        function uniqueId(prefix) {
            var id = ++idCounter;
            return toString(prefix) + id;
        }


        var add = createMathOperation(function(augend, addend) {
            return augend + addend;
        }, 0);

        var ceil = createRound('ceil');

        var divide = createMathOperation(function(dividend, divisor) {
            return dividend / divisor;
        }, 1);

        var floor = createRound('floor');

        function max(array) {
            return (array && array.length)
                ? baseExtremum(array, identity, baseGt)
                : undefined;
        }

        function maxBy(array, iteratee) {
            return (array && array.length)
                ? baseExtremum(array, getIteratee(iteratee, 2), baseGt)
                : undefined;
        }

        function mean(array) {
            return baseMean(array, identity);
        }

        function meanBy(array, iteratee) {
            return baseMean(array, getIteratee(iteratee, 2));
        }

        function min(array) {
            return (array && array.length)
                ? baseExtremum(array, identity, baseLt)
                : undefined;
        }

        function minBy(array, iteratee) {
            return (array && array.length)
                ? baseExtremum(array, getIteratee(iteratee, 2), baseLt)
                : undefined;
        }

        var multiply = createMathOperation(function(multiplier, multiplicand) {
            return multiplier * multiplicand;
        }, 1);

        var round = createRound('round');

        var subtract = createMathOperation(function(minuend, subtrahend) {
            return minuend - subtrahend;
        }, 0);

        function sum(array) {
            return (array && array.length)
                ? baseSum(array, identity)
                : 0;
        }

        function sumBy(array, iteratee) {
            return (array && array.length)
                ? baseSum(array, getIteratee(iteratee, 2))
                : 0;
        }


        lodash.after = after;
        lodash.ary = ary;
        lodash.assign = assign;
        lodash.assignIn = assignIn;
        lodash.assignInWith = assignInWith;
        lodash.assignWith = assignWith;
        lodash.at = at;
        lodash.before = before;
        lodash.bind = bind;
        lodash.bindAll = bindAll;
        lodash.bindKey = bindKey;
        lodash.castArray = castArray;
        lodash.chain = chain;
        lodash.chunk = chunk;
        lodash.compact = compact;
        lodash.concat = concat;
        lodash.cond = cond;
        lodash.conforms = conforms;
        lodash.constant = constant;
        lodash.countBy = countBy;
        lodash.create = create;
        lodash.curry = curry;
        lodash.curryRight = curryRight;
        lodash.debounce = debounce;
        lodash.defaults = defaults;
        lodash.defaultsDeep = defaultsDeep;
        lodash.defer = defer;
        lodash.delay = delay;
        lodash.difference = difference;
        lodash.differenceBy = differenceBy;
        lodash.differenceWith = differenceWith;
        lodash.drop = drop;
        lodash.dropRight = dropRight;
        lodash.dropRightWhile = dropRightWhile;
        lodash.dropWhile = dropWhile;
        lodash.fill = fill;
        lodash.filter = filter;
        lodash.flatMap = flatMap;
        lodash.flatMapDeep = flatMapDeep;
        lodash.flatMapDepth = flatMapDepth;
        lodash.flatten = flatten;
        lodash.flattenDeep = flattenDeep;
        lodash.flattenDepth = flattenDepth;
        lodash.flip = flip;
        lodash.flow = flow;
        lodash.flowRight = flowRight;
        lodash.fromPairs = fromPairs;
        lodash.functions = functions;
        lodash.functionsIn = functionsIn;
        lodash.groupBy = groupBy;
        lodash.initial = initial;
        lodash.intersection = intersection;
        lodash.intersectionBy = intersectionBy;
        lodash.intersectionWith = intersectionWith;
        lodash.invert = invert;
        lodash.invertBy = invertBy;
        lodash.invokeMap = invokeMap;
        lodash.iteratee = iteratee;
        lodash.keyBy = keyBy;
        lodash.keys = keys;
        lodash.keysIn = keysIn;
        lodash.map = map;
        lodash.mapKeys = mapKeys;
        lodash.mapValues = mapValues;
        lodash.matches = matches;
        lodash.matchesProperty = matchesProperty;
        lodash.memoize = memoize;
        lodash.merge = merge;
        lodash.mergeWith = mergeWith;
        lodash.method = method;
        lodash.methodOf = methodOf;
        lodash.mixin = mixin;
        lodash.negate = negate;
        lodash.nthArg = nthArg;
        lodash.omit = omit;
        lodash.omitBy = omitBy;
        lodash.once = once;
        lodash.orderBy = orderBy;
        lodash.over = over;
        lodash.overArgs = overArgs;
        lodash.overEvery = overEvery;
        lodash.overSome = overSome;
        lodash.partial = partial;
        lodash.partialRight = partialRight;
        lodash.partition = partition;
        lodash.pick = pick;
        lodash.pickBy = pickBy;
        lodash.property = property;
        lodash.propertyOf = propertyOf;
        lodash.pull = pull;
        lodash.pullAll = pullAll;
        lodash.pullAllBy = pullAllBy;
        lodash.pullAllWith = pullAllWith;
        lodash.pullAt = pullAt;
        lodash.range = range;
        lodash.rangeRight = rangeRight;
        lodash.rearg = rearg;
        lodash.reject = reject;
        lodash.remove = remove;
        lodash.rest = rest;
        lodash.reverse = reverse;
        lodash.sampleSize = sampleSize;
        lodash.set = set;
        lodash.setWith = setWith;
        lodash.shuffle = shuffle;
        lodash.slice = slice;
        lodash.sortBy = sortBy;
        lodash.sortedUniq = sortedUniq;
        lodash.sortedUniqBy = sortedUniqBy;
        lodash.split = split;
        lodash.spread = spread;
        lodash.tail = tail;
        lodash.take = take;
        lodash.takeRight = takeRight;
        lodash.takeRightWhile = takeRightWhile;
        lodash.takeWhile = takeWhile;
        lodash.tap = tap;
        lodash.throttle = throttle;
        lodash.thru = thru;
        lodash.toArray = toArray;
        lodash.toPairs = toPairs;
        lodash.toPairsIn = toPairsIn;
        lodash.toPath = toPath;
        lodash.toPlainObject = toPlainObject;
        lodash.transform = transform;
        lodash.unary = unary;
        lodash.union = union;
        lodash.unionBy = unionBy;
        lodash.unionWith = unionWith;
        lodash.uniq = uniq;
        lodash.uniqBy = uniqBy;
        lodash.uniqWith = uniqWith;
        lodash.unset = unset;
        lodash.unzip = unzip;
        lodash.unzipWith = unzipWith;
        lodash.update = update;
        lodash.updateWith = updateWith;
        lodash.values = values;
        lodash.valuesIn = valuesIn;
        lodash.without = without;
        lodash.words = words;
        lodash.wrap = wrap;
        lodash.xor = xor;
        lodash.xorBy = xorBy;
        lodash.xorWith = xorWith;
        lodash.zip = zip;
        lodash.zipObject = zipObject;
        lodash.zipObjectDeep = zipObjectDeep;
        lodash.zipWith = zipWith;

        lodash.entries = toPairs;
        lodash.entriesIn = toPairsIn;
        lodash.extend = assignIn;
        lodash.extendWith = assignInWith;

        mixin(lodash, lodash);


        lodash.add = add;
        lodash.attempt = attempt;
        lodash.camelCase = camelCase;
        lodash.capitalize = capitalize;
        lodash.ceil = ceil;
        lodash.clamp = clamp;
        lodash.clone = clone;
        lodash.cloneDeep = cloneDeep;
        lodash.cloneDeepWith = cloneDeepWith;
        lodash.cloneWith = cloneWith;
        lodash.conformsTo = conformsTo;
        lodash.deburr = deburr;
        lodash.defaultTo = defaultTo;
        lodash.divide = divide;
        lodash.endsWith = endsWith;
        lodash.eq = eq;
        lodash.escape = escape;
        lodash.escapeRegExp = escapeRegExp;
        lodash.every = every;
        lodash.find = find;
        lodash.findIndex = findIndex;
        lodash.findKey = findKey;
        lodash.findLast = findLast;
        lodash.findLastIndex = findLastIndex;
        lodash.findLastKey = findLastKey;
        lodash.floor = floor;
        lodash.forEach = forEach;
        lodash.forEachRight = forEachRight;
        lodash.forIn = forIn;
        lodash.forInRight = forInRight;
        lodash.forOwn = forOwn;
        lodash.forOwnRight = forOwnRight;
        lodash.get = get;
        lodash.gt = gt;
        lodash.gte = gte;
        lodash.has = has;
        lodash.hasIn = hasIn;
        lodash.head = head;
        lodash.identity = identity;
        lodash.includes = includes;
        lodash.indexOf = indexOf;
        lodash.inRange = inRange;
        lodash.invoke = invoke;
        lodash.isArguments = isArguments;
        lodash.isArray = isArray;
        lodash.isArrayBuffer = isArrayBuffer;
        lodash.isArrayLike = isArrayLike;
        lodash.isArrayLikeObject = isArrayLikeObject;
        lodash.isBoolean = isBoolean;
        lodash.isBuffer = isBuffer;
        lodash.isDate = isDate;
        lodash.isElement = isElement;
        lodash.isEmpty = isEmpty;
        lodash.isEqual = isEqual;
        lodash.isEqualWith = isEqualWith;
        lodash.isError = isError;
        lodash.isFinite = isFinite;
        lodash.isFunction = isFunction;
        lodash.isInteger = isInteger;
        lodash.isLength = isLength;
        lodash.isMap = isMap;
        lodash.isMatch = isMatch;
        lodash.isMatchWith = isMatchWith;
        lodash.isNaN = isNaN;
        lodash.isNative = isNative;
        lodash.isNil = isNil;
        lodash.isNull = isNull;
        lodash.isNumber = isNumber;
        lodash.isObject = isObject;
        lodash.isObjectLike = isObjectLike;
        lodash.isPlainObject = isPlainObject;
        lodash.isRegExp = isRegExp;
        lodash.isSafeInteger = isSafeInteger;
        lodash.isSet = isSet;
        lodash.isString = isString;
        lodash.isSymbol = isSymbol;
        lodash.isTypedArray = isTypedArray;
        lodash.isUndefined = isUndefined;
        lodash.isWeakMap = isWeakMap;
        lodash.isWeakSet = isWeakSet;
        lodash.join = join;
        lodash.kebabCase = kebabCase;
        lodash.last = last;
        lodash.lastIndexOf = lastIndexOf;
        lodash.lowerCase = lowerCase;
        lodash.lowerFirst = lowerFirst;
        lodash.lt = lt;
        lodash.lte = lte;
        lodash.max = max;
        lodash.maxBy = maxBy;
        lodash.mean = mean;
        lodash.meanBy = meanBy;
        lodash.min = min;
        lodash.minBy = minBy;
        lodash.stubArray = stubArray;
        lodash.stubFalse = stubFalse;
        lodash.stubObject = stubObject;
        lodash.stubString = stubString;
        lodash.stubTrue = stubTrue;
        lodash.multiply = multiply;
        lodash.nth = nth;
        lodash.noConflict = noConflict;
        lodash.noop = noop;
        lodash.now = now;
        lodash.pad = pad;
        lodash.padEnd = padEnd;
        lodash.padStart = padStart;
        lodash.parseInt = parseInt;
        lodash.random = random;
        lodash.reduce = reduce;
        lodash.reduceRight = reduceRight;
        lodash.repeat = repeat;
        lodash.replace = replace;
        lodash.result = result;
        lodash.round = round;
        lodash.runInContext = runInContext;
        lodash.sample = sample;
        lodash.size = size;
        lodash.snakeCase = snakeCase;
        lodash.some = some;
        lodash.sortedIndex = sortedIndex;
        lodash.sortedIndexBy = sortedIndexBy;
        lodash.sortedIndexOf = sortedIndexOf;
        lodash.sortedLastIndex = sortedLastIndex;
        lodash.sortedLastIndexBy = sortedLastIndexBy;
        lodash.sortedLastIndexOf = sortedLastIndexOf;
        lodash.startCase = startCase;
        lodash.startsWith = startsWith;
        lodash.subtract = subtract;
        lodash.sum = sum;
        lodash.sumBy = sumBy;
        lodash.template = template;
        lodash.times = times;
        lodash.toFinite = toFinite;
        lodash.toInteger = toInteger;
        lodash.toLength = toLength;
        lodash.toLower = toLower;
        lodash.toNumber = toNumber;
        lodash.toSafeInteger = toSafeInteger;
        lodash.toString = toString;
        lodash.toUpper = toUpper;
        lodash.trim = trim;
        lodash.trimEnd = trimEnd;
        lodash.trimStart = trimStart;
        lodash.truncate = truncate;
        lodash.unescape = unescape;
        lodash.uniqueId = uniqueId;
        lodash.upperCase = upperCase;
        lodash.upperFirst = upperFirst;

        lodash.each = forEach;
        lodash.eachRight = forEachRight;
        lodash.first = head;

        mixin(lodash, (function() {
            var source = {};
            baseForOwn(lodash, function(func, methodName) {
                if (!hasOwnProperty.call(lodash.prototype, methodName)) {
                    source[methodName] = func;
                }
            });
            return source;
        }()), { 'chain': false });


        lodash.VERSION = VERSION;

        arrayEach(['bind', 'bindKey', 'curry', 'curryRight', 'partial', 'partialRight'], function(methodName) {
            lodash[methodName].placeholder = lodash;
        });

        arrayEach(['drop', 'take'], function(methodName, index) {
            LazyWrapper.prototype[methodName] = function(n) {
                n = n === undefined ? 1 : nativeMax(toInteger(n), 0);

                var result = (this.__filtered__ && !index)
                    ? new LazyWrapper(this)
                    : this.clone();

                if (result.__filtered__) {
                    result.__takeCount__ = nativeMin(n, result.__takeCount__);
                } else {
                    result.__views__.push({
                        'size': nativeMin(n, MAX_ARRAY_LENGTH),
                        'type': methodName + (result.__dir__ < 0 ? 'Right' : '')
                    });
                }
                return result;
            };

            LazyWrapper.prototype[methodName + 'Right'] = function(n) {
                return this.reverse()[methodName](n).reverse();
            };
        });

        arrayEach(['filter', 'map', 'takeWhile'], function(methodName, index) {
            var type = index + 1,
                isFilter = type == LAZY_FILTER_FLAG || type == LAZY_WHILE_FLAG;

            LazyWrapper.prototype[methodName] = function(iteratee) {
                var result = this.clone();
                result.__iteratees__.push({
                    'iteratee': getIteratee(iteratee, 3),
                    'type': type
                });
                result.__filtered__ = result.__filtered__ || isFilter;
                return result;
            };
        });

        arrayEach(['head', 'last'], function(methodName, index) {
            var takeName = 'take' + (index ? 'Right' : '');

            LazyWrapper.prototype[methodName] = function() {
                return this[takeName](1).value()[0];
            };
        });

        arrayEach(['initial', 'tail'], function(methodName, index) {
            var dropName = 'drop' + (index ? '' : 'Right');

            LazyWrapper.prototype[methodName] = function() {
                return this.__filtered__ ? new LazyWrapper(this) : this[dropName](1);
            };
        });

        LazyWrapper.prototype.compact = function() {
            return this.filter(identity);
        };

        LazyWrapper.prototype.find = function(predicate) {
            return this.filter(predicate).head();
        };

        LazyWrapper.prototype.findLast = function(predicate) {
            return this.reverse().find(predicate);
        };

        LazyWrapper.prototype.invokeMap = baseRest(function(path, args) {
            if (typeof path == 'function') {
                return new LazyWrapper(this);
            }
            return this.map(function(value) {
                return baseInvoke(value, path, args);
            });
        });

        LazyWrapper.prototype.reject = function(predicate) {
            return this.filter(negate(getIteratee(predicate)));
        };

        LazyWrapper.prototype.slice = function(start, end) {
            start = toInteger(start);

            var result = this;
            if (result.__filtered__ && (start > 0 || end < 0)) {
                return new LazyWrapper(result);
            }
            if (start < 0) {
                result = result.takeRight(-start);
            } else if (start) {
                result = result.drop(start);
            }
            if (end !== undefined) {
                end = toInteger(end);
                result = end < 0 ? result.dropRight(-end) : result.take(end - start);
            }
            return result;
        };

        LazyWrapper.prototype.takeRightWhile = function(predicate) {
            return this.reverse().takeWhile(predicate).reverse();
        };

        LazyWrapper.prototype.toArray = function() {
            return this.take(MAX_ARRAY_LENGTH);
        };

        baseForOwn(LazyWrapper.prototype, function(func, methodName) {
            var checkIteratee = /^(?:filter|find|map|reject)|While$/.test(methodName),
                isTaker = /^(?:head|last)$/.test(methodName),
                lodashFunc = lodash[isTaker ? ('take' + (methodName == 'last' ? 'Right' : '')) : methodName],
                retUnwrapped = isTaker || /^find/.test(methodName);

            if (!lodashFunc) {
                return;
            }
            lodash.prototype[methodName] = function() {
                var value = this.__wrapped__,
                    args = isTaker ? [1] : arguments,
                    isLazy = value instanceof LazyWrapper,
                    iteratee = args[0],
                    useLazy = isLazy || isArray(value);

                var interceptor = function(value) {
                    var result = lodashFunc.apply(lodash, arrayPush([value], args));
                    return (isTaker && chainAll) ? result[0] : result;
                };

                if (useLazy && checkIteratee && typeof iteratee == 'function' && iteratee.length != 1) {
                    isLazy = useLazy = false;
                }
                var chainAll = this.__chain__,
                    isHybrid = !!this.__actions__.length,
                    isUnwrapped = retUnwrapped && !chainAll,
                    onlyLazy = isLazy && !isHybrid;

                if (!retUnwrapped && useLazy) {
                    value = onlyLazy ? value : new LazyWrapper(this);
                    var result = func.apply(value, args);
                    result.__actions__.push({ 'func': thru, 'args': [interceptor], 'thisArg': undefined });
                    return new LodashWrapper(result, chainAll);
                }
                if (isUnwrapped && onlyLazy) {
                    return func.apply(this, args);
                }
                result = this.thru(interceptor);
                return isUnwrapped ? (isTaker ? result.value()[0] : result.value()) : result;
            };
        });

        arrayEach(['pop', 'push', 'shift', 'sort', 'splice', 'unshift'], function(methodName) {
            var func = arrayProto[methodName],
                chainName = /^(?:push|sort|unshift)$/.test(methodName) ? 'tap' : 'thru',
                retUnwrapped = /^(?:pop|shift)$/.test(methodName);

            lodash.prototype[methodName] = function() {
                var args = arguments;
                if (retUnwrapped && !this.__chain__) {
                    var value = this.value();
                    return func.apply(isArray(value) ? value : [], args);
                }
                return this[chainName](function(value) {
                    return func.apply(isArray(value) ? value : [], args);
                });
            };
        });

        baseForOwn(LazyWrapper.prototype, function(func, methodName) {
            var lodashFunc = lodash[methodName];
            if (lodashFunc) {
                var key = (lodashFunc.name + ''),
                    names = realNames[key] || (realNames[key] = []);

                names.push({ 'name': methodName, 'func': lodashFunc });
            }
        });

        realNames[createHybrid(undefined, WRAP_BIND_KEY_FLAG).name] = [{
            'name': 'wrapper',
            'func': undefined
        }];

        LazyWrapper.prototype.clone = lazyClone;
        LazyWrapper.prototype.reverse = lazyReverse;
        LazyWrapper.prototype.value = lazyValue;

        lodash.prototype.at = wrapperAt;
        lodash.prototype.chain = wrapperChain;
        lodash.prototype.commit = wrapperCommit;
        lodash.prototype.next = wrapperNext;
        lodash.prototype.plant = wrapperPlant;
        lodash.prototype.reverse = wrapperReverse;
        lodash.prototype.toJSON = lodash.prototype.valueOf = lodash.prototype.value = wrapperValue;

        lodash.prototype.first = lodash.prototype.head;

        if (symIterator) {
            lodash.prototype[symIterator] = wrapperToIterator;
        }
        return lodash;
    });


    var _ = runInContext();

    if (freeModule) {
        (freeModule.exports = _)._ = _;
        freeExports._ = _;
    }
    else {
        root._ = _;
    }
}.call(this));

!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.AOS=t():e.AOS=t()}(this,function(){return function(e){function t(n){if(o[n])return o[n].exports;var i=o[n]={exports:{},id:n,loaded:!1};return e[n].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}var o={};return t.m=e,t.c=o,t.p="dist/",t(0)}([function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}var i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},a=o(1),r=(n(a),o(5)),c=n(r),u=o(6),s=n(u),d=o(7),f=n(d),l=o(8),m=n(l),p=o(9),b=n(p),v=o(10),g=n(v),y=o(13),w=n(y),h=[],k=!1,x=document.all&&!window.atob,j={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded"},O=function(){var e=arguments.length<=0||void 0===arguments[0]?!1:arguments[0];return e&&(k=!0),k?(h=(0,g["default"])(h,j),(0,b["default"])(h,j.once),h):void 0},_=function(){h=(0,w["default"])(),O()},z=function(){h.forEach(function(e,t){e.node.removeAttribute("data-aos"),e.node.removeAttribute("data-aos-easing"),e.node.removeAttribute("data-aos-duration"),e.node.removeAttribute("data-aos-delay")})},A=function(e){return e===!0||"mobile"===e&&m["default"].mobile()||"phone"===e&&m["default"].phone()||"tablet"===e&&m["default"].tablet()||"function"==typeof e&&e()===!0},E=function(e){return j=i(j,e),h=(0,w["default"])(),A(j.disable)||x?z():(document.querySelector("body").setAttribute("data-aos-easing",j.easing),document.querySelector("body").setAttribute("data-aos-duration",j.duration),document.querySelector("body").setAttribute("data-aos-delay",j.delay),"DOMContentLoaded"===j.startEvent&&["complete","interactive"].indexOf(document.readyState)>-1?O(!0):"load"===j.startEvent?window.addEventListener(j.startEvent,function(){O(!0)}):document.addEventListener(j.startEvent,function(){O(!0)}),window.addEventListener("resize",(0,s["default"])(O,50,!0)),window.addEventListener("orientationchange",(0,s["default"])(O,50,!0)),window.addEventListener("scroll",(0,c["default"])(function(){(0,b["default"])(h,j.once)},99)),document.addEventListener("DOMNodeRemoved",function(e){var t=e.target;t&&1===t.nodeType&&t.hasAttribute&&t.hasAttribute("data-aos")&&(0,s["default"])(_,50,!0)}),(0,f["default"])("[data-aos]",_),h)};e.exports={init:E,refresh:O,refreshHard:_}},function(e,t){},,,,function(e,t,o){"use strict";function n(e,t,o){var n=!0,a=!0;if("function"!=typeof e)throw new TypeError(c);return i(o)&&(n="leading"in o?!!o.leading:n,a="trailing"in o?!!o.trailing:a),r(e,t,{leading:n,maxWait:t,trailing:a})}function i(e){var t="undefined"==typeof e?"undefined":a(e);return!!e&&("object"==t||"function"==t)}var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},r=o(6),c="Expected a function";e.exports=n},function(e,t){"use strict";function o(e,t,o){function n(t){var o=b,n=v;return b=v=void 0,O=t,y=e.apply(n,o)}function a(e){return O=e,w=setTimeout(d,t),_?n(e):y}function r(e){var o=e-h,n=e-O,i=t-o;return z?x(i,g-n):i}function u(e){var o=e-h,n=e-O;return!h||o>=t||0>o||z&&n>=g}function d(){var e=j();return u(e)?f(e):void(w=setTimeout(d,r(e)))}function f(e){return clearTimeout(w),w=void 0,A&&b?n(e):(b=v=void 0,y)}function l(){void 0!==w&&clearTimeout(w),h=O=0,b=v=w=void 0}function m(){return void 0===w?y:f(j())}function p(){var e=j(),o=u(e);if(b=arguments,v=this,h=e,o){if(void 0===w)return a(h);if(z)return clearTimeout(w),w=setTimeout(d,t),n(h)}return void 0===w&&(w=setTimeout(d,t)),y}var b,v,g,y,w,h=0,O=0,_=!1,z=!1,A=!0;if("function"!=typeof e)throw new TypeError(s);return t=c(t)||0,i(o)&&(_=!!o.leading,z="maxWait"in o,g=z?k(c(o.maxWait)||0,t):g,A="trailing"in o?!!o.trailing:A),p.cancel=l,p.flush=m,p}function n(e){var t=i(e)?h.call(e):"";return t==f||t==l}function i(e){var t="undefined"==typeof e?"undefined":u(e);return!!e&&("object"==t||"function"==t)}function a(e){return!!e&&"object"==("undefined"==typeof e?"undefined":u(e))}function r(e){return"symbol"==("undefined"==typeof e?"undefined":u(e))||a(e)&&h.call(e)==m}function c(e){if("number"==typeof e)return e;if(r(e))return d;if(i(e)){var t=n(e.valueOf)?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(p,"");var o=v.test(e);return o||g.test(e)?y(e.slice(2),o?2:8):b.test(e)?d:+e}var u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},s="Expected a function",d=NaN,f="[object Function]",l="[object GeneratorFunction]",m="[object Symbol]",p=/^\s+|\s+$/g,b=/^[-+]0x[0-9a-f]+$/i,v=/^0b[01]+$/i,g=/^0o[0-7]+$/i,y=parseInt,w=Object.prototype,h=w.toString,k=Math.max,x=Math.min,j=Date.now;e.exports=o},function(e,t){"use strict";function o(e,t){r.push({selector:e,fn:t}),!c&&a&&(c=new a(n),c.observe(i.documentElement,{childList:!0,subtree:!0,removedNodes:!0})),n()}function n(){for(var e,t,o=0,n=r.length;n>o;o++){e=r[o],t=i.querySelectorAll(e.selector);for(var a,c=0,u=t.length;u>c;c++)a=t[c],a.ready||(a.ready=!0,e.fn.call(a,a))}}Object.defineProperty(t,"__esModule",{value:!0});var i=window.document,a=window.MutationObserver||window.WebKitMutationObserver,r=[],c=void 0;t["default"]=o},function(e,t){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,o,n){return o&&e(t.prototype,o),n&&e(t,n),t}}(),i=function(){function e(){o(this,e)}return n(e,[{key:"phone",value:function(){var e=!1;return function(t){(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)))&&(e=!0)}(navigator.userAgent||navigator.vendor||window.opera),e}},{key:"mobile",value:function(){var e=!1;return function(t){(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)))&&(e=!0)}(navigator.userAgent||navigator.vendor||window.opera),e}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),e}();t["default"]=new i},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(e,t,o){var n=e.node.getAttribute("data-aos-once");t>e.position?e.node.classList.add("aos-animate"):"undefined"!=typeof n&&("false"===n||!o&&"true"!==n)&&e.node.classList.remove("aos-animate")},n=function(e,t){var n=window.pageYOffset,i=window.innerHeight;e.forEach(function(e,a){o(e,i+n,t)})};t["default"]=n},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=o(11),a=n(i),r=function(e,t){return e.forEach(function(e,o){e.node.classList.add("aos-init"),e.position=(0,a["default"])(e.node,t.offset)}),e};t["default"]=r},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var i=o(12),a=n(i),r=function(e,t){var o=0,n=0,i=window.innerHeight,r={offset:e.getAttribute("data-aos-offset"),anchor:e.getAttribute("data-aos-anchor"),anchorPlacement:e.getAttribute("data-aos-anchor-placement")};switch(r.offset&&!isNaN(r.offset)&&(n=parseInt(r.offset)),r.anchor&&document.querySelectorAll(r.anchor)&&(e=document.querySelectorAll(r.anchor)[0]),o=(0,a["default"])(e).top,r.anchorPlacement){case"top-bottom":break;case"center-bottom":o+=e.offsetHeight/2;break;case"bottom-bottom":o+=e.offsetHeight;break;case"top-center":o+=i/2;break;case"bottom-center":o+=i/2+e.offsetHeight;break;case"center-center":o+=i/2+e.offsetHeight/2;break;case"top-top":o+=i;break;case"bottom-top":o+=e.offsetHeight+i;break;case"center-top":o+=e.offsetHeight/2+i}return r.anchorPlacement||r.offset||isNaN(t)||(n=t),o+n};t["default"]=r},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){for(var t=0,o=0;e&&!isNaN(e.offsetLeft)&&!isNaN(e.offsetTop);)t+=e.offsetLeft-("BODY"!=e.tagName?e.scrollLeft:0),o+=e.offsetTop-("BODY"!=e.tagName?e.scrollTop:0),e=e.offsetParent;return{top:o,left:t}};t["default"]=o},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){e=e||document.querySelectorAll("[data-aos]");var t=[];return[].forEach.call(e,function(e,o){t.push({node:e})}),t};t["default"]=o}])});

(function ($) {
    $(function () {
        var options = {
            classes: {
                active: 'is-active'
            },
            durations: {
                page: {
                    slide: 750 
                },
                bookmark: {
                    slide: 750
                },
                resize: {
                    throttle: 200
                },
                scroll: {
                    throttle: 200
                }
            },
            selectors: {
                bookmarks: {
                    navigation: '[data-type*="bookmark-navigation"]',
                    list: '[data-type*="bookmark-list"]',
                    previous: '[data-type*="bookmark-previous"]',
                    next: '[data-type*="bookmark-next"]'
                }
            }
        };

        var viewport = $(window);
        var viewportTop = viewport.scrollTop();
        var body = $('html, body');
        var navigation = $(options.selectors.bookmarks.navigation);

        if (!navigation.length) return;

        var bookmarkList = navigation.find(options.selectors.bookmarks.list);
        var bookmarkOrder = bookmarkList.find('li');
        var bookmarkLinks = bookmarkList.find('li > a');
        var bookmarkPrev = navigation.find(options.selectors.bookmarks.previous);
        var bookmarkNext = navigation.find(options.selectors.bookmarks.next);
        var bookmarkAnimating = {};
        var bookmarkCurrent = null;
        var details = null;

        viewport.on('load', function () {
            details = getDetails();
            viewportTop = viewport.scrollTop();

            attachEvents();
            toggleNavigation();
            highlightViewportBookmark();
        });

        function attachEvents() {
            viewport.on('scroll', _.throttle(function () {
                viewportTop = viewport.scrollTop();

                toggleNavigation();
                if (!isAnimating()) highlightViewportBookmark();
            }, options.durations.scroll.throttle));

            viewport.on('resize', _.throttle(function () {
                details = getDetails();
            }, options.durations.resize.throttle));

            bookmarkPrev.on('click', function (event) {
                event.preventDefault();

                var index = Math.floor(bookmarkOrder.length / 2) - 1;

                scrollToSection($(bookmarkOrder[index]).data().bookmarkName);
            });

            bookmarkNext.on('click', function (event) {
                event.preventDefault();

                var index = Math.floor(bookmarkOrder.length / 2) + 1;

                scrollToSection($(bookmarkOrder[index]).data().bookmarkName);
            });

            _.each(details, function (item, name) {
                item.bookmark.on('click', function (event) {
                    event.preventDefault();

                    scrollToSection(name);
                });
            });
        }

        function animateToBookmark(name) {
            if (bookmarkCurrent === name) return;

            if (!details[name]) {
                console.error('Unable to find bookmark:', name);
                return;
            }

            var bookmark = details[name].bookmark;
            var bookmarkPosition = bookmarkOrder.index(bookmark);
            var distanceToCenter = Math.floor(bookmarkOrder.length / 2) - bookmarkPosition;
            var distanceCounting = Math.abs(distanceToCenter);
            var animateDirection = distanceToCenter > 0 ? 'right' : 'left';
            var animateSpaceLeft = 0;
            var bookmarkToRemove = [];

            bookmarkList.css('width', bookmarkList.outerWidth());

            bookmarkOrder = animateDirection === 'right' ? _.reverse(bookmarkOrder) : bookmarkOrder;

            _.each(bookmarkOrder, function (item) {
                if (distanceCounting <= 0) return false;

                var element = $(item);
                var clone = element.clone();
                var method = animateDirection === 'right' ? 'prepend' : 'append';

                distanceCounting--;
                animateSpaceLeft += element.outerWidth();
                bookmarkList[method](clone);
                bookmarkToRemove.push({
                    element: element,
                    clone: clone
                });
            });

            updateBookmarkOrder();

            var bookmarkFirst = $(bookmarkOrder[0]);
            var animateDistance = animateDirection === 'right' ? 0 : -animateSpaceLeft;

            if (animateDirection === 'right') {
                bookmarkFirst.css('marginLeft', -animateSpaceLeft);
            }

            bookmarkFirst.animate({
                marginLeft: animateDistance
            }, {
                duration: options.durations.bookmark.slide,
                start: function () {
                    bookmarkAnimating[name] = true;
                },
                always: function () {
                    bookmarkAnimating[name] = false;

                    _.each(bookmarkToRemove, function (item) {
                        item.clone.replaceWith(item.element);
                    });

                    bookmarkFirst.css('marginLeft', '');
                    bookmarkList.css('width', '');
                    updateBookmarkOrder();
                }
            });
        }

        function getDetails() {
            return _.reduce(bookmarkLinks, function (object, item) {
                var bookmark = $(item);
                var bookmarkContainer = bookmark.parent('li');
                var selector = bookmark.attr('href');
                var name = selector.replace('#', '');

                if (!selector || !name) {
                    console.error('Unable to find section in bookmark href:', bookmark);
                    return;
                }

                var section = $(selector);

                if (!section.length) {
                    console.error('Unable to find section:', selector);
                    return;
                }

                bookmarkContainer.attr('data-bookmark-name', name);

                object[name] = {
                    bookmark: bookmarkContainer,
                    trigger: getTriggers(section)
                };

                return object;
            }, {});
        }

        function highlightBookmark(name) {
            if (!details[name]) {
                console.error('Unable to find bookmark:', name);
                return;
            }

            bookmarkOrder.removeClass(options.classes.active);
            details[name].bookmark.addClass(options.classes.active);

            if (!isAnimating()) animateToBookmark(name);

            bookmarkCurrent = name;
        }

        function highlightViewportBookmark() {
            var target = null;

            _.each(details, function (item, name) {
                var trigger = item.trigger;

                if (trigger.top <= viewportTop && trigger.bottom >= viewportTop) {
                    target = name;
                }
            });

						updateBookmarkOrder();

            if (target && target != bookmarkCurrent) {
                highlightBookmark(target);
            }
        }

        function scrollToSection(name) {
            if (!details[name]) {
                console.error('Unable to find section:', name);
                return;
            }

            var trigger = details[name].trigger;

            body.animate({
                scrollTop: trigger.top + 10
            }, {
                duration: options.durations.page.slide,
                start: function () {
                    highlightBookmark(name);
                    bookmarkAnimating[name] = true;
                },
                always: function () {
                    bookmarkAnimating[name] = false;
                }
            });
        }

        function updateBookmarkOrder() {
            bookmarkOrder = bookmarkList.find('li');
        }

        function getTriggers(element) {
            var trigger = element.offset().top - navigation.outerHeight();

            return {
                top: trigger,
                bottom: element.outerHeight() + trigger
            };
        }

        function isAnimating() {
            return _.some(_.values(bookmarkAnimating), function (item) {
                return item;
            });
        }

        function toggleNavigation() {
            var bookmarkTop = _.minBy(_.values(details), 'trigger.top');

            bookmarkTop = bookmarkTop.trigger.top;

            var mainMethod = bookmarkTop <= viewportTop ? 'hide' : 'show';
            var bookMethod = bookmarkTop <= viewportTop ? 'show' : 'hide';

						$('#top-menu')[mainMethod]();

            navigation[bookMethod]();
        }
    });
})(jQuery);

(function ($, document) {
    $(function () {
        var eventEmitter = $(document);

				        var defaults = {
            template: [
			'	<section class="ui-modal" style="display:none;">',
			'   	<section class="ui-modal-box">',
			'			<div class="ui-modal-close" data-modal-close></div>',
			'			<section class="ui-modal-content">',
			'				<header><h1 data-modal-title></h1></header>',
			'				<div class="content" data-modal-content></div>',
			'			</section>',
			'   	</section>',
                '</section>'
            ].join("\n"),
            selectors: {
                close: '[data-modal-close]',
                content: '[data-modal-content]',
                scroller: '[data-modal-scroller]',
                title: '[data-modal-title]'
            }
        };

         var Modal = function (options) {
            this.setOptions(options);
            this.template = $.extend({}, this.template);
        };

         Modal.prototype = {
            initialized: false,
            modals: [],
            options: null,
            timeout: null,
            template: {
                close: null,
                container: null,
                content: null,
                scroller: null,
                title: null
            }
        };

         Modal.prototype.create = function () {
            if (this.initialized) return this;

             this.initialized = true;
            this.modals.push(this);

             var options = this.options;
            var template = $(options.template);

             if (!template.length) {
                console.log('cm.modal: Template not found - "' + options.template + '"');
                return null;
            }

             if (document.contains(template[0])) {
                var hasInnerElement = !!template[0].children.length;
                var original = template;

                 template = hasInnerElement ? $(template[0].children[0]) : $(template[0].innerHTML);

                 if (hasInnerElement) {
					$('body').prepend(template);
                }

                 original.remove();
            }

             var close = template.find(options.selectors.close);

             this.template.close = close;
            this.template.container = template;

             this.template.content = template.find(options.selectors.content);
            this.template.scroller = template.find(options.selectors.scroller);
            this.template.title = template.find(options.selectors.title);

             close.on('click', function (event) {
                event.preventDefault();

                 this.close();
            }.bind(this));

             template.on('click', function (event) {
                var target = $(event.target);
                var parent = target.parent();

                 if (!template[0].contains(parent[0])) {
                    this.close();
                }
            }.bind(this));

             if (!document.contains(template[0])) {
				$('body').prepend(template);
            }

             eventEmitter.trigger('cm.modal.init', this);

             return this;
        };

         Modal.prototype.open = function (data) {
            if (!this.create()) return;

             $.each(this.modals, function (key,item) {
                if (item != this) {
                    item.close();
                }
            }.bind(this));

             var details = $.extend({content: null, title: null}, data);

             this.setContent(details.content);
            this.setTitle(details.title);

             this.template.container.show();
            this.template.scroller.css('height', this.template.scroller.parent().height());

             eventEmitter.trigger('cm.modal.open', this);

             return this;
        };

         Modal.prototype.close = function () {
            this.template.container.hide();
            this.template.scroller.css('height', 'auto');

            eventEmitter.trigger('cm.modal.close', this);

 			return this;
        };

         Modal.prototype.setData = function (element, data) {
            if (!data) return;

             element.empty();

             if (data[0] instanceof HTMLElement) {
                var hasInnerElement = !!data[0].children.length;
                var template = hasInnerElement ? $(data[0].children[0])[0] : $(data[0].innerHTML)[0];

                 if (!template) {
                    template = document.createTextNode(data[0].innerHTML);
                }

                 element[0].appendChild(template);
            } else {
                element.html(data);
            }

             return this;
        };

         Modal.prototype.setContent = function (data) {
            this.setData(this.template.content, data);
            return this;
        };

         Modal.prototype.setOptions = function (options) {
            this.options = $.extend({}, defaults, options);
            return this;
        };

         Modal.prototype.setTitle = function (data) {
            this.setData(this.template.title, data);
            return this;
        };


        var modals = $('[data-type~="cm-modal"]');

         if (modals.length) {
            $.each(modals, function (key,item) {
                var element = $(item);
                var data = element.data();

                 var options = {};
                var title = data.modalTitle ? $(data.modalTitle) : null;
				var content = data.modalTarget ? $('[data-modal-section="' + data.modalTarget + '"]') : null;

                 if (data.modalTemplate) {
                    options.template = data.modalTemplate;
                }

                 var modal = new Modal(options).create();
                var initialized = false;

                element.on('click', function (event) {
                    if (data.modalPreventDefault) event.preventDefault();

                     if (!initialized) {
                        modal.setTitle(title);
                        modal.setContent(content);
                        initialized = true;
                    }

                     modal.open();
                });
            });
        }

        eventEmitter.on('cm.modal.close', function () {
            $('.video-iframe').each(function () {
                this.contentWindow.postMessage('{"event":"command","func":"' + 'stopVideo' + '","args":""}', '*');
            });
        });

        if (typeof window !== 'undefined') {
            window = $.extend(window, {cm: {modal: Modal}});
        }

        if (typeof define !== 'undefined') {
            define('cm-modal', [], function() {
                return Modal;
            });
        }

        if (typeof module !== 'undefined') {
            module.exports = Modal;
        }
    });
})(jQuery, document);

(function () {
    $(function () {
        var toggles = $('[data-type~="cm-tab"]');



                        _.each(toggles, function (value) {
            var element = $(value);
            var data = element.data();

                        if (!data.target) {
                console.log('cm.toggle[error]', 'data-target must be defined', element);
                return;
            }

                        var panels = $('[data-section]');
            var target = $('[data-section~="' + data.target + '"]');

                                if (!target.length) {
                console.log('cm.toggle[error]', 'could not find target data-section="' + data.target + '"', element);
            }

            element.on('click', function (event) {
                event.preventDefault();

                                toggles.removeClass('is-open');
                panels.removeClass('is-open');
                element.addClass('is-open');
                target.addClass('is-open');

            });
        });
    });
})();
(function () {
    $(function () {
        var toggles = $('[data-type~="cm-toggle"]');

        _.each(toggles, function (value) {
            var element = $(value);
            var data = element.data();

            if (!data.target) {
                console.log('cm.toggle[error]', 'data-target must be defined', element);
                return;
            }

            var target = $('[data-section~="' + data.target + '"]');

            if (!target.length) {
                console.log('cm.toggle[error]', 'could not find target data-section="' + data.target + '"', element);
            }

            element.on('click', function (event) {
                event.preventDefault();

                if (element.hasClass('is-open')) {
                    element.removeClass('is-open');
                    target.removeClass('is-open');
                } else {
                    element.addClass('is-open');
                    target.addClass('is-open');
                }
            });
        });
    });
})();
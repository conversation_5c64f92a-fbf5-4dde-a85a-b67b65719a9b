@charset "UTF-8";
@font-face {
  font-family: 'glyphs';
  src: url("/build/fonts/glyphs.eot");
  src: url("/build/fonts/glyphs.eot") format("embedded-opentype"), url("/build/fonts/glyphs.ttf") format("truetype"), url("/build/fonts/glyphs.woff") format("woff"), url("/build/fonts/glyphs.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}

/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  line-height: 1;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

a:hover, a:active, a:focus {
  outline: 0;
}

.bg-primary {
  background-color: #5d9bfc;
}

.bg-primary-lightest {
  background-color: #a8c9fc;
}

.bg-primary-lighter {
  background-color: #80b2ff;
}

.bg-primary-light {
  background-color: #6da7ff;
}

.bg-primary-medium {
  background-color: #3378e3;
}

.bg-primary-dark {
  background-color: #174bad;
}

.bg-primary-darker {
  background-color: #042271;
}

.bg-primary-darkest {
  background-color: #092a5f;
}

.bg-secondary {
  background-color: #f2f3f6;
}

.bg-accent-one {
  background-color: #686cbe;
}

.bg-accent-one-light-medium {
  background-color: #777bc6;
}

.bg-accent-one-light {
  background-color: #b6b9e6;
}

.bg-accent-one-lighter {
  background-color: #ccd0ee;
}

.bg-accent-one-lightest {
  background-color: #e0e2f5;
}

.bg-accent-one-dark {
  background-color: #565aac;
}

.bg-accent-one-darker {
  background-color: #38398c;
}

.bg-accent-one-darkest {
  background-color: #2d317b;
}

.bg-accent-two {
  background-color: #50a475;
}

.bg-accent-two-light {
  background-color: #67b489;
}

.bg-accent-two-dark {
  background-color: #368157;
}

.bg-accent-two-darker {
  background-color: #317b4e;
}

.bg-accent-three {
  background-color: #79cbe3;
}

.bg-cta-orange {
  background-color: orange;
}

.bg-cta-orange-medium {
  background-color: #e59400;
}

.bg-cta-orange-dark {
  background-color: #d07800;
}

.bg-ui-primary {
  background-color: #c1cfe6;
}

.bg-ui-primary-lighter {
  background-color: #e5eff5;
}

.bg-ui-primary-light {
  background-color: #c8e1f5;
}

.bg-ui-primary-medium {
  background-color: #a7b8d6;
}

.bg-ui-primary-dark {
  background-color: #5d759a;
}

.bg-ui-primary-darker {
  background-color: #304565;
}

.bg-ui-primary-darkest {
  background-color: #28374d;
}

.bg-ui-border {
  background-color: #c1cfe6;
}

.bg-border-ui {
  background-color: #c1cfe6;
}

.bg-ui-border-medium {
  background-color: #b8c8e4;
}

.bg-ui-border-dark {
  background-color: #a7b8d6;
}

.bg-ui-border-light {
  background-color: #d8e1f0;
}

.bg-ui-gradient-top {
  background-color: #f4f8ff;
}

.bg-ui-gradient-top-light {
  background-color: #f5f9ff;
}

.bg-ui-gradient-bottom {
  background-color: #f4f8ff;
}

.bg-ui-gradient-bottom-light {
  background-color: #f4f8ff;
}

.bg-ui-box-bg {
  background-color: #FFF;
}

.bg-ui-bg {
  background-color: #e0e9f9;
}

.bg-ui-bg-light {
  background-color: #e2ebfb;
}

.bg-ui-bg-medium {
  background-color: #c9d7f0;
}

.bg-white {
  background-color: white;
}

.bg-gray-darkest {
  background-color: #1e1e1e;
}

.bg-gray-darker {
  background-color: #343434;
}

.bg-gray-dark {
  background-color: #505050;
}

.bg-gray-medium {
  background-color: #7e7e7e;
}

.bg-gray-light {
  background-color: #bababa;
}

.bg-gray-lighter {
  background-color: #e0e0e0;
}

.bg-gray-lightest {
  background-color: #ededed;
}

.bg-body {
  background-color: #2f313a;
}

.bg-body-ui {
  background-color: #758ba9;
}

.bg-body-light {
  background-color: #747f8a;
}

.bg-body-dark {
  background-color: black;
}

.bg-test {
  background-color: #758BA9;
}

.bg-menu-dark {
  background-color: #1c2c3e;
}

.bg-menu-dark-body {
  background-color: #cfdce2;
}

.bg-menu-dark-hover {
  background-color: #686cbe;
}

.bg-menu-light {
  background-color: #e5eaef;
}

.bg-menu-light-body {
  background-color: #b4c0d1;
}

.bg-menu-light-hover {
  background-color: #80b2ff;
}

.bg-bg-main {
  background-color: #eff5ff;
}

.bg-bg-card-hover {
  background-color: #f5f9ff;
}

.bg-bg-main-light {
  background-color: #f4f8ff;
}

.bg-bg-main-lighter {
  background-color: #fafbff;
}

.bg-bg-main-medium {
  background-color: #d9e6fa;
}

.bg-bg-secondary {
  background-color: #f2f3f6;
}

.bg-alert-error {
  background-color: #e26464;
}

.bg-alert-error-light {
  background-color: #e09898;
}

.bg-alert-warning {
  background-color: #f0d886;
}

.bg-alert-success {
  background-color: #50a475;
}

.bg-alert-success-light {
  background-color: #8bc3a3;
}

.bg-alert-green {
  background-color: #50a475;
}

.bg-alert-information {
  background-color: #80b2ff;
}

.bg-alert-information-light {
  background-color: #80b2ff;
}

.bg-flowchart-condition {
  background-color: #f0f5ff;
}

.bg-flowchart-split-test-1 {
  background-color: #73bcff;
}

.bg-flowchart-split-test-2 {
  background-color: #777bc6;
}

.bg-flowchart-split-test-3 {
  background-color: #67b489;
}

.bg-flowchart-split-test-4 {
  background-color: #ffa21a;
}

.bg-flowchart-split-test-5 {
  background-color: #2cbccb;
}

.bg-flowchart-split-test-6 {
  background-color: #9acc68;
}

.bg-flowchart-split-test-7 {
  background-color: #cb5c5b;
}

.bg-flowchart-split-test-8 {
  background-color: #63a1f5;
}

.bg-flowchart-connection {
  background-color: #d3e0f5;
}

.bg-flowchart-label {
  background-color: #bbceeb;
}

.bg-flowchart-action-modify {
  background-color: #5d9bfc;
}

.bg-flowchart-action-direction {
  background-color: #50a475;
}

.bg-flowchart-action-flow {
  background-color: #7479d3;
}

.bg-input {
  background-color: #f2f3f6;
}

.bg-teal-light {
  background-color: #6ECED9;
}

.bg-teal-dark {
  background-color: #0097AA;
}

.bg-peach-light {
  background-color: #E6AB9D;
}

.bg-peach-dark {
  background-color: #BC5F5F;
}

.bg-pink-light {
  background-color: #E286A6;
}

.bg-pink-dark {
  background-color: #C64873;
}

.bg-violet-light {
  background-color: #8D98D8;
}

.bg-violet-dark {
  background-color: #505EAF;
}

.bg-blue-light {
  background-color: #86B2DE;
}

.bg-blue-dark {
  background-color: #4182D9;
}

.bg-silver-light {
  background-color: #A4B3BA;
}

.bg-silver-dark {
  background-color: #6C818D;
}

.bg-brown-light {
  background-color: #B7A29B;
}

.bg-brown-dark {
  background-color: #9B6F60;
}

.bg-yellow-light {
  background-color: #E5B05C;
}

.bg-yellow-dark {
  background-color: #DE8225;
}

.bg-green-light {
  background-color: #7EAD7A;
}

.bg-green-dark {
  background-color: #367C44;
}

.bg-coldgreen-light {
  background-color: #62A8A0;
}

.bg-coldgreen-dark {
  background-color: #2C7466;
}

/* --------

BASE TYPOGRAPHIC STYLE DEFINITIONS:

These are shared typographic definitions, used in multiple pages and modular components.

SASS extends are to be used to guarantee  consistent typographic treatment.

type-* classes make use of the @extend definitions and can be applied independently to implement those typographic styles as needed.

-------- */
.new-ui .ui-form input[type="text"],
.new-ui .ui-form input[type="search"],
.new-ui .ui-form input[type="password"],
.new-ui .ui-form input[type="email"],
.new-ui .ui-form input[type="phone"],
.new-ui .ui-form input[type="address"],
.new-ui .ui-form input[type="date"],
.new-ui .ui-form input[type="month"],
.new-ui .ui-form input[type="number"],
.new-ui .ui-form input[type="url"],
.new-ui .ui-form input[type="tel"],
.new-ui .ui-form input[type="range"],
.new-ui .ui-form input[type="datetime"],
.new-ui .ui-form input[type="week"],
.new-ui .ui-form textarea, .new-ui .ui-form .ui-button {
  font-family: "Source Sans Pro", "Source Sans Pro", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-size: 16px;
  line-height: 1.45em;
  font-weight: normal;
  color: #2f313a;
}

.type-intro {
  font-size: 1.35rem;
  line-height: 1.5em;
  font-weight: 300;
}

.type-elegant {
  font-size: 1.15rem;
  line-height: 1.35em;
  font-weight: 300;
}

.type-emphasis {
  color: #5d9bfc;
}

.type-note, .type-note-small {
  font-style: italic;
  font-size: 0.85rem;
  line-height: 1.125em;
  font-weight: normal;
  color: #758ba9;
}

.type-strong {
  font-weight: 600;
}

.type-spec {
  font-weight: normal;
  font-size: inherit;
  color: #bababa;
}

.type-ui-label, .company, .new-ui table.table-default thead tr th,
.new-ui .ui-table.table-default thead tr th {
  text-transform: uppercase;
  text-align: center;
  font-weight: normal;
}

.type-ui-label, .company, .new-ui table.table-default thead tr th.strong,
.new-ui .ui-table.table-default thead tr th.strong {
  font-weight: 700;
  letter-spacing: 1px;
}

.type-timestamp,
.timestamp, .type-ui-note {
  margin-top: 0.5rem;
  font-style: italic;
  font-weight: normal;
  font-size: 0.85rem;
  color: #bababa;
}

.type-label {
  color: #5d9bfc;
  margin-bottom: 0;
}

.type-action,
.action-link, .type-link {
  display: block;
  color: #5d9bfc;
  font-weight: 500;
}

.type-action:hover,
.action-link:hover, .type-link:hover {
  font-weight: 600;
  transition: font-weight 0.1s ease-out;
}

.type-action,
.action-link {
  text-transform: uppercase;
  font-size: 0.825rem;
  line-height: 1.125em;
  letter-spacing: 1px;
}

.small.type-action,
.small.action-link {
  font-size: 0.675rem;
}

.type-location {
  text-transform: capitalize !important;
  font-weight: 600;
  font-size: 0.725rem;
  line-height: 1.15em;
}

.type-sep {
  display: inline-block;
  width: auto;
  height: 100%;
  font-size: 1.05em;
  font-weight: 600;
  color: #80b2ff;
  text-align: center;
  vertical-align: middle;
}

.type-intro.regular {
  font-weight: 400;
}

.type-intro.strong {
  font-weight: 600;
}

.type-elegant.regular {
  font-weight: 400;
}

.type-elegant.strong {
  font-weight: 600;
}

* + .type-note {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.type-note-small {
  font-size: 0.875rem !important;
}

.label {
  font-weight: 600;
  color: #505050;
}

.tag-label {
  display: inline-block;
  height: auto;
  font-size: 0.875rem;
  line-height: 1em;
  letter-spacing: 0;
  color: #bababa;
  font-weight: 700;
  font-style: normal;
}

.stat {
  font-weight: 600;
}

.stat.emphasis {
  color: #5d9bfc;
}

.type-instruction {
  font-style: italic;
  color: #747f8a;
}

.type-ui-label {
  display: inline-block;
  font-size: 0.75rem;
}

.type-spec {
  margin-left: 1rem;
}

.type-caps {
  text-transform: uppercase;
}

.type-block {
  display: block !important;
}

.type-primary {
  color: #5d9bfc !important;
}

.type-secondary {
  color: #f2f3f6 !important;
}

.type-dark {
  color: #343434 !important;
}

.type-red {
  color: #e26464 !important;
}

.type-yellow {
  color: #f0d886 !important;
}

.type-green {
  color: #50a475 !important;
}

.type-blue {
  color: #80b2ff !important;
}

.username {
  color: #5d9bfc;
}

.company {
  color: #7e7e7e;
  text-align: left;
  font-size: 0.9rem;
}

strong {
  font-weight: 700;
}

.align-center {
  text-align: center !important;
}

.align-right {
  text-align: right !important;
}

.align-left {
  text-align: left !important;
}

.align-justify {
  text-align: justify !important;
}

.align-justify p {
  text-align: justify !important;
}

.sep {
  display: inline-block;
  padding: 0 1.5rem;
}

.type-black {
  color: black;
}

.air {
  padding-top: 2rem;
}

/*
DEFAULT TYPOGRAPHIC DEFINITIONS
*/
h1, h2, h3, h4, h5, h6 {
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  color: #2f313a;
  font-weight: 400;
}

h1 {
  font-size: 2em;
  font-weight: 100;
}

h2 {
  font-size: 1.5em;
}

h3 {
  font-size: 1.25em;
}

h4 {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 0.85em;
}

a {
  text-decoration: none;
  color: inherit;
}

a:hover {
  text-decoration: none;
  transition: all 0.2s ease-out;
}

p a {
  color: #5d9bfc;
}

p a:hover {
  text-decoration: underline;
}

strong {
  font-weight: 600;
}

sub,
sup {
  display: inline-block;
  font-size: 0.5em;
  line-height: 1em;
  font-weight: 300;
}

sup {
  vertical-align: top;
  margin-top: -0.5em;
}

sub {
  vertical-align: bottom;
  margin-bottom: -0.5em;
}

.page-title,
.section-title {
  color: #5d9bfc;
}

.page-description,
.section-description {
  font-size: 1.15rem;
  line-height: 1.5em;
}

.page-title + .page-description,
.section-title + .section-description {
  margin-top: 3rem;
}

.title-elegant {
  display: inline-block;
  width: auto;
  height: auto;
  padding-bottom: 0.65em;
  font-weight: 600;
  border-bottom: 3px solid #5d9bfc;
  color: #5d9bfc;
}

.columnize {
  -webkit-column-count: 2;
  -moz-column-count: 2;
  column-count: 2;
  -webkit-column-gap: 3rem;
  -moz-column-gap: 3rem;
  column-gap: 3rem;
  text-align: left;
  font-size: 1.275rem;
  line-height: 1.325em;
}

.columnize p {
  orphans: 2;
  widows: 2;
  hyphens: auto;
  -webkit-hyphens: auto;
}

.columnize ul li,
.columnize ol li {
  break-before: avoid;
  break-after: avoid;
  break-inside: avoid;
}

.columnize.justify,
.columnize.justify * {
  text-align: justify;
}

@media (max-width: 1280px) {
  .columnize {
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2;
  }
}

@media (max-width: 960px) {
  .columnize {
    -webkit-column-count: 1;
    -moz-column-count: 1;
    column-count: 1;
    -webkit-column-gap: 0;
    -moz-column-gap: 0;
    column-gap: 0;
  }
}

.glyph-action:before {
  content: "";
}

.glyph-choice:before {
  content: "";
}

.glyph-timer:before {
  content: "";
}

.glyph-delay:before {
  content: "";
}

.glyph-wait:before {
  content: "";
}

.glyph-arrow-up:before {
  content: "";
}

.glyph-arrow-right:before {
  content: "";
}

.glyph-arrow-down:before {
  content: "";
}

.glyph-arrow-left:before {
  content: "";
}

.glyph-arrow-up-bold:before {
  content: "";
}

.glyph-arrow-right-bold:before {
  content: "";
}

.glyph-arrow-down-bold:before {
  content: "";
}

.glyph-arrow-left-bold:before {
  content: "";
}

.glyph-checkmark:before {
  content: "";
}

.glyph-checkmark-bold:before {
  content: "";
}

.glyph-plus:before {
  content: "";
}

.glyph-plus-bold:before {
  content: "";
}

.glyph-minus:before {
  content: "";
}

.glyph-minus-bold:before {
  content: "";
}

.glyph-cross:before {
  content: "";
}

.glyph-cross-bold:before {
  content: "";
}

.glyph-cr-play:before {
  content: "";
}

.glyph-cr-plus:before {
  content: "";
}

.glyph-cr-minus:before {
  content: "";
}

.glyph-cr-checkmark:before {
  content: "";
}

.glyph-sq-arrow-down:before {
  content: "";
}

.glyph-sq-arrow-left:before {
  content: "";
}

.glyph-sq-arrow-right:before {
  content: "";
}

.glyph-sq-arrow-up:before {
  content: "";
}

.glyph-sq-arrow-cross:before {
  content: "";
}

.glyph-sq-arrow-plus:before {
  content: "";
}

.glyph-sq-arrow-minus:before {
  content: "";
}

.glyph-happy:before {
  content: "";
}

.glyph-neutral:before {
  content: "󩍁";
}

.glyph-sad:before {
  content: "";
}

.glyph-triangle-down:before {
  content: "";
}

.glyph-triangle-left:before {
  content: "";
}

.glyph-triangle-right:before {
  content: "";
}

.glyph-triangle-up:before {
  content: "";
}

.glyph-pointer-up:before {
  content: "";
}

.glyph-pointer-right:before {
  content: "";
}

.glyph-pointer-down:before {
  content: "";
}

.glyph-pointer-left:before {
  content: "";
}

.glyph-pointer-up-2:before {
  content: "";
}

.glyph-pointer-right-2:before {
  content: "";
}

.glyph-pointer-down-2:before {
  content: "";
}

.glyph-pointer-left-2:before {
  content: "";
}

.glyph-cursor-move:before {
  content: "";
}

.glyph-resize:before {
  content: "";
}

.glyph-minimize:before {
  content: "";
}

.glyph-maximize:before {
  content: "";
}

.glyph-star:before {
  content: "";
}

.glyph-heart:before {
  content: "";
}

.glyph-link:before {
  content: "";
}

.glyph-save:before {
  content: "";
}

.glyph-hyperlink:before {
  content: "";
}

.glyph-edit:before {
  content: "";
}

.glyph-edit-2:before {
  content: "";
}

.glyph-thumb-down:before {
  content: "";
}

.glyph-thumb-up:before {
  content: "";
}

.glyph-download:before {
  content: "";
}

.glyph-download-cloud:before {
  content: "";
}

.glyph-upload:before {
  content: "";
}

.glyph-upload-cloud:before {
  content: "";
}

.glyph-email:before {
  content: "";
}

.glyph-email-bell:before {
  content: "";
}

.glyph-phone:before {
  content: "";
}

.glyph-help:before {
  content: "";
}

.glyph-trash:before {
  content: "";
}

.glyph-trashbin:before {
  content: "";
}

.glyph-grid:before {
  content: "";
}

.glyph-settings:before {
  content: "";
}

.glyph-settings-2:before {
  content: "";
}

.glyph-settings-3:before {
  content: "";
}

.glyph-gear:before {
  content: "";
}

.glyph-book:before {
  content: "";
}

.glyph-graduation-cap:before {
  content: "";
}

.glyph-folder:before {
  content: "";
}

.glyph-folder-play:before {
  content: "";
}

.glyph-folder-music:before {
  content: "";
}

.glyph-folder-images:before {
  content: "";
}

.glyph-laptop:before {
  content: "";
}

.glyph-tv:before {
  content: "";
}

.glyph-world:before {
  content: "";
}

.glyph-location:before {
  content: "";
}

.glyph-reload:before {
  content: "";
}

.glyph-search:before {
  content: "";
}

.glyph-search-2:before {
  content: "";
}

.glyph-menu:before {
  content: "";
}

.glyph-menu-dropdown:before {
  content: "";
}

.glyph-menu-cr-vertical:before {
  content: "";
}

.glyph-bell:before {
  content: "";
}

.glyph-quote:before {
  content: "";
}

.glyph-alert:before {
  content: "";
}

.glyph-lock:before {
  content: "";
}

.glyph-unlock:before {
  content: "";
}

.glyph-send:before {
  content: "";
}

.glyph-pin:before {
  content: "";
}

.glyph-mappin:before {
  content: "";
}

.glyph-key:before {
  content: "";
}

.glyph-warning:before {
  content: "";
}

.glyph-warning-2:before {
  content: "";
}

.glyph-rocket:before {
  content: "";
}

.glyph-document:before {
  content: "";
}

.glyph-reply-all:before {
  content: "";
}

.glyph-reply:before {
  content: "";
}

.glyph-evernote:before {
  content: "";
}

.glyph-hangouts:before {
  content: "";
}

.glyph-skype-cr:before {
  content: "";
}

.glyph-skype:before {
  content: "";
}

.glyph-vine-cr:before {
  content: "";
}

.glyph-vine:before {
  content: "";
}

.glyph-yelp:before {
  content: "";
}

.glyph-facebook-cr:before {
  content: "";
}

.glyph-facebook:before {
  content: "";
}

.glyph-flickr-cr:before {
  content: "";
}

.glyph-flickr:before {
  content: "";
}

.glyph-instagram-cr:before {
  content: "";
}

.glyph-instagram:before {
  content: "";
}

.glyph-linkedin-cr:before {
  content: "";
}

.glyph-linkedin:before {
  content: "";
}

.glyph-pinterest-cr:before {
  content: "";
}

.glyph-pinterest:before {
  content: "";
}

.glyph-sand-clock:before {
  content: "";
}

.glyph-tumblr:before {
  content: "";
}

.glyph-twitter-cr:before {
  content: "";
}

.glyph-twitter:before {
  content: "";
}

.glyph-vimeo-cr:before {
  content: "";
}

.glyph-vimeo:before {
  content: "";
}

.glyph-youtube-cr:before {
  content: "";
}

.glyph-youtube:before {
  content: "";
}

.glyph-feather:before {
  content: "";
}

.glyph-connection:before {
  content: "";
}

.glyph-world-location:before {
  content: "";
}

.glyph-amazon:before {
  content: "";
}

.glyph-google:before {
  content: "";
}

.glyph-google-cr:before {
  content: "";
}

.glyph-google-plus:before {
  content: "";
}

.glyph-google-plus-cr:before {
  content: "";
}

.glyph-google-hangouts:before {
  content: "";
}

.glyph-facebook-sq:before {
  content: "";
}

.glyph-telegram:before {
  content: "";
}

.glyph-rss:before {
  content: "";
}

.glyph-rss-sq:before {
  content: "";
}

.glyph-reddit:before {
  content: "";
}

.glyph-chart-1:before {
  content: "";
}

.glyph-chart-2:before {
  content: "";
}

.glyph-chart-3:before {
  content: "";
}

.glyph-chart-4:before {
  content: "";
}

.glyph-chart-5:before {
  content: "";
}

.glyph-chart-6:before {
  content: "";
}

.glyph-chart-7:before {
  content: "";
}

.glyph-ab-testing:before {
  content: "";
}

.glyph-abtesting:before {
  content: "";
}

.glyph-contact-update:before {
  content: "";
}

.glyph-contact-delete:before {
  content: "";
}

.glyph-sms-bell:before {
  content: "";
}

.glyph-sms:before {
  content: "";
}

.glyph-tag:before {
  content: "";
}

.glyph-tag-add:before {
  content: "";
}

.glyph-tag-delete:before {
  content: "";
}

.glyph-user:before {
  content: "";
}

.glyph-return:before {
  content: "";
}

.glyph-flowchart:before {
  content: "";
}

.glyph-outbound:before {
  content: "";
}

.glyph-target:before {
  content: "";
}

.glyph-email-send:before {
  content: "";
}

.glyph-email-open:before {
  content: "";
}

.glyph-email-click:before {
  content: "";
}

.glyph-sms-send:before {
  content: "";
}

.glyph-sms-open:before {
  content: "";
}

.glyph-sms-click:before {
  content: "";
}

.glyph-click:before {
  content: "";
}

.glyph-send-2:before {
  content: "";
}

.glyph-calendar:before {
  content: "";
}

.glyph-calendar-clock:before {
  content: "";
}

.glyph-calendar-clock-2:before {
  content: "";
}

.glyph-email-sign:before {
  content: "";
}

.glyph-stop:before {
  content: "";
}

.glyph-stop-hand:before {
  content: "";
}

.glyph-medal:before {
  content: "";
}

.glyph-trophy:before {
  content: "";
}

.glyph-number-sign:before {
  content: "";
}

.glyph-sandclock:before {
  content: "";
}

.glyph-text:before {
  content: "";
}

.glyph-text-multiple:before {
  content: "";
}

.glyph-url:before {
  content: "";
}

.glyph-automation:before {
  content: "";
}

.glyph-automation-stop:before {
  content: "";
}

.glyph-enlarge:before {
  content: "";
}

.glyph-reduce:before {
  content: "";
}

.glyph-form-fields:before {
  content: "";
}

.glyph-funnel:before {
  content: "";
}

.glyph-door-exit:before {
  content: "";
}

.glyph-pixel-search:before {
  content: "";
}

.glyph-newsletter-sms:before {
  content: "";
}

.glyph-newsletter-email:before {
  content: "";
}

.glyph-tag-image:before {
  content: "";
}

.glyph-tag-click:before {
  content: "";
}

.glyph-email-gear:before {
  content: "";
}

.glyph-sms-gear:before {
  content: "";
}

.glyph-view:before {
  content: "";
}

.glyph-duplicate:before {
  content: "";
}

.glyph-flowchart-expand-all:before {
  content: "";
}

.glyph-flowchart-collapse-all:before {
  content: "";
}

.glyph-flowchart-collapse-to-no:before {
  content: "";
}

.glyph-flowchart-collapse-to-yes:before {
  content: "";
}

.glyph-zoom-in:before {
  content: "";
}

.glyph-zoom-out:before {
  content: "";
}

.glyph-zoom-reset:before {
  content: "";
}

.glyph-open-window:before {
  content: "";
}

.glyph-flag:before {
  content: "";
}

.glyph-klicky-run:before {
  content: "";
}

.glyph-klicky-warning:before {
  content: "";
}

.glyph-klicky-cross:before {
  content: "";
}

.glyph-klicky-info:before {
  content: "";
}

.glyph-wufoo:before {
  content: "";
}

.glyph-wistia:before {
  content: "";
}

.glyph-twilio:before {
  content: "";
}

.glyph-thrivethemes:before {
  content: "";
}

.glyph-paypal:before {
  content: "";
}

.glyph-optimizepress:before {
  content: "";
}

.glyph-nexmo:before {
  content: "";
}

.glyph-leadpages:before {
  content: "";
}

.glyph-digistore:before {
  content: "";
}

.glyph-clickbank:before {
  content: "";
}

.glyph-affilicon:before {
  content: "";
}

.glyph-cardreaderpro:before {
  content: "";
}

.glyph-form-widget:before {
  content: "";
}

.glyph-form-custom:before {
  content: "";
}

.glyph-form-inline:before {
  content: "";
}

.glyph-form-raw:before {
  content: "";
}

.glyph-form-combo:before {
  content: "";
}

.glyph-api-key:before {
  content: "";
}

.glyph-facebook-button:before {
  content: "";
}

.glyph-goal:before {
  content: "";
}

.glyph-race-flag:before {
  content: "";
}

.glyph-wave-flag:before {
  content: "";
}

*[class*="icon-"],
*[class*="glyph-"] {
  font-weight: normal;
}

*[class*="icon-"].glyphcolor-primary:before,
*[class*="glyph-"].glyphcolor-primary:before {
  color: #5d9bfc !important;
}

.hidden {
  display: none !important;
}

.hidden.inline {
  display: none !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.bg-white {
  background-color: #FFF !important;
}

.air-right {
  margin-right: 1rem !important;
}

.air-right-small {
  margin-right: 0.5rem !important;
}

.air-right-medium {
  margin-right: 1.5rem !important;
}

.air-right-large {
  margin-right: 2rem !important;
}

.air-right-xlarge {
  margin-right: 3rem !important;
}

.air-left {
  margin-left: 1rem !important;
}

.air-left-small {
  margin-left: 0.5rem !important;
}

.air-left-medium {
  margin-left: 1.5rem !important;
}

.air-left-large {
  margin-left: 2rem !important;
}

.air-left-xlarge {
  margin-left: 3rem !important;
}

.air-top {
  margin-top: 1.5rem !important;
}

.air-top-small {
  margin-top: 0.75rem !important;
}

.air-top-medium {
  margin-top: 3.5rem !important;
}

.air-top-large {
  margin-top: 6rem !important;
}

.air-bottom {
  margin-bottom: 1.5rem !important;
}

.air-bottom-small {
  margin-bottom: 0.75rem !important;
}

.air-bottom-medium {
  margin-bottom: 3.5rem !important;
}

.air-bottom-large {
  margin-bottom: 6rem !important;
}

.picture-frame {
  border: 1px solid #a8c9fc;
  border-radius: 0.25rem;
  padding: 0.5rem !important;
}

.picture-frame img {
  width: 100%;
  height: auto;
}

.cursor-default {
  cursor: default !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-none {
  cursor: none !important;
}

.cursor-move {
  cursor: move !important;
}

.inline {
  display: inline !important;
}

.inline-block {
  display: inline-block !important;
}

.ui-box-close,
.ui-modal-close,
.panel-close,
.sidebar-close {
  display: block;
  width: 2rem;
  height: 2rem;
  cursor: pointer;
}

.ui-box-close:before,
.ui-modal-close:before,
.panel-close:before,
.sidebar-close:before {
  display: block;
  font-family: "glyphs";
  content: "";
  font-size: 1.25rem;
  line-height: 2rem;
  text-align: center;
  color: #c1cfe6;
}

.ui-box-close:hover,
.ui-modal-close:hover,
.panel-close:hover,
.sidebar-close:hover {
  -moz-transform: scale(1.25);
  -o-transform: scale(1.25);
  -ms-transform: scale(1.25);
  -webkit-transform: scale(1.25);
  transform: scale(1.25);
  transition: transform 0.15s ease-out;
}

.centerblock {
  display: block !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.scrollable {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.scrollable .content,
.scrollable .content-scroll {
  width: calc(100% + 2.5rem);
  height: 100%;
  min-height: 100%;
  padding-right: 2.5rem;
  overflow: scroll;
}

.scrollable .content-scroll::-webkit-scrollbar {
  display: none;
}

.hrule {
  display: block;
  width: 100%;
  height: 1px;
  background-color: #d8e1f0;
  margin: 1rem auto;
  position: relative;
  overflow: visible;
}

.hrule.toolbar > *,
.hrule.toolbar .ui-toolbar {
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: inline-block;
  width: auto;
  height: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 5;
}

.hrule.toolbar > *:hover,
.hrule.toolbar .ui-toolbar:hover {
  box-shadow: 2px 2px 1px 0 rgba(193, 207, 230, 0.35);
  transition: all 0.15s ease-out;
}

.hrule.air-none, .hrule.no-margin {
  margin: 0 auto;
}

.hrule.transparent {
  height: 0;
  background-color: transparent;
}

.hrule.transparent + .ui-box.app {
  margin-top: -1px;
}

.hrule.vline {
  padding: 2rem 0;
  background-color: transparent;
}

.hrule.vline:before {
  display: block;
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  background-color: #c1cfe6;
}

.hrule.vline.dash:before {
  background-color: transparent;
  border-left: 1px dashed #c1cfe6;
}

.forbidden,
.disabled {
  cursor: not-allowed !important;
  transition: none !important;
}

.forbidden *,
.disabled * {
  cursor: not-allowed !important;
}

.forbidden:before, .forbidden:after,
.disabled:before,
.disabled:after {
  cursor: not-allowed !important;
}

.forbidden.dim,
.disabled.dim {
  opacity: 0.75 !important;
}

html {
  position: relative;
  min-height: 100% !important;
  width: 100%;
  height: 100%;
}

body {
  font-family: "Source Sans Pro", "Source Sans Pro", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-size: 16px;
  line-height: 1.45em;
  font-weight: normal;
  color: #2f313a;
  width: 100%;
  margin: 0;
  padding: 0;
  color: #2f313a;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.25em;
  background: #FFF;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body.admin-menu {
  padding-top: 20px !important;
}

body.admin-menu .bookmark-menu {
  top: 20px !important;
}

@media (max-width: 767px) {
  body.admin-menu {
    padding-top: 0 !important;
  }
  body.admin-menu .bookmark-menu {
    top: 0 !important;
  }
}

#site-wrapper {
  clear: both;
  position: relative;
  width: 100%;
  height: auto !important;
  min-height: 100% !important;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

#site-wrapper:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

#top-menu ul.navbar-nav li a {
  font-family: Arial, sans-serif;
  font-size: 0.875rem;
}

.styleguide-landingpage #block-user-login {
  display: none;
}

button {
  border: none;
}

button,
*[class*="button-"] {
  display: inline-block;
  width: auto;
  height: auto;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  text-align: center;
  cursor: pointer;
  box-shadow: none;
  outline: none;
  border-radius: 0.25rem;
  transition: all 0.15s ease-out;
  padding: 0.65em 1.125em;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.1em;
}

button.block,
*[class*="button-"].block {
  display: block;
  width: 100%;
  max-width: 100%;
}

button.xlarge,
*[class*="button-"].xlarge {
  padding: 1.75em 2.5em;
  font-size: 1.25em;
  font-weight: 400;
}

button.large,
*[class*="button-"].large {
  padding: 1em 2.25em;
  font-size: 1.5em;
  font-weight: 400;
}

button.medium,
*[class*="button-"].medium {
  padding: 1em 1.25em;
  font-size: 1.35em;
}

button.small,
*[class*="button-"].small {
  padding: 0.375em 1.125em;
  font-size: 0.9em;
}

button.small[class*="icon-"]:before, button.small[class*="glyph-"]:before,
*[class*="button-"].small[class*="icon-"]:before,
*[class*="button-"].small[class*="glyph-"]:before {
  font-size: 1em;
}

button.xsmall,
*[class*="button-"].xsmall {
  padding: 0.375em 0.75em;
  font-size: 0.725em;
}

button.xsmall[class*="icon-"]:before, button.xsmall[class*="glyph-"]:before,
*[class*="button-"].xsmall[class*="icon-"]:before,
*[class*="button-"].xsmall[class*="glyph-"]:before {
  font-size: 1.05em;
}

button[class*="icon-"]:before, button[class*="glyph-"]:before,
*[class*="button-"][class*="icon-"]:before,
*[class*="button-"][class*="glyph-"]:before {
  display: inline;
  margin-right: 0.5em;
  font-size: 1em;
  line-height: inherit;
  vertical-align: top;
}

.button-primary {
  color: #FFF;
  background: #5d9bfc;
}

.button-primary[class*="icon-"]:before, .button-primary[class*="glyph-"]:before {
  color: #174bad;
}

.button-primary:hover {
  color: #FFF !important;
  background: #6da7ff;
  transition: all 0.25s ease-out;
}

.button-primary.bevel {
  box-shadow: inset -1px -2px 0 0 rgba(23, 75, 173, 0.35);
}

.button-secondary {
  color: #FFF;
  background: #686cbe;
}

.button-secondary[class*="icon-"]:before, .button-secondary[class*="glyph-"]:before {
  color: #2d317b;
}

.button-secondary:hover {
  color: #FFF !important;
  background: #777bc6;
  transition: all 0.25s ease-out;
}

.button-secondary:hover[class*="icon-"]:before, .button-secondary:hover[class*="glyph-"]:before {
  color: #2d317b;
  transition: all 0.25s ease-out;
}

.button-secondary.bevel {
  box-shadow: inset -1px -2px 0 0 rgba(45, 49, 123, 0.65);
}

.button-tertiary {
  color: #FFF;
  background: #686cbe;
}

.button-tertiary[class*="icon-"]:before, .button-tertiary[class*="glyph-"]:before {
  color: #FFF;
}

.button-tertiary:hover {
  color: #FFF !important;
  background: #565aac;
  transition: all 0.25s ease-out;
}

.button-tertiary:hover[class*="icon-"]:before, .button-tertiary:hover[class*="glyph-"]:before {
  color: #2d317b;
  transition: all 0.25s ease-out;
}

.button-alpha {
  color: #FFF;
  background: rgba(0, 0, 0, 0.175);
}

.button-alpha[class*="icon-"]:before, .button-alpha[class*="glyph-"]:before {
  color: #FFF;
}

.button-alpha:hover {
  color: #FFF;
  background: rgba(0, 0, 0, 0.275);
  transition: all 0.15s ease-out;
}

.button-danger {
  color: #FFF;
  background: #e26464;
}

.button-danger[class*="icon-"]:before, .button-danger[class*="glyph-"]:before {
  color: #c63434;
}

.button-danger:hover {
  color: #FFF !important;
  background: #e57575;
  transition: all 0.25s ease-out;
}

.button-danger.bevel {
  box-shadow: inset -1px -2px 0 0 #da3939;
}

.button-gray {
  color: #FFF;
  background: #c1cfe6;
}

.button-gray[class*="icon-"]:before, .button-gray[class*="glyph-"]:before {
  color: #5d759a;
}

.button-gray:hover {
  color: #FFF !important;
  background: #d7e0ef;
  transition: all 0.25s ease-out;
}

.button-gray.bevel {
  box-shadow: inset -1px -2px 0 0 rgba(93, 117, 154, 0.35);
}

.button-cancel {
  color: #a7b8d6;
  background-color: transparent;
  border: none;
  box-shadow: inset 0 0 0 1px #a7b8d6;
  right: 0;
}

.button-cancel[class*="icon-"]:before, .button-cancel[class*="glyph-"]:before {
  color: #758ba9;
  margin-right: 0;
  font-size: 0.85em !important;
  line-height: 1.35em !important;
}

.button-cancel:hover {
  color: #5d9bfc;
  box-shadow: inset 0 0 0 1px #3378e3;
  transition: all 0.25s ease-out;
}

.tool-button {
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #c1cfe6;
  box-shadow: inset 0 0 0 1px #b8c8e4;
  border-radius: 0.1875rem;
  cursor: pointer;
  transition: all 0.2s ease-in;
}

.tool-button[class*="icon-"]:before, .tool-button[class*="glyph-"]:before {
  display: block;
  width: 1.25rem;
  height: 1.25rem;
  font-size: 0.75rem;
  line-height: 1.25rem;
  text-indent: 3px;
  color: #FFF;
}

.tool-button:hover {
  background-color: #5d759a;
  box-shadow: 0 0 2px 3px rgba(93, 117, 154, 0.175);
  transition: all 0.25s ease-out;
}

.tool-button.no-text {
  font-size: 0;
}

.tool-button.no-text:before {
  font-size: 0.75rem;
}

.action-edit {
  position: relative;
  z-index: 5;
  cursor: pointer;
}

.action-edit[class*="icon-"]:before {
  width: 100%;
  height: 7.5rem;
  font-size: 3.5rem;
  line-height: 7.5rem;
  color: #80b2ff;
}

.action-icon {
  color: #5d9bfc;
  text-shadow: 1px -1px rgba(255, 255, 255, 0.75), -1px 1px rgba(255, 255, 255, 0.75), 1px 1px rgba(255, 255, 255, 0.75), -1px -1px rgba(255, 255, 255, 0.75);
  cursor: pointer;
}

.action-icon:hover {
  color: #2f313a;
  transition: all 0.25s ease-out;
}

.button-action {
  background: #f2f3f6;
  background: -moz-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: -webkit-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: linear-gradient(to bottom, #FFF 0%, #f5f9ff 100%);
  border: 1px solid #c1cfe6;
  border-top: 1px solid #d8e1f0;
  border-left: 1px solid #d8e1f0;
  position: relative;
  border-radius: 0.25rem;
  font-size: 1.25rem;
  padding: 1.25em;
  font-weight: normal;
  color: #5d9bfc;
  text-align: left;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.35);
  box-shadow: 2px 2px 0 0 rgba(193, 207, 230, 0.2);
}

.button-action > footer {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  position: relative;
}

.button-action > footer:before {
  display: block;
  content: "";
  width: 100%;
  height: 1px;
  background: #c1cfe6;
  box-shadow: 0 1px 0 0 rgba(255, 255, 255, 0.65);
  position: absolute;
  top: 0;
  left: 0;
}

.button-action[class*="glyph-"] {
  padding-left: 2.85em;
}

.button-action[class*="glyph-"]:before, .button-action[class*="glyph-"]:after {
  display: block;
  position: absolute;
}

.button-action[class*="glyph-"]:before {
  width: 1.5em;
  height: 1.5em;
  text-align: center;
  font-size: 1.65em;
  line-height: 1.5em;
  position: absolute;
  top: 50%;
  left: 0.5em;
  margin-top: -0.5em;
}

.button-action:hover {
  font-weight: 600;
  box-shadow: 2px 2px 0 0 rgba(193, 207, 230, 0.35), 0 0 0 3px rgba(193, 207, 230, 0.115);
  transition: all 0.15s ease-out;
}

.button-action.medium {
  padding: 0.925em;
  font-size: 1rem;
}

.button-action.medium[class*="glyph-"] {
  padding-left: 2.25em;
}

.button-action.small {
  padding: 0.65em;
  font-size: 0.9rem;
}

.button-action.small[class*="glyph-"] {
  padding-left: 2em;
}

.button-action.block {
  text-align: center;
  text-indent: -1.25em;
}

.button-action.block:before {
  text-indent: 0;
}

.button-light {
  background: #FFF;
  color: #2f313a;
  border-radius: 0.25rem;
}

.button-light:before {
  font-size: 1.5rem;
  color: #5d9bfc;
}

.button-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  padding: 0;
  line-height: 2rem;
  border-radius: 2rem;
  text-align: center;
  background: none;
}

.button-icon:before, .button-icon[class*="glyph-"] {
  display: block;
  width: 2rem;
  height: 2rem;
  padding: 0 !important;
  margin: 0;
  border-radius: 2rem;
  line-height: 2rem;
  font-size: 2rem;
  color: #c1cfe6;
}

.button-icon:hover:before, .button-icon:hover[class*="glyph-"] {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.button-icon.no-text {
  font-size: 0;
}

.button-icon.no-text:before {
  font-size: 1.2rem;
}

.button-icon.small {
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  border-radius: 1rem;
}

.button-icon.small:before, .button-icon.small[class*="glyph-"] {
  width: 1rem;
  height: 1rem;
  border-radius: 1rem;
  line-height: 1rem;
  font-size: 1rem;
}

.button-icon.medium {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  border-radius: 1.5rem;
}

.button-icon.medium:before, .button-icon.medium[class*="glyph-"] {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 1.5rem;
  line-height: 1.5rem;
  font-size: 1.5rem;
}

.button-icon.large {
  width: 2.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
  border-radius: 2.5rem;
}

.button-icon.large:before, .button-icon.large[class*="glyph-"] {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 2.5rem;
  line-height: 2.5rem;
  font-size: 2.5rem;
}

.ui-actions {
  display: inline-block;
}

.ui-actions > a,
.ui-actions > li {
  display: inline-block;
  margin: 0 0.35em 0 0;
  vertical-align: top;
}

.ui-actions > a:before,
.ui-actions > li:before {
  vertical-align: top;
}

.ui-actions > a[class*="-edit-2"]:before,
.ui-actions > li[class*="-edit-2"]:before {
  font-size: 0.65rem;
}

.ui-actions > a:last-child,
.ui-actions > li:last-child {
  margin: 0;
}

.ui-remove {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  position: absolute;
  top: 0.35rem;
  right: 0.35rem;
  cursor: pointer;
}

.ui-remove:before {
  font-family: "glyphs";
  content: "";
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  font-size: 0.7rem;
  color: #c1cfe6;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.25);
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
}

.ui-remove:hover:before {
  color: #5d759a;
  transition: color 0.15s ease-out;
}

.ui-remove.v-center {
  top: 50%;
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.button-cta {
  display: inline-block;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  width: auto;
  height: auto;
  padding: 0.75em 1.125em !important;
  background: orange;
  box-shadow: inset -2px -3px 0 0 rgba(229, 148, 0, 0.75);
  border: 1px solid #d07800;
  color: #FFF;
  text-shadow: 2px 2px 0 rgba(229, 148, 0, 0.5);
  text-align: center;
  border-radius: 0.25rem;
  letter-spacing: -1px;
  position: relative;
  transition: all 0.15s ease-out;
  font-size: 1.25rem;
}

.button-cta.small {
  font-size: 1rem;
}

.button-cta.medium {
  font-size: 1.85rem;
}

.button-cta.large {
  font-size: 2rem;
}

.button-cta.xlarge {
  font-size: 2.45rem;
}

.button-cta:after {
  content: "";
  width: 80%;
  height: 0.85rem;
  border-radius: 100%;
  background: rgba(4, 34, 113, 0.15);
  box-shadow: 0 0 12px 10px rgba(4, 34, 113, 0.15);
  position: absolute;
  bottom: -0.5rem;
  left: 10%;
  z-index: -1;
  transition: all 0.15s ease-out;
}

.button-cta:hover {
  color: #FFF !important;
  background: #ffb01f;
  transition: all 0.2s ease-in;
}

.button-cta:hover:after {
  background: rgba(4, 34, 113, 0.185);
  box-shadow: 0 0 12px 10px rgba(4, 34, 113, 0.185);
  transition: all 0.2s ease-in;
}

@media (max-width: 600px) {
  .button-cta.large, .button-cta.xlarge {
    font-size: 1.85rem;
  }
}

@media (max-width: 500px) {
  .button-cta.medium, .button-cta.large, .button-cta.xlarge {
    font-size: 1.35rem;
  }
}

@media (max-width: 320px) {
  .button-cta.medium, .button-cta.large, .button-cta.xlarge {
    font-size: 1.15rem;
  }
}

.button-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #758ba9;
  font-weight: 400;
  background: transparent;
}

.button-text[class*="icon-"]:before, .button-text[class*="glyph-"]:before {
  color: #5d9bfc !important;
}

.button-text.block {
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding: 1.25em;
  text-align: center;
  border: 1px solid #d8e1f0;
}

.button-text.block:hover {
  background-color: #fafbff;
}

.button-app,
.app-icon {
  display: block;
  width: 6rem;
  height: 0;
  padding-top: 6rem;
  position: relative;
  border-radius: 12.5%;
  border: 1px solid #d8e1f0;
  background-color: #f4f8ff;
  box-shadow: 0 3px 0 0 rgba(193, 207, 230, 0.25);
  cursor: pointer;
  text-align: center;
}

.button-app[class*="glyph-"]:before, .button-app[class*="icon-"]:before,
.app-icon[class*="glyph-"]:before,
.app-icon[class*="icon-"]:before {
  display: block;
  width: 6rem;
  height: 6rem;
  line-height: 6rem !important;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -3rem;
  margin-left: -3rem;
  font-size: 3rem !important;
  color: #5d9bfc !important;
  text-shadow: 2px 2px 0 rgba(255, 255, 255, 0.5);
  text-align: center !important;
  text-indent: 0.25rem;
}

.button-app.icon-color-teal-light,
.app-icon.icon-color-teal-light {
  background-color: #6ECED9 !important;
}

.button-app.icon-color-teal-light[class*="glyph-"]:before, .button-app.icon-color-teal-light[class*="icon-"]:before,
.app-icon.icon-color-teal-light[class*="glyph-"]:before,
.app-icon.icon-color-teal-light[class*="icon-"]:before {
  color: #1a5a61 !important;
  text-shadow: none;
}

.button-app.icon-color-teal-dark,
.app-icon.icon-color-teal-dark {
  background-color: #0097AA !important;
}

.button-app.icon-color-teal-dark[class*="glyph-"]:before, .button-app.icon-color-teal-dark[class*="icon-"]:before,
.app-icon.icon-color-teal-dark[class*="glyph-"]:before,
.app-icon.icon-color-teal-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-peach-light,
.app-icon.icon-color-peach-light {
  background-color: #E6AB9D !important;
}

.button-app.icon-color-peach-light[class*="glyph-"]:before, .button-app.icon-color-peach-light[class*="icon-"]:before,
.app-icon.icon-color-peach-light[class*="glyph-"]:before,
.app-icon.icon-color-peach-light[class*="icon-"]:before {
  color: #923a25 !important;
  text-shadow: none;
}

.button-app.icon-color-peach-dark,
.app-icon.icon-color-peach-dark {
  background-color: #BC5F5F !important;
}

.button-app.icon-color-peach-dark[class*="glyph-"]:before, .button-app.icon-color-peach-dark[class*="icon-"]:before,
.app-icon.icon-color-peach-dark[class*="glyph-"]:before,
.app-icon.icon-color-peach-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-pink-light,
.app-icon.icon-color-pink-light {
  background-color: #E286A6 !important;
}

.button-app.icon-color-pink-light[class*="glyph-"]:before, .button-app.icon-color-pink-light[class*="icon-"]:before,
.app-icon.icon-color-pink-light[class*="glyph-"]:before,
.app-icon.icon-color-pink-light[class*="icon-"]:before {
  color: #7e1e3f !important;
  text-shadow: none;
}

.button-app.icon-color-pink-dark,
.app-icon.icon-color-pink-dark {
  background-color: #C64873 !important;
}

.button-app.icon-color-pink-dark[class*="glyph-"]:before, .button-app.icon-color-pink-dark[class*="icon-"]:before,
.app-icon.icon-color-pink-dark[class*="glyph-"]:before,
.app-icon.icon-color-pink-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-violet-light,
.app-icon.icon-color-violet-light {
  background-color: #8D98D8 !important;
}

.button-app.icon-color-violet-light[class*="glyph-"]:before, .button-app.icon-color-violet-light[class*="icon-"]:before,
.app-icon.icon-color-violet-light[class*="glyph-"]:before,
.app-icon.icon-color-violet-light[class*="icon-"]:before {
  color: #273272 !important;
  text-shadow: none;
}

.button-app.icon-color-violet-dark,
.app-icon.icon-color-violet-dark {
  background-color: #505EAF !important;
}

.button-app.icon-color-violet-dark[class*="glyph-"]:before, .button-app.icon-color-violet-dark[class*="icon-"]:before,
.app-icon.icon-color-violet-dark[class*="glyph-"]:before,
.app-icon.icon-color-violet-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-blue-light,
.app-icon.icon-color-blue-light {
  background-color: #86B2DE !important;
}

.button-app.icon-color-blue-light[class*="glyph-"]:before, .button-app.icon-color-blue-light[class*="icon-"]:before,
.app-icon.icon-color-blue-light[class*="glyph-"]:before,
.app-icon.icon-color-blue-light[class*="icon-"]:before {
  color: #214c77 !important;
  text-shadow: none;
}

.button-app.icon-color-blue-dark,
.app-icon.icon-color-blue-dark {
  background-color: #4182D9 !important;
}

.button-app.icon-color-blue-dark[class*="glyph-"]:before, .button-app.icon-color-blue-dark[class*="icon-"]:before,
.app-icon.icon-color-blue-dark[class*="glyph-"]:before,
.app-icon.icon-color-blue-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-silver-light,
.app-icon.icon-color-silver-light {
  background-color: #A4B3BA !important;
}

.button-app.icon-color-silver-light[class*="glyph-"]:before, .button-app.icon-color-silver-light[class*="icon-"]:before,
.app-icon.icon-color-silver-light[class*="glyph-"]:before,
.app-icon.icon-color-silver-light[class*="icon-"]:before {
  color: #3f4d53 !important;
  text-shadow: none;
}

.button-app.icon-color-silver-dark,
.app-icon.icon-color-silver-dark {
  background-color: #6C818D !important;
}

.button-app.icon-color-silver-dark[class*="glyph-"]:before, .button-app.icon-color-silver-dark[class*="icon-"]:before,
.app-icon.icon-color-silver-dark[class*="glyph-"]:before,
.app-icon.icon-color-silver-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-brown-light,
.app-icon.icon-color-brown-light {
  background-color: #B7A29B !important;
}

.button-app.icon-color-brown-light[class*="glyph-"]:before, .button-app.icon-color-brown-light[class*="icon-"]:before,
.app-icon.icon-color-brown-light[class*="glyph-"]:before,
.app-icon.icon-color-brown-light[class*="icon-"]:before {
  color: #4e3e38 !important;
  text-shadow: none;
}

.button-app.icon-color-brown-dark,
.app-icon.icon-color-brown-dark {
  background-color: #9B6F60 !important;
}

.button-app.icon-color-brown-dark[class*="glyph-"]:before, .button-app.icon-color-brown-dark[class*="icon-"]:before,
.app-icon.icon-color-brown-dark[class*="glyph-"]:before,
.app-icon.icon-color-brown-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-yellow-light,
.app-icon.icon-color-yellow-light {
  background-color: #E5B05C !important;
}

.button-app.icon-color-yellow-light[class*="glyph-"]:before, .button-app.icon-color-yellow-light[class*="icon-"]:before,
.app-icon.icon-color-yellow-light[class*="glyph-"]:before,
.app-icon.icon-color-yellow-light[class*="icon-"]:before {
  color: #654410 !important;
  text-shadow: none;
}

.button-app.icon-color-yellow-dark,
.app-icon.icon-color-yellow-dark {
  background-color: #DE8225 !important;
}

.button-app.icon-color-yellow-dark[class*="glyph-"]:before, .button-app.icon-color-yellow-dark[class*="icon-"]:before,
.app-icon.icon-color-yellow-dark[class*="glyph-"]:before,
.app-icon.icon-color-yellow-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-green-light,
.app-icon.icon-color-green-light {
  background-color: #7EAD7A !important;
}

.button-app.icon-color-green-light[class*="glyph-"]:before, .button-app.icon-color-green-light[class*="icon-"]:before,
.app-icon.icon-color-green-light[class*="glyph-"]:before,
.app-icon.icon-color-green-light[class*="icon-"]:before {
  color: #243823 !important;
  text-shadow: none;
}

.button-app.icon-color-green-dark,
.app-icon.icon-color-green-dark {
  background-color: #367C44 !important;
}

.button-app.icon-color-green-dark[class*="glyph-"]:before, .button-app.icon-color-green-dark[class*="icon-"]:before,
.app-icon.icon-color-green-dark[class*="glyph-"]:before,
.app-icon.icon-color-green-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-coldgreen-light,
.app-icon.icon-color-coldgreen-light {
  background-color: #62A8A0 !important;
}

.button-app.icon-color-coldgreen-light[class*="glyph-"]:before, .button-app.icon-color-coldgreen-light[class*="icon-"]:before,
.app-icon.icon-color-coldgreen-light[class*="glyph-"]:before,
.app-icon.icon-color-coldgreen-light[class*="icon-"]:before {
  color: #162826 !important;
  text-shadow: none;
}

.button-app.icon-color-coldgreen-dark,
.app-icon.icon-color-coldgreen-dark {
  background-color: #2C7466 !important;
}

.button-app.icon-color-coldgreen-dark[class*="glyph-"]:before, .button-app.icon-color-coldgreen-dark[class*="icon-"]:before,
.app-icon.icon-color-coldgreen-dark[class*="glyph-"]:before,
.app-icon.icon-color-coldgreen-dark[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.icon-color-,
.app-icon.icon-color- {
  background-color: #FFF !important;
}

.button-app.icon-color-:before,
.app-icon.icon-color-:before {
  color: #5d9bfc !important;
}

.button-app:not(.forbidden):hover,
.app-icon:not(.forbidden):hover {
  border: 1px solid #c1cfe6;
  box-shadow: 0 3px 0 0 rgba(193, 207, 230, 0.275), 0 0 0 4px rgba(193, 207, 230, 0.2);
  transition: all 0.15s ease-out;
}

.button-app.action-modify,
.app-icon.action-modify {
  background-color: #5d9bfc;
  border-color: #3f88fb;
  box-shadow: 0 3px 0 0 rgba(193, 207, 230, 0.35);
}

.button-app.action-modify[class*="glyph-"]:before, .button-app.action-modify[class*="icon-"]:before,
.app-icon.action-modify[class*="glyph-"]:before,
.app-icon.action-modify[class*="icon-"]:before {
  color: #FFF !important;
  text-shadow: none;
}

.button-app.action-modify:not(.forbidden):hover,
.app-icon.action-modify:not(.forbidden):hover {
  border-color: #2176fb;
  box-shadow: 0 3px 0 0 rgba(193, 207, 230, 0.475), 0 0 0 4px rgba(193, 207, 230, 0.4);
}

.ui-modal-toggle {
  cursor: pointer;
}

.ui-modal-source-hide {
  display: none !important;
}

.ui-modal {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 500;
  background-color: rgba(0, 0, 0, 0.75);
}

.ui-modal:not(.canvas) .ui-modal-box {
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: block;
  width: auto;
  min-width: 60vw;
  max-width: 90vw;
  height: auto;
  min-height: 45vh;
  max-height: 90vh;
  border-radius: 0.25rem;
  background-color: #FFF;
  position: absolute;
  top: 50%;
  left: 50%;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-head {
  display: block;
  width: 100%;
  height: auto;
  min-height: 3rem;
  padding: 1.5rem;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  background: #5d9bfc;
  color: #FFF;
  font-weight: 400;
  position: relative;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-head .ui-modal-close {
  top: 50%;
  right: 0.75rem;
  z-index: 5;
  margin-top: -1rem;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-head .ui-modal-close:before {
  color: #FFF;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content {
  padding: 3rem;
  box-shadow: none !important;
  text-align: center;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content h2 {
  color: #1c2c3e;
  font-size: 1.5rem;
  margin: 1rem auto 3rem auto;
  text-align: center;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content .video-iframe-wrapper {
  padding-bottom: 56.25%;
  height: 0;
  position: relative;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content .video-iframe-wrapper iframe,
.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content .video-iframe-wrapper object,
.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content .video-iframe-wrapper embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content img {
  width: 100%;
  height: auto;
}

.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content .video-iframe-wrapper + *[class*="button-"],
.ui-modal:not(.canvas) .ui-modal-box .ui-modal-content iframe + *[class*="button-"] {
  margin-top: 1.25rem;
}

.ui-modal .ui-modal-close {
  position: absolute;
  top: 0.95rem;
  right: 0.95rem;
  z-index: 5;
}

.ui-modal .button-primary + .button-cancel,
.ui-modal button + .button-cancel {
  margin-left: 0.75rem;
}

.ui-modal .modal-section {
  padding-top: 2rem;
  border-top: 1px solid #d8e1f0;
}

.ui-modal .modal-section header {
  padding: 1.5rem 0 3rem 0;
  text-align: center;
}

.ui-modal .modal-section header h2,
.ui-modal .modal-section header h3 {
  font-size: 1.25rem;
}

.ui-modal .modal-section + .modal-section {
  margin-top: 2.5rem;
}

.ui-modal.canvas {
  display: none;
  height: calc(100% - 6rem);
  background-color: rgba(255, 255, 255, 0.75);
  top: 7.25rem;
}

.ui-modal.canvas .content-scroll {
  position: relative;
}

.ui-modal.canvas .content-scroll .ui-modal-close {
  top: 2.5rem;
  right: 1.5rem;
}

.ui-modal.canvas .content-scroll .ui-modal-close:before {
  font-size: 1.75rem;
}

.ui-modal.canvas .ui-modal-box {
  display: block;
  width: auto;
  min-width: 80vw;
  height: auto;
  min-height: 100%;
  margin: 0 auto;
  background: #FFF;
  position: relative;
}

.ui-modal.canvas .ui-modal-box .ui-modal-head,
.ui-modal.canvas .ui-modal-box .ui-modal-content {
  width: 100%;
  max-width: 75rem;
  padding-right: 5rem;
  padding-left: 5rem;
  margin: 0 auto;
  position: relative;
}

.ui-modal.canvas .ui-modal-box .ui-modal-head {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
  text-align: center;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-size: 1.625rem;
}

.ui-modal.canvas .ui-modal-box .ui-modal-head .ui-modal-close {
  top: 3.25rem;
  right: 5.25rem;
}

.ui-modal.canvas .ui-modal-box .ui-modal-content {
  padding-top: 0;
  padding-bottom: 5rem;
}

.ui-modal.canvas .ui-modal-box .ui-modal-content header {
  margin-bottom: 4rem;
}

.ui-modal.canvas .ui-modal-box .ui-modal-content header .modal-search {
  display: block;
  width: 100%;
  margin: 0 auto;
  position: relative;
}

.ui-modal.canvas .ui-modal-box .ui-modal-content header .modal-search:before {
  font-family: "glyphs";
  content: "";
  width: 1.5rem;
  height: 1.5rem;
  color: #c1cfe6;
  font-size: 1.525rem;
  line-height: 1.5rem;
  text-align: center;
  position: absolute;
  top: 50%;
  right: 0.75rem;
  margin-top: -0.725rem;
}

.ui-modal.canvas .ui-modal-box .ui-modal-content header .modal-search input {
  height: 3rem;
  font-size: 1.25rem;
  line-height: 3rem;
}

.ui-modal.canvas .ui-modal-box .ui-form {
  text-align: left;
}

.ui-modal.canvas .ui-modal-footer {
  padding-bottom: 2rem;
}

.ui-modal.is-open {
  display: block;
}

@media (max-width: 1280px) {
  .ui-modal .ui-modal-box {
    min-width: 70vw;
  }
}

@media (max-width: 1024px) {
  .ui-modal .ui-modal-box {
    min-width: 80vw;
    max-width: 95vw;
    min-height: 25vh;
    max-height: 95vh;
  }
}

@media (max-width: 768px) {
  .ui-modal .ui-modal-box {
    min-width: 90vw;
    max-width: 100vw;
    max-height: 100vh;
  }
  .ui-modal .ui-modal-box .ui-modal-close {
    top: 0.25rem;
    right: 0.25rem;
  }
  .ui-modal .ui-modal-box .ui-modal-content {
    padding: 2rem;
  }
}

@media (max-width: 400px) {
  .ui-modal .ui-modal-box {
    min-height: 15vh;
  }
}

/* ----- GRIDS:APPS ---- */
.grid-apps {
  margin-left: 4px;
  text-align: left;
  user-select: none;
}

.grid-apps > li,
.grid-apps .app-entity {
  display: inline-block;
  width: 14%;
  margin-right: 3.2%;
  vertical-align: top;
  margin-left: -0.25rem;
  margin-bottom: 2rem;
  position: relative;
}

.grid-apps > li .app-name,
.grid-apps > li .app-icon,
.grid-apps > li .button-icon,
.grid-apps .app-entity .app-name,
.grid-apps .app-entity .app-icon,
.grid-apps .app-entity .button-icon {
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
  transform: translateY(0);
  transition: transform 0.15s ease-in;
  margin: 0 auto;
  text-align: center;
}

.grid-apps > li .app-name,
.grid-apps .app-entity .app-name {
  display: block;
  font-size: 1rem;
  line-height: 1.25em;
  font-weight: 600;
  margin-top: 0.75rem;
  position: relative;
}

.grid-apps > li .box-expansion,
.grid-apps .app-entity .box-expansion {
  display: none;
  width: 100vw;
  margin: 1.75rem 0 0 0;
  padding: 3rem 0;
  position: relative;
}

.grid-apps > li .box-expansion:before,
.grid-apps .app-entity .box-expansion:before {
  display: block;
  content: "";
  width: 100vw;
  height: 100%;
  background-color: #e2ebfb;
  position: absolute;
  top: 0;
  left: -10.5vw;
}

.grid-apps > li .box-expansion .box-expansion-content,
.grid-apps .app-entity .box-expansion .box-expansion-content {
  position: relative;
  z-index: 5;
}

.grid-apps > li .box-expansion .box-expansion-content > h2,
.grid-apps > li .box-expansion .box-expansion-content > h3,
.grid-apps .app-entity .box-expansion .box-expansion-content > h2,
.grid-apps .app-entity .box-expansion .box-expansion-content > h3 {
  margin-bottom: 2em;
  text-align: center;
}

.grid-apps > li .box-expansion .box-expansion-content .button-text.block,
.grid-apps > li .box-expansion .box-expansion-content .ui-box.app + .ui-box.app,
.grid-apps .app-entity .box-expansion .box-expansion-content .button-text.block,
.grid-apps .app-entity .box-expansion .box-expansion-content .ui-box.app + .ui-box.app {
  margin-top: 2rem;
}

.grid-apps > li .panel-close,
.grid-apps .app-entity .panel-close {
  position: absolute;
  top: 0.95rem;
  right: -10%;
  z-index: 5;
}

.grid-apps > li:nth-child(1n+1) .box-expansion,
.grid-apps .app-entity:nth-child(1n+1) .box-expansion {
  margin-left: calc(22.857142857% - 9.375rem);
}

.grid-apps > li:nth-child(2n+2) .box-expansion,
.grid-apps .app-entity:nth-child(2n+2) .box-expansion {
  margin-left: calc(-100% - 9.375rem);
}

.grid-apps > li:nth-child(3n+3) .box-expansion,
.grid-apps .app-entity:nth-child(3n+3) .box-expansion {
  margin-left: calc(-222.857142857% - 9.375rem);
}

.grid-apps > li:nth-child(4n+4) .box-expansion,
.grid-apps .app-entity:nth-child(4n+4) .box-expansion {
  margin-left: calc(-345.714285714% - 9.375rem);
}

.grid-apps > li:nth-child(5n+5) .box-expansion,
.grid-apps .app-entity:nth-child(5n+5) .box-expansion {
  margin-left: calc(-468.571428571% - 9.375rem);
}

.grid-apps > li:nth-child(6n+6),
.grid-apps .app-entity:nth-child(6n+6) {
  margin-right: 0;
}

.grid-apps > li:nth-child(6n+6) .box-expansion,
.grid-apps .app-entity:nth-child(6n+6) .box-expansion {
  margin-left: calc(-591.428571429% - 9.375rem);
}

.grid-apps > li:not(.forbidden):hover .app-icon,
.grid-apps > li:not(.forbidden):hover .button-icon,
.grid-apps .app-entity:not(.forbidden):hover .app-icon,
.grid-apps .app-entity:not(.forbidden):hover .button-icon {
  -moz-transform: translateY(-5px);
  -o-transform: translateY(-5px);
  -ms-transform: translateY(-5px);
  -webkit-transform: translateY(-5px);
  transform: translateY(-5px);
  transition: transform 0.2s ease-out;
}

.grid-apps > li:not(.forbidden):hover .app-name,
.grid-apps .app-entity:not(.forbidden):hover .app-name {
  -moz-transform: translateY(5px);
  -o-transform: translateY(5px);
  -ms-transform: translateY(5px);
  -webkit-transform: translateY(5px);
  transform: translateY(5px);
  transition: transform 0.2s ease-out;
}

.grid-apps > li:not(.forbidden).is-active .app-icon,
.grid-apps > li:not(.forbidden).is-active .button-icon,
.grid-apps .app-entity:not(.forbidden).is-active .app-icon,
.grid-apps .app-entity:not(.forbidden).is-active .button-icon {
  border: 1px solid #c1cfe6;
}

.grid-apps > li:not(.forbidden).is-active .app-name:before,
.grid-apps .app-entity:not(.forbidden).is-active .app-name:before {
  display: block;
  width: 2rem;
  height: 2rem;
  font-family: "glyphs";
  content: "";
  color: #e0e9f9;
  font-size: 2rem;
  line-height: 2rem;
  position: absolute;
  bottom: -2.25rem;
  left: 50%;
  margin-left: -1rem;
  z-index: 1;
}

.grid-apps > li:not(.forbidden).is-active .box-expansion,
.grid-apps .app-entity:not(.forbidden).is-active .box-expansion {
  display: block;
}

.sidebar-is-active .ui-modal.canvas {
  width: calc(100% - 16.25rem) !important;
}

@media (max-width: 1024px) {
  .sidebar-is-active .ui-modal.canvas .ui-modal-box {
    min-width: auto;
  }
  .sidebar-is-active .ui-modal.canvas .ui-modal-box .ui-modal-content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sidebar-is-active .grid-apps > li,
  .sidebar-is-active .grid-apps .app-entity {
    width: 17.2%;
    margin-right: 3.5%;
  }
  .sidebar-is-active .grid-apps > li:nth-child(6n+6),
  .sidebar-is-active .grid-apps .app-entity:nth-child(6n+6) {
    margin-right: 3.5%;
  }
  .sidebar-is-active .grid-apps > li:nth-child(5n+5),
  .sidebar-is-active .grid-apps .app-entity:nth-child(5n+5) {
    margin-right: 0;
  }
}

@-webkit-keyframes bounce-right {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(-20px);
    -o-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  60% {
    -moz-transform: translateX(-10px);
    -o-transform: translateX(-10px);
    -ms-transform: translateX(-10px);
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
}

@-moz-keyframes bounce-right {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(-20px);
    -o-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  60% {
    -moz-transform: translateX(-10px);
    -o-transform: translateX(-10px);
    -ms-transform: translateX(-10px);
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
}

@-o-keyframes bounce-right {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(-20px);
    -o-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  60% {
    -moz-transform: translateX(-10px);
    -o-transform: translateX(-10px);
    -ms-transform: translateX(-10px);
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
}

@keyframes bounce-right {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(-20px);
    -o-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }
  60% {
    -moz-transform: translateX(-10px);
    -o-transform: translateX(-10px);
    -ms-transform: translateX(-10px);
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
}

@-webkit-keyframes bounce-left {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(20px);
    -o-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  60% {
    -moz-transform: translateX(10px);
    -o-transform: translateX(10px);
    -ms-transform: translateX(10px);
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
}

@-moz-keyframes bounce-left {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(20px);
    -o-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  60% {
    -moz-transform: translateX(10px);
    -o-transform: translateX(10px);
    -ms-transform: translateX(10px);
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
}

@-o-keyframes bounce-left {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(20px);
    -o-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  60% {
    -moz-transform: translateX(10px);
    -o-transform: translateX(10px);
    -ms-transform: translateX(10px);
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
}

@keyframes bounce-left {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateX(0);
    -o-transform: translateX(0);
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  40% {
    -moz-transform: translateX(20px);
    -o-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }
  60% {
    -moz-transform: translateX(10px);
    -o-transform: translateX(10px);
    -ms-transform: translateX(10px);
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
}

.new-ui {
  /*
<div class="ui-spinner large">
	<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	width="" height="" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">
		<path opacity="0.2" fill="#000" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946
		s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634
		c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"/>
		<path fill="#000" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0
		C22.32,8.481,24.301,9.057,26.013,10.047z">
		<animateTransform attributeType="xml"
		  attributeName="transform"
		  type="rotate"
		  from="0 20 20"
		  to="360 20 20"
		  dur="1s"
		  repeatCount="indefinite"/>
		</path>
	</svg>
</div>
*/
  /*

GRIDS:

- Grid sizes define width percentages based on predefined values. Example: 5, 12, 24. Generated grid classes (grid-4-12) can be used to define the width of elements regardless of their parent container. An element with a grid class (grid-4-12) will only behave as a grid element if placed inside a parent container with a class of "grid".

- Different widths can be used together to achieve different grid combinations. Example:

| grid-1-5 | grid-3-12 | grid-1-5 |

| 20% | 60% | 20% |

- Grids are only responsive if the class "responsive" is added to the parent container with class "grid".


COLUMNS:

- Columns can only contain "column" elements.
- Columns gutters are defined by adding the classes "gutter-xsmall, gutter-small, gutter-medium, gutter-large, gutter-xlarge" to the parent element. If gutter is not defined, columns will use the default gutter "1rem".
- Columns are inherently responsive.

*/
  /* _____

GRID SIZES:
- Defines grid-based width percentages.
- Can be used on any element, indepentedly from the .grid element.

---- */
  /* ---- GRID & COLUMNS ---- */
  /* ----- GRIDS ---- */
  /* ----- COLUMNS ---- */
  /* ===== TEMPLATE BASE STYLES  ===== */
  /* Todo: general classes for all cursors? */
}

.new-ui *[class*="glyph-"]:before {
  display: inline;
  color: inherit;
  vertical-align: top;
  line-height: inherit;
  font-size: inherit;
  font-family: "glyphs";
  font-weight: normal;
  text-align: right;
  padding-right: 0.25rem;
}

.new-ui *[class*="glyph-"].align-right:before {
  padding-right: 0;
  padding-left: 0.5rem;
  float: right;
}

.new-ui *[class*="glyph-"].ui-tool {
  display: inline-block;
  width: auto;
  min-width: 1.25rem;
  height: 1.25rem;
}

.new-ui *[class*="glyph-"].ui-tool:before {
  display: block;
  width: 1.25rem;
  height: 1.25rem;
}

.new-ui *[class*="glyph-"].ui-tool.notext {
  font-size: 0;
}

.new-ui *[class*="glyph-"].ui-tool.notext:before {
  font-size: inherit;
}

.new-ui .ui-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  text-align: center;
  vertical-align: top;
}

.new-ui .ui-spinner svg {
  width: 100%;
  height: 100%;
}

.new-ui .ui-spinner svg path,
.new-ui .ui-spinner svg rect {
  fill: #5d9bfc;
}

.new-ui .ui-spinner.absolute {
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}

.new-ui .ui-spinner.purple svg path,
.new-ui .ui-spinner.purple svg rect {
  fill: #686cbe;
}

.new-ui .ui-spinner.green svg path,
.new-ui .ui-spinner.green svg rect {
  fill: #50a475;
}

.new-ui .ui-spinner.gray svg path,
.new-ui .ui-spinner.gray svg rect {
  fill: #304565;
}

.new-ui .ui-spinner.centered {
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}

.new-ui .ui-spinner.small {
  width: 1rem;
  height: 1rem;
}

.new-ui .ui-spinner.medium {
  width: 1.75rem;
  height: 1.75rem;
}

.new-ui .ui-spinner.large {
  width: 3rem;
  height: 3rem;
}

.new-ui .ui-spinner.xlarge {
  width: 5rem;
  height: 5rem;
}

.new-ui .ui-spinner.xxarge {
  width: 8rem;
  height: 8rem;
}

.new-ui .menu-vertical {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.new-ui .menu-vertical,
.new-ui .menu-vertical > ul {
  display: block;
  width: 100%;
  height: auto;
}

.new-ui .menu-vertical > li,
.new-ui .menu-vertical > ul > li {
  display: block;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  color: rgba(186, 186, 186, 0.5);
  cursor: pointer;
  z-index: 2;
}

.new-ui .menu-vertical > li a,
.new-ui .menu-vertical > ul > li a {
  display: block;
  width: 100%;
  height: 100%;
  color: inherit;
}

.new-ui .menu-vertical > li[class*="ui-marker-"]:after,
.new-ui .menu-vertical > ul > li[class*="ui-marker-"]:after {
  top: 50%;
  left: 1.5rem;
  margin-top: -size(marker)/2;
}

.new-ui .menu-vertical[role="navigation"] > li,
.new-ui .menu-vertical[role="navigation"] > ul > li, .new-ui .menu-vertical.sidebar > li,
.new-ui .menu-vertical.sidebar > ul > li {
  padding: 0.75rem 1.25rem;
}

.new-ui .menu-vertical.dark {
  background: #1c2c3e;
}

.new-ui .menu-vertical.dark > li,
.new-ui .menu-vertical.dark > ul > li {
  color: #cfdce2;
}

.new-ui .menu-vertical.dark > li:hover,
.new-ui .menu-vertical.dark > ul > li:hover {
  background: rgba(104, 108, 190, 0.6);
  transition: all 0.25s ease-out;
}

.new-ui .menu-vertical.dark > li.active,
.new-ui .menu-vertical.dark > ul > li.active {
  background: #686cbe;
  color: #FFF;
  transition: all 0.25s ease-out;
}

.new-ui .menu-vertical.light {
  background: #e5eaef;
}

.new-ui .menu-vertical.light > li,
.new-ui .menu-vertical.light > ul > li {
  color: #b4c0d1;
}

.new-ui .menu-vertical.light > li:hover,
.new-ui .menu-vertical.light > ul > li:hover {
  background: rgba(128, 178, 255, 0.6);
  transition: all 0.25s ease-out;
}

.new-ui .menu-vertical.light > li.active,
.new-ui .menu-vertical.light > ul > li.active {
  background: #80b2ff;
  color: #FFF;
  transition: all 0.25s ease-out;
}

.new-ui .menu-horizontal {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.new-ui .menu-horizontal,
.new-ui .menu-horizontal > ul {
  display: block;
  width: 100%;
  height: auto;
  margin-top: 0;
}

.new-ui .menu-horizontal > li,
.new-ui .menu-horizontal > ul > li {
  display: inline-block;
  width: auto;
  box-sizing: border-box;
  position: relative;
  margin-left: -rem(4px);
  white-space: nowrap;
}

.new-ui .menu-horizontal > li *,
.new-ui .menu-horizontal > ul > li * {
  white-space: normal;
}

.new-ui .menu-horizontal > li a,
.new-ui .menu-horizontal > ul > li a {
  display: block;
  width: 100%;
  height: 100%;
}

.new-ui .menu-horizontal.style-pipes li {
  position: relative;
  margin-right: 0.75rem;
  padding-right: 0.75rem;
}

.new-ui .menu-horizontal.style-pipes li:after {
  width: 1px;
  height: 85%;
  top: 8%;
  right: 0;
  background: rgba(186, 186, 186, 0.75);
}

.new-ui .menu-horizontal.style-pipes li:last-child {
  margin-right: 0;
  padding-right: 0;
}

.new-ui .menu-horizontal.style-pipes li:last-child:after {
  display: none;
}

.new-ui .menu-horizontal.style-tabs {
  text-align: left;
}

.new-ui .menu-horizontal.style-tabs,
.new-ui .menu-horizontal.style-tabs > ul {
  box-shadow: inset 0 -1px 0 0 #d8e1f0;
}

.new-ui .menu-horizontal.style-tabs > li,
.new-ui .menu-horizontal.style-tabs > ul > li {
  padding: 1.25rem;
  font-size: 1rem;
  cursor: pointer;
}

.new-ui .menu-horizontal.style-tabs > li.active, .new-ui .menu-horizontal.style-tabs > li.is-active,
.new-ui .menu-horizontal.style-tabs > ul > li.active,
.new-ui .menu-horizontal.style-tabs > ul > li.is-active {
  box-shadow: inset 0 -3px 0 0 #5d9bfc;
}

.new-ui .main-menu {
  display: inline-block;
  width: auto;
  height: 4rem;
  padding: 0;
}

.new-ui .main-menu a {
  color: #FFF;
  font-weight: 600;
}

.new-ui .main-menu > ul {
  display: block;
  width: 100%;
  height: 4rem;
}

.new-ui .main-menu > ul > li {
  display: inline-block;
  height: 4rem;
  padding: 0 2rem;
  vertical-align: top;
  position: relative;
}

.new-ui .main-menu > ul > li > a {
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  display: block;
  width: auto;
  height: 4rem;
  font-size: 0.925rem;
  line-height: 4rem;
  font-weight: 500;
}

.new-ui .main-menu > ul > li .submenu {
  display: none;
  width: auto;
  min-width: 30rem;
  max-width: 50rem;
  height: auto;
  padding: 1.5rem 0;
  background: #5d9bfc;
  position: absolute;
  top: 4rem;
  left: 0;
  z-index: 5000;
}

.new-ui .main-menu > ul > li .submenu .submenu-group {
  padding: 1.5rem 0;
  border-bottom: 1px solid rgba(51, 120, 227, 0.65);
}

.new-ui .main-menu > ul > li .submenu .submenu-group > li {
  padding: 0.5rem 2rem;
}

.new-ui .main-menu > ul > li .submenu .submenu-group:first-child {
  padding-top: 0;
}

.new-ui .main-menu > ul > li .submenu .submenu-group:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}

.new-ui .main-menu > ul > li .submenu.is-open {
  display: block;
}

.new-ui .main-menu > ul > li:after {
  display: none;
  content: "";
  width: calc(100% - 3.5rem);
  height: 3px;
  background: #5d9bfc;
  position: absolute;
  bottom: 0;
  left: 1.75rem;
  opacity: 0;
}

.new-ui .main-menu > ul > li.has-menu {
  padding-right: 4rem;
}

.new-ui .main-menu > ul > li.has-menu:before {
  font-family: "glyphs";
  display: "inline";
  display: block;
  content: "";
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
  position: absolute;
  top: 50%;
  right: 1.35rem;
  margin-top: -1rem;
  color: #686cbe;
}

.new-ui .main-menu > ul > li.has-menu.is-open:after {
  display: none !important;
}

.new-ui .main-menu > ul > li:hover:after {
  display: block;
  background: #686cbe;
  opacity: 1;
  transition: opacity 0.3s ease-out;
}

.new-ui .main-menu > ul > li.active:after, .new-ui .main-menu > ul > li.is-active:after {
  display: block;
  background: #686cbe;
  opacity: 1;
}

.new-ui .main-menu > ul > li.is-open {
  background: #5d9bfc;
}

.new-ui .main-menu > ul > li.is-open.has-menu:before {
  color: #28374d;
}

.new-ui .page-menu {
  height: 5.625rem;
  position: relative;
}

.new-ui .page-menu li {
  height: 5.625rem;
  line-height: 5.625rem;
  font-size: 1.25rem;
  font-weight: normal;
  letter-spacing: 0;
  margin-right: 2rem;
  cursor: pointer;
}

.new-ui .page-menu li a {
  height: 5.625rem;
  color: #505050;
}

.new-ui .page-menu .menu-toggle {
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -1.5rem;
  cursor: pointer;
}

.new-ui .page-menu .menu-toggle:before {
  color: #bababa;
}

.new-ui .page-menu .menu-toggle:hover:before {
  color: #5d9bfc;
  transition: color 0.1s ease-out;
}

.new-ui .page-menu .menu-toggle.is-active:before, .new-ui .page-menu .menu-toggle.is-open:before {
  content: "";
  color: #5d9bfc;
}

.new-ui .top-menu {
  width: 100%;
  height: auto;
  margin-bottom: 2rem;
}

.new-ui .top-menu li {
  height: 2.25rem;
}

.new-ui .top-menu li a {
  padding: 0 1.5rem;
  color: #2f313a;
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 2.25rem;
  letter-spacing: 1px;
}

.new-ui .top-menu li a .stat {
  color: #5d9bfc;
}

.new-ui .top-menu li:hover {
  background: #e0e0e0;
}

.new-ui .top-menu .menu-toggle {
  height: 3.75rem;
  font-size: 1.25rem;
  font-weight: normal;
  line-height: 3.75rem;
  text-transform: uppercase;
  color: #bababa;
  padding-left: 2.25rem;
}

.new-ui .top-menu .menu-toggle:before {
  width: 3.75rem;
  height: 3.75rem;
  line-height: 3.75rem;
  font-size: 1.825rem;
  text-indent: -5px !important;
  text-align: center;
  position: absolute;
  left: auto;
  top: 0;
  right: 0;
}

.new-ui .top-menu .menu-panel {
  padding-bottom: 1rem;
}

.new-ui .top-menu .menu-panel li {
  height: 2.5rem;
  line-height: 2.5rem;
}

.new-ui .top-menu .menu-panel li a {
  color: #313131;
}

.new-ui .top-menu .menu-panel li.active a, .new-ui .top-menu .menu-panel li:hover a {
  color: #bababa;
  transition: color 0.1s ease-out;
}

.new-ui .top-menu .sign-out,
.new-ui .top-menu .support {
  display: none;
}

.new-ui .top-menu.is-open {
  background: #000;
}

.new-ui .top-menu.is-open .menu-toggle {
  color: #5d9bfc;
}

.new-ui .bookmark-menu {
  clear: both;
  display: block;
  width: 100vw;
  height: 4rem;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}

.new-ui .bookmark-menu:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .bookmark-menu > ul,
.new-ui .bookmark-menu .links > ul {
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  display: inline-block;
  width: auto;
  height: 4rem;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  position: absolute;
}

.new-ui .bookmark-menu > ul > li,
.new-ui .bookmark-menu .links > ul > li {
  display: inline-block;
  position: relative;
  width: auto;
  height: auto;
  padding: 0 1rem;
  line-height: 4rem;
  vertical-align: middle;
}

.new-ui .bookmark-menu > ul > li a,
.new-ui .bookmark-menu .links > ul > li a {
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-weight: 500;
  font-size: 1rem;
}

.new-ui .bookmark-menu .links {
  display: inline-block;
  width: auto;
  height: 4rem;
  padding: 0 4rem;
  position: relative;
  overflow: hidden;
}

.new-ui .bookmark-menu .links .prev,
.new-ui .bookmark-menu .links .next {
  background: #1c2c3e;
  display: block;
  width: 4rem;
  height: 4rem;
  position: absolute;
  top: 0;
  font-size: 0;
}

.new-ui .bookmark-menu .links .prev:before,
.new-ui .bookmark-menu .links .next:before {
  font-family: "glyphs";
  display: "block";
  width: 4rem;
  height: 4rem;
  line-height: 4rem;
  font-size: 1.325rem;
  text-align: center;
  color: #686cbe;
}

.new-ui .bookmark-menu .links .prev {
  left: 0;
}

.new-ui .bookmark-menu .links .prev:before {
  content: "";
}

.new-ui .bookmark-menu .links .next {
  right: 0;
}

.new-ui .bookmark-menu .links .next:before {
  content: "";
}

.new-ui .bookmark-menu.dark {
  background: #1c2c3e;
  box-shadow: 0 5px 1px 0 rgba(28, 44, 62, 0.175);
}

.new-ui .bookmark-menu.dark ul li a {
  color: #5d9bfc;
}

.new-ui .bookmark-menu.dark ul li:hover a {
  color: #a8c9fc;
}

.new-ui .bookmark-menu.dark ul li.is-active a, .new-ui .bookmark-menu.dark ul li.active a {
  color: #FFF;
}

.new-ui .float-menu {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  box-shadow: 3px 3px 6px 0 rgba(93, 117, 154, 0.15);
  background-color: #eff5ff;
  display: inline-block;
  padding: 1rem;
  width: auto;
  height: auto;
}

.new-ui .float-menu[class*="pointer-"]:before, .new-ui .float-menu[class*="pointer-"]:after {
  display: block;
  font-family: "glyphs";
  width: 10px;
  font-size: 21px;
  line-height: 27px;
  text-align: left;
  position: absolute;
}

.new-ui .float-menu[class*="pointer-"]:before {
  color: #eff5ff;
  text-align: left;
  z-index: 6;
}

.new-ui .float-menu[class*="pointer-"]:after {
  color: #c1cfe6;
  z-index: 5;
}

.new-ui .float-menu.pointer-left:before, .new-ui .float-menu.pointer-left:after, .new-ui .float-menu.pointer-right:before, .new-ui .float-menu.pointer-right:after {
  top: 12px;
}

.new-ui .float-menu.pointer-left.center:before, .new-ui .float-menu.pointer-left.center:after, .new-ui .float-menu.pointer-right.center:before, .new-ui .float-menu.pointer-right.center:after {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
}

.new-ui .float-menu.pointer-up:before, .new-ui .float-menu.pointer-up:after, .new-ui .float-menu.pointer-down:before, .new-ui .float-menu.pointer-down:after {
  left: 12px;
}

.new-ui .float-menu.pointer-up.center:before, .new-ui .float-menu.pointer-up.center:after, .new-ui .float-menu.pointer-down.center:before, .new-ui .float-menu.pointer-down.center:after {
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  left: 50%;
  margin-left: -8px;
}

.new-ui .float-menu.pointer-left:before, .new-ui .float-menu.pointer-left:after {
  content: "";
}

.new-ui .float-menu.pointer-left:before {
  left: -12px;
}

.new-ui .float-menu.pointer-left:after {
  left: -14px;
}

.new-ui .float-menu.pointer-right:before, .new-ui .float-menu.pointer-right:after {
  content: "";
}

.new-ui .float-menu.pointer-right:before {
  right: 0;
}

.new-ui .float-menu.pointer-right:after {
  right: -2px;
}

.new-ui .float-menu.pointer-up:before, .new-ui .float-menu.pointer-up:after {
  content: "";
}

.new-ui .float-menu.pointer-up:before {
  top: -14px;
}

.new-ui .float-menu.pointer-up:after {
  top: -16px;
}

.new-ui .float-menu.pointer-down:before, .new-ui .float-menu.pointer-down:after {
  content: "";
}

.new-ui .float-menu.pointer-down:before {
  bottom: -16px;
}

.new-ui .float-menu.pointer-down:after {
  bottom: -18px;
}

.new-ui .float-menu li {
  font-size: 0.9rem;
  font-weight: 600;
  color: #5d759a;
  text-align: left;
  white-space: nowrap;
  margin-bottom: 0.5rem;
}

.new-ui .float-menu li:last-child {
  margin-bottom: 0;
}

.new-ui .float-menu li:hover {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui #top-menu {
  z-index: 2000;
  top: 30px;
}

.new-ui *[class*="ui-tag"] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: inline-block;
  width: auto;
  height: 1.75em;
  padding: 0 0.75em 0 0.75em;
  font-size: 0.85rem;
  line-height: 1.75em;
  font-weight: 600;
  border-radius: 0.25rem;
  color: #FFF;
  position: relative;
}

.new-ui *[class*="ui-tag"] .tag-delete,
.new-ui *[class*="ui-tag"] .tag-close {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  position: static;
  font-size: 0;
}

.new-ui *[class*="ui-tag"] .tag-delete:before,
.new-ui *[class*="ui-tag"] .tag-close:before {
  font-family: "glyphs";
  display: block;
  content: "";
  width: 1rem;
  height: 1rem;
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.35);
  mix-blend-mode: overlay;
  text-align: center;
  line-height: 1rem;
  position: absolute;
  top: 50%;
  right: 0.25rem;
  margin-top: -0.5rem;
}

.new-ui *[class*="ui-tag"] .tag-delete:hover:before,
.new-ui *[class*="ui-tag"] .tag-close:hover:before {
  color: rgba(0, 0, 0, 0.65);
  transition: color 0.2s ease-out;
}

.new-ui *[class*="ui-tag"].small, .new-ui *[class*="ui-tag"].mini {
  height: 1.5rem;
  line-height: 1.5rem;
  font-size: 0.675rem;
}

.new-ui *[class*="ui-tag"].xsmall {
  height: 1rem;
  line-height: 1rem;
  font-size: 0.575rem;
  padding: 0 0.5em 0 0.5em;
}

.new-ui .ui-tag {
  background: #a7b8d6;
}

.new-ui .ui-tag.number {
  padding: 0 0.5em;
  font-size: 0.575rem;
  background-color: #d9e6fa;
  box-shadow: inset 0 0 0 1px #c1cfe6;
  color: #758ba9;
  font-weight: 800;
  position: absolute;
}

.new-ui .ui-tag-warning,
.new-ui .ui-tag-yellow {
  background: #f0d886;
}

.new-ui .ui-tag-alert,
.new-ui .ui-tag-error,
.new-ui .ui-tag-red {
  background: #e26464;
}

.new-ui .ui-tag-success,
.new-ui .ui-tag-active,
.new-ui .ui-tag-green {
  background: #50a475;
}

.new-ui .ui-tag-info,
.new-ui .ui-tag-blue {
  background: #80b2ff;
}

.new-ui .ui-tag-location {
  background: #FFF;
  color: #2f313a;
  font-size: 0.9rem;
  font-weight: 600;
}

.new-ui .ui-tag-pocket {
  height: auto;
  font-size: 1.125em;
  line-height: 1em;
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  color: #758ba9;
  border: 1px solid #c1cfe6;
  background-color: #FFF;
  position: relative;
  cursor: pointer;
}

.new-ui .ui-tag-pocket .label {
  display: inline-block;
  font-size: 10px;
  line-height: 0.75em;
  font-weight: 800;
  color: #c1cfe6;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-left: -0.5rem;
  vertical-align: middle;
}

.new-ui .ui-tag-pocket:before {
  font-family: "glyphs";
  display: block;
  width: 1.25rem;
  height: 1.25rem;
  line-height: 1.25rem;
  font-size: 0.925em;
  position: absolute;
  left: 0.75rem;
  top: 50%;
  margin-top: -0.625rem;
}

.new-ui .ui-tag-pocket.include:before {
  content: "";
  color: #5d9bfc;
}

.new-ui .ui-tag-pocket.exclude:before {
  content: "";
  color: #e26464;
}

.new-ui .ui-tag-pocket.edit {
  color: #c1cfe6;
  border-color: #c1cfe6;
  cursor: pointer;
}

.new-ui .ui-tag-pocket.edit:before {
  content: "";
  color: #c1cfe6;
}

.new-ui .ui-tag-pocket.edit:hover {
  color: #FFF;
  background-color: #c1cfe6;
  border-color: #c1cfe6;
  transition: all 0.15s ease-out;
}

.new-ui .ui-tag-pocket.edit:hover:before {
  color: #FFF;
  transition: color 0.15s ease-out;
}

.new-ui .ui-tag-pocket.large {
  font-size: 1.325rem;
}

.new-ui .tag-role {
  font-size: 0.825rem;
  padding: 0 0.5em 0 0.5em;
  height: 1.5rem;
  line-height: 1.5rem;
}

.new-ui .tag-list,
.new-ui .tag-lists,
.new-ui .list-tags {
  text-align: left;
  white-space: normal;
}

.new-ui .tag-list *[class*="ui-tag"],
.new-ui .tag-lists *[class*="ui-tag"],
.new-ui .list-tags *[class*="ui-tag"] {
  margin-bottom: 0.425rem !important;
  margin-right: 0.275rem;
  vertical-align: top;
  float: none !important;
}

.new-ui .tag-list *[class*="ui-tag"].tag-role,
.new-ui .tag-lists *[class*="ui-tag"].tag-role,
.new-ui .list-tags *[class*="ui-tag"].tag-role {
  margin-right: 0.15rem;
}

.new-ui .tag-list.inline-group,
.new-ui .tag-lists.inline-group,
.new-ui .list-tags.inline-group {
  display: inline-block;
  margin-left: 1rem;
}

.new-ui .tag-holder {
  width: auto;
  height: 1.5rem;
}

.new-ui .tag-holder .tag-label {
  text-transform: uppercase;
  margin-right: 0.5rem;
  color: #7e7e7e;
}

.new-ui .ui-badge-circle {
  width: 3rem;
  height: 3rem;
  border-radius: 100%;
  font-size: 1.75rem;
  line-height: 3rem;
  text-align: center;
  font-weight: 800;
  color: #c8e1f5;
  box-shadow: inset 0 0 0 4px #c8e1f5;
}

.new-ui .ui-badge-circle.large {
  width: 4rem;
  height: 4rem;
  font-size: 2.25rem;
  line-height: 4rem;
}

.new-ui *[class*="ui-marker-"]:after {
  display: block;
  content: "";
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 100%;
}

.new-ui .ui-marker-alert:after,
.new-ui .ui-marker-error:after {
  background: #e26464;
}

.new-ui .ui-marker-live:after,
.new-ui .ui-marker-success:after {
  background: #50a475;
}

.new-ui .ui-marker-warning:after {
  background: #f0d886;
}

.new-ui .ui-marker-information:after,
.new-ui .ui-marker-info:after {
  background: #80b2ff;
}

.new-ui .ui-count {
  display: block;
  width: 17px;
  height: 17px;
  border-radius: 17px;
  text-align: center;
  font-weight: 800;
  font-size: 9px;
  line-height: 15px;
  color: #FFF;
  letter-spacing: -1px;
  text-indent: -1px;
  background-color: #5d9bfc;
}

.new-ui .ui-box {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  position: relative;
}

.new-ui .ui-box .ui-box-close {
  width: 1rem;
  height: 1rem;
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  z-index: 5;
}

.new-ui .ui-box .ui-box-close:before {
  font-size: 0.75rem;
  line-height: 1rem;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.65);
}

.new-ui .ui-box .ui-box-close:hover {
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
  transform: scale(1);
}

.new-ui .ui-box .ui-box-close:hover:before {
  color: #5d759a;
}

.new-ui .ui-box.padding {
  padding: 2rem;
}

.new-ui .ui-box.padding-small {
  padding: 1rem;
}

.new-ui .ui-box[class*="border-"]:after {
  display: block;
  content: "";
  position: absolute;
}

.new-ui .ui-box[class*="border-"][class*="-primary"]:after, .new-ui .ui-box[class*="border-"][class*="-blue"]:after {
  background-color: #5d9bfc;
}

.new-ui .ui-box[class*="border-"][class*="secondary"]:after, .new-ui .ui-box[class*="border-"][class*="-purple"]:after {
  background-color: #686cbe;
}

.new-ui .ui-box[class*="border-"][class*="tertiary"]:after, .new-ui .ui-box[class*="border-"][class*="-green"]:after {
  background-color: #50a475;
}

.new-ui .ui-box[class*="border-left"]:after, .new-ui .ui-box[class*="border-right"]:after {
  width: 0.25rem;
  height: 100%;
  top: 0;
}

.new-ui .ui-box[class*="border-top"]:after, .new-ui .ui-box[class*="border-bottom"]:after {
  width: 100%;
  height: 4px;
  left: 0;
}

.new-ui .ui-box[class*="border-left"]:after {
  left: 0;
  border-top-left-radius: 0.1875rem;
  border-bottom-left-radius: 0.1875rem;
}

.new-ui .ui-box[class*="border-right"]:after {
  right: 0;
  border-top-right-radius: 0.1875rem;
  border-bottom-right-radius: 0.1875rem;
}

.new-ui .ui-box[class*="border-bottom"]:after {
  bottom: 0;
  border-bottom-left-radius: 0.1875rem;
  border-bottom-right-radius: 0.1875rem;
}

.new-ui .ui-box[class*="border-top"]:after {
  top: 0;
  border-top-left-radius: 0.1875rem;
  border-top-right-radius: 0.1875rem;
}

.new-ui .ui-box.float {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  box-shadow: 3px 3px 6px 0 rgba(93, 117, 154, 0.15);
}

.new-ui .ui-box.menu {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  box-shadow: 3px 3px 6px 0 rgba(93, 117, 154, 0.15);
  background-color: #eff5ff;
  display: inline-block;
  padding: 1rem;
  width: auto;
  height: auto;
}

.new-ui .ui-box.menu[class*="pointer-"]:before, .new-ui .ui-box.menu[class*="pointer-"]:after {
  display: block;
  font-family: "glyphs";
  width: 10px;
  font-size: 21px;
  line-height: 27px;
  text-align: left;
  position: absolute;
}

.new-ui .ui-box.menu[class*="pointer-"]:before {
  color: #eff5ff;
  text-align: left;
  z-index: 6;
}

.new-ui .ui-box.menu[class*="pointer-"]:after {
  color: #c1cfe6;
  z-index: 5;
}

.new-ui .ui-box.menu.pointer-left:before, .new-ui .ui-box.menu.pointer-left:after, .new-ui .ui-box.menu.pointer-right:before, .new-ui .ui-box.menu.pointer-right:after {
  top: 12px;
}

.new-ui .ui-box.menu.pointer-left.center:before, .new-ui .ui-box.menu.pointer-left.center:after, .new-ui .ui-box.menu.pointer-right.center:before, .new-ui .ui-box.menu.pointer-right.center:after {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
}

.new-ui .ui-box.menu.pointer-up:before, .new-ui .ui-box.menu.pointer-up:after, .new-ui .ui-box.menu.pointer-down:before, .new-ui .ui-box.menu.pointer-down:after {
  left: 12px;
}

.new-ui .ui-box.menu.pointer-up.center:before, .new-ui .ui-box.menu.pointer-up.center:after, .new-ui .ui-box.menu.pointer-down.center:before, .new-ui .ui-box.menu.pointer-down.center:after {
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  left: 50%;
  margin-left: -8px;
}

.new-ui .ui-box.menu.pointer-left:before, .new-ui .ui-box.menu.pointer-left:after {
  content: "";
}

.new-ui .ui-box.menu.pointer-left:before {
  left: -12px;
}

.new-ui .ui-box.menu.pointer-left:after {
  left: -14px;
}

.new-ui .ui-box.menu.pointer-right:before, .new-ui .ui-box.menu.pointer-right:after {
  content: "";
}

.new-ui .ui-box.menu.pointer-right:before {
  right: 0;
}

.new-ui .ui-box.menu.pointer-right:after {
  right: -2px;
}

.new-ui .ui-box.menu.pointer-up:before, .new-ui .ui-box.menu.pointer-up:after {
  content: "";
}

.new-ui .ui-box.menu.pointer-up:before {
  top: -14px;
}

.new-ui .ui-box.menu.pointer-up:after {
  top: -16px;
}

.new-ui .ui-box.menu.pointer-down:before, .new-ui .ui-box.menu.pointer-down:after {
  content: "";
}

.new-ui .ui-box.menu.pointer-down:before {
  bottom: -16px;
}

.new-ui .ui-box.menu.pointer-down:after {
  bottom: -18px;
}

.new-ui .ui-box.card {
  padding: 2rem;
}

.new-ui .ui-box.card .card-head {
  position: relative;
}

.new-ui .ui-box.card .card-head[class*="icon-"]:before, .new-ui .ui-box.card .card-head[class*="glyph-"]:before {
  width: 4rem;
  height: 4rem;
  font-size: 2.45rem;
  line-height: 4rem;
  text-align: center;
  position: absolute;
  top: 50%;
  left: -5rem;
  margin-top: -2rem;
}

.new-ui .ui-box.card .card-toggle {
  cursor: pointer;
}

.new-ui .ui-box.card .card-content {
  margin: 0;
}

.new-ui .ui-box.card[class*="-primary"] .card-head[class*="icon-"]:before, .new-ui .ui-box.card[class*="-primary"] .card-head[class*="glyph-"]:before, .new-ui .ui-box.card[class*="-blue"] .card-head[class*="icon-"]:before, .new-ui .ui-box.card[class*="-blue"] .card-head[class*="glyph-"]:before {
  color: #5d9bfc;
}

.new-ui .ui-box.card[class*="secondary"] .card-head[class*="icon-"]:before, .new-ui .ui-box.card[class*="secondary"] .card-head[class*="glyph-"]:before, .new-ui .ui-box.card[class*="-purple"] .card-head[class*="icon-"]:before, .new-ui .ui-box.card[class*="-purple"] .card-head[class*="glyph-"]:before {
  color: #686cbe;
}

.new-ui .ui-box.card[class*="tertiary"] .card-head[class*="icon-"]:before, .new-ui .ui-box.card[class*="tertiary"] .card-head[class*="glyph-"]:before, .new-ui .ui-box.card[class*="-green"] .card-head[class*="icon-"]:before, .new-ui .ui-box.card[class*="-green"] .card-head[class*="glyph-"]:before {
  color: #50a475;
}

.new-ui .ui-box.card.shadow:hover {
  box-shadow: 0 0 0 4px rgba(48, 69, 101, 0.065), 0 4px 1px -2px rgba(48, 69, 101, 0.075);
  border: 1px solid #b8c8e4;
}

.new-ui .ui-box.card.shadow[class*="-primary"]:hover, .new-ui .ui-box.card.shadow[class*="-blue"]:hover {
  box-shadow: 0 0 0 4px rgba(93, 155, 252, 0.175), 0 4px 1px -2px rgba(93, 155, 252, 0.15);
  border: 1px solid #6da7ff;
}

.new-ui .ui-box.card.shadow[class*="secondary"]:hover, .new-ui .ui-box.card.shadow[class*="-purple"]:hover {
  box-shadow: 0 0 0 4px rgba(104, 108, 190, 0.175), 0 4px 1px -2px rgba(104, 108, 190, 0.15);
  border: 1px solid #b6b9e6;
}

.new-ui .ui-box.card.shadow[class*="tertiary"]:hover, .new-ui .ui-box.card.shadow[class*="-green"]:hover {
  box-shadow: 0 0 0 4px rgba(80, 164, 117, 0.175), 0 4px 1px -2px rgba(80, 164, 117, 0.15);
  border: 1px solid #67b489;
}

.new-ui .ui-box.air {
  box-shadow: inset 0 0 0 1px #e0e0e0;
  margin-top: 2rem;
}

.new-ui .ui-box.air h2,
.new-ui .ui-box.air h3 {
  font-size: 1.15rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.new-ui .ui-box.air .grid {
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  min-height: 3.75rem;
}

.new-ui .ui-box.air .grid:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.new-ui .ui-box.app {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  background-color: #eff5ff;
  padding: 2rem;
  box-shadow: 0 3px 0 0 rgba(224, 233, 249, 0.45);
}

.new-ui .ui-box.app .ui-box-header,
.new-ui .ui-box.app .ui-box-footer {
  position: relative;
}

.new-ui .ui-box.app .ui-box-header:before,
.new-ui .ui-box.app .ui-box-footer:before {
  display: block;
  content: "";
  width: calc(100% + 4rem);
  height: 1px;
  background-color: #d8e1f0;
  position: absolute;
  left: -2rem;
}

.new-ui .ui-box.app .ui-box-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1.25rem;
}

.new-ui .ui-box.app .ui-box-header:before {
  bottom: 0;
}

.new-ui .ui-box.app .ui-box-footer {
  text-align: center;
  margin-top: 1.5rem;
  margin-bottom: -2rem;
}

.new-ui .ui-box.app .ui-box-footer:before {
  top: 0;
}

.new-ui .ui-box.app .ui-box-title {
  font-size: 1em;
  color: #5d9bfc;
  margin: 0;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
}

.new-ui .ui-box.app .ui-autocomplete {
  background-color: #FFF;
}

.new-ui .ui-box.app .button-text.block {
  background-color: rgba(255, 255, 255, 0.65);
}

.new-ui .ui-box.app .button-text.block:hover {
  background-color: #FFF;
  transition: background-color 0.15s ease-out;
}

.new-ui .ui-box.app:first-of-type {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.new-ui .ui-box.app:last-of-type {
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.new-ui .ui-box.notification {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  border-color: #eff5ff;
  padding: 1.5rem;
  background-color: #f4f8ff;
}

.new-ui .ui-box.notification p {
  color: #5d759a;
  text-align: center;
}

.new-ui .ui-box.notification .klicky {
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: auto;
  height: 6rem;
  position: absolute;
  left: 50%;
  top: -3rem;
}

.new-ui .ui-box.notification.klicky {
  padding-top: 3rem;
}

.new-ui .hrule + .ui-box.app {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.new-ui .hrule + .ui-box.app .ui-box-footer:before {
  top: -1px;
}

.new-ui .ui-card {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  height: auto;
  padding: 20px 16px;
  margin: 0 auto;
  border: 1px solid #c1cfe6;
  background-color: #FFF;
  text-align: center;
  color: #5d759a;
  position: relative;
  cursor: pointer;
  white-space: normal;
}

.new-ui .ui-card .card-preview {
  position: relative;
  z-index: 2;
  margin-top: 4px;
}

.new-ui .ui-card .card-menu {
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 6px;
  right: 6px;
  z-index: 50;
}

.new-ui .ui-card .card-menu .menu-toggle:before {
  font-family: "glyphs";
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-size: 14px;
  color: #c1cfe6;
}

.new-ui .ui-card .card-menu .menu-toggle:hover:before {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui .ui-card .card-menu .menu-container {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  display: none;
  position: absolute;
  left: 40px;
  margin-top: -10px;
}

.new-ui .ui-card .card-menu:hover .menu-container {
  display: inline-block;
}

.new-ui .ui-card .card-title {
  font-family: "Source Sans Pro", "Source Sans Pro", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-size: 0.865rem;
  font-weight: 600;
}

.new-ui .ui-card .card-title .glyph-open-window {
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
  transform: scale(1);
  display: inline-block;
  vertical-align: top;
  transition: transform 0.15s ease-in;
  cursor: pointer;
}

.new-ui .ui-card .card-title .glyph-open-window:hover {
  -moz-transform: scale(1.2);
  -o-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
  transition: transform 0.15s ease-in;
}

.new-ui .ui-card .card-description {
  display: block;
  width: 85%;
  margin: 0.125em auto 0 auto;
  font-size: 0.825em;
  text-transform: none;
}

.new-ui .ui-card .card-stats {
  color: #bbceeb;
  margin-top: 3px;
}

.new-ui .ui-card .card-number {
  display: none !important;
  width: auto;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  color: #bbceeb;
  font-size: 9px;
  line-height: 12px;
  font-weight: 600;
  position: absolute;
  bottom: 8px;
  left: 10px;
}

.new-ui .ui-card .card-connections {
  display: inline-block;
  width: auto;
  padding: 0 0.35rem 0 1rem;
  border-radius: 2px;
  background-color: #5d9bfc;
  font-size: 12px;
  font-weight: 900;
  color: #FFF;
  position: absolute;
  bottom: 0.375rem;
  right: 0.375rem;
}

.new-ui .ui-card .card-connections:before {
  font-family: "glyphs";
  content: "";
  font-size: 9px;
  position: absolute;
  top: 2px;
  left: 3px;
  color: rgba(0, 0, 0, 0.35);
}

.new-ui .ui-card .card-delete {
  display: block;
  width: 16px;
  height: 16px;
  border-radius: 16px;
  position: absolute;
  top: 6px;
  right: 6px;
  z-index: 2;
  cursor: pointer;
  opacity: 0;
}

.new-ui .ui-card .card-delete:before {
  font-family: "glyphs";
  display: block;
  content: "";
  width: 16px;
  height: 16px;
  line-height: 16px;
  color: #c1cfe6;
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 12px;
  margin-left: -8px;
  margin-top: -7px;
}

.new-ui .ui-card .card-delete:hover {
  background-color: #e26464;
  transition: all 0.15s ease-out;
}

.new-ui .ui-card .card-delete:hover:before {
  color: #FFF;
  transition: all 0.15s ease-out;
}

.new-ui .ui-card .card-action-icon {
  display: block;
  width: 1.5rem;
  height: 1rem;
  margin: 0 auto 0.35rem auto;
  color: #5d759a;
  text-align: center;
}

.new-ui .ui-card .card-action-icon:before {
  display: inline-block;
  font-size: 1.425rem;
  text-indent: 1px;
  color: #c1cfe6;
}

.new-ui .ui-card .action-goto {
  color: #50a475;
  position: relative;
  background-color: transparent;
}

.new-ui .ui-card .action-goto p {
  font-size: 1rem;
}

.new-ui .ui-card .action-goto .action-goto-icon,
.new-ui .ui-card .action-goto .action-disconnect-icon {
  display: block;
  width: 24px;
  height: 24px;
  padding: 0;
  border: 1px solid #5d759a;
  border-radius: 24px;
  background-color: #a7b8d6;
  box-shadow: 0 3px 0 0 rgba(93, 155, 252, 0.25);
  position: absolute;
  left: 50%;
  bottom: -33px;
  margin-left: -12px;
  cursor: pointer;
  z-index: 100;
}

.new-ui .ui-card .action-goto .action-goto-icon:before,
.new-ui .ui-card .action-goto .action-disconnect-icon:before {
  font-family: "glyphs";
  font-size: 16px;
  line-height: 23px;
  text-align: center;
  color: #FFF;
}

.new-ui .ui-card .action-goto .action-goto-icon:before {
  content: "";
}

.new-ui .ui-card .action-goto .action-goto-icon:hover {
  border-color: #3378e3;
  background-color: #5d9bfc;
}

.new-ui .ui-card .action-goto .action-disconnect-icon:before {
  content: "";
}

.new-ui .ui-card .action-goto .action-disconnect-icon:hover {
  border-color: #ce2727;
  background-color: #e26464;
}

.new-ui .ui-card .card-group {
  width: calc(100% - 12px);
  padding: 0.5rem 0;
  margin-left: 6px;
  position: relative;
}

.new-ui .ui-card .card-group .ui-card {
  width: 100%;
  box-shadow: none;
}

.new-ui .ui-card .card-group .ui-card + .ui-card {
  margin-top: 0.35rem;
}

.new-ui .ui-card .card-group .ui-card.split-test {
  padding: 0.5rem;
  border: none;
  border-radius: 0.1875rem;
}

.new-ui .ui-card .card-group .ui-card.split-test .card-title,
.new-ui .ui-card .card-group .ui-card.split-test .card-description,
.new-ui .ui-card .card-group .ui-card.split-test .card-action-icon,
.new-ui .ui-card .card-group .ui-card.split-test .card-stats {
  color: #FFF;
}

.new-ui .ui-card .card-group .ui-card.split-test .card-title:before,
.new-ui .ui-card .card-group .ui-card.split-test .card-description:before,
.new-ui .ui-card .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card .card-group .ui-card.split-test .card-stats:before {
  color: #FFF;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(1n+1) {
  background-color: #73bcff;
  box-shadow: inset 0 0 0 1px #40a4ff;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(1n+1).winner {
  box-shadow: inset 0 0 0 4px #40a4ff;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(2n+2) {
  background-color: #777bc6;
  box-shadow: inset 0 0 0 1px #5358b7;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(2n+2).winner {
  box-shadow: inset 0 0 0 4px #5358b7;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(3n+3) {
  background-color: #67b489;
  box-shadow: inset 0 0 0 1px #4d9b6f;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(3n+3).winner {
  box-shadow: inset 0 0 0 4px #4d9b6f;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(4n+4) {
  background-color: #ffa21a;
  box-shadow: inset 0 0 0 1px #e68900;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(4n+4).winner {
  box-shadow: inset 0 0 0 4px #e68900;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(5n+5) {
  background-color: #2cbccb;
  box-shadow: inset 0 0 0 1px #2395a1;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(5n+5).winner {
  box-shadow: inset 0 0 0 4px #2395a1;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(6n+6) {
  background-color: #9acc68;
  box-shadow: inset 0 0 0 1px #76b13c;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(6n+6).winner {
  box-shadow: inset 0 0 0 4px #76b13c;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(7n+7) {
  background-color: #cb5c5b;
  box-shadow: inset 0 0 0 1px #b93c3b;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(7n+7).winner {
  box-shadow: inset 0 0 0 4px #b93c3b;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(8n+8) {
  background-color: #63a1f5;
  box-shadow: inset 0 0 0 1px #3384f2;
}

.new-ui .ui-card .card-group .ui-card.split-test:nth-child(8n+8).winner {
  box-shadow: inset 0 0 0 4px #3384f2;
}

.new-ui .ui-card .card-group .ui-card.split-test.winner:before {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
  font-size: 2rem;
  color: #FFF !important;
  text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.15);
  position: absolute;
  top: 50%;
  left: 1rem;
  margin-top: -0.9rem;
}

.new-ui .ui-card .card-group .card-preview + .split-test-card {
  margin-top: 1rem;
}

.new-ui .ui-card .card-group .card-quick-menu {
  height: 5rem;
}

.new-ui .ui-card.add-card {
  padding: 0.75rem;
  border: 1px dashed #c1cfe6;
  border-top: 1px dashed #c1cfe6 !important;
}

.new-ui .ui-card.add-card .button-text {
  font-family: "Source Sans Pro", "Source Sans Pro", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  opacity: 0.5;
}

.new-ui .ui-card.add-card:hover {
  transition: all 0.15s ease-out;
}

.new-ui .ui-card.add-card:hover .button-text {
  opacity: 1;
  transition: all 0.15s ease-out;
}

.new-ui .ui-card.rule {
  padding: 30px;
}

.new-ui .ui-card.rule .card-preview,
.new-ui .ui-card.rule .card-form {
  padding-left: 5rem;
  text-align: left;
}

.new-ui .ui-card.rule .card-preview {
  margin: 0;
  font-size: 1.25rem;
}

.new-ui .ui-card.rule .card-preview .card-title {
  font-size: 0.925em;
  color: black;
}

.new-ui .ui-card.rule .card-preview .card-description {
  width: 100%;
}

.new-ui .ui-card.rule .card-preview .card-action-icon {
  width: 2rem;
  height: 4rem;
  line-height: 4rem;
  position: absolute;
  top: 50%;
  left: -0.325rem;
  margin-top: -2rem;
}

.new-ui .ui-card.rule .card-preview .card-action-icon:before {
  color: #5d9bfc;
  font-size: 3rem;
}

.new-ui .ui-card.rule .card-form {
  padding-top: 2.5rem;
  padding-bottom: 1.5rem;
}

.new-ui .ui-card.rule.add-card {
  border-left-style: dashed;
  border-right-style: dashed;
  border-bottom-style: dashed !important;
}

.new-ui .ui-card.rule:hover .card-preview .card-title {
  color: #5d9bfc;
}

.new-ui .ui-card.edit {
  background-color: #eff5ff;
}

.new-ui .ui-card.edit h3 {
  margin-top: 1.25rem;
  margin-bottom: 2rem;
}

.new-ui .ui-card.edit .grid-apps {
  padding: 1rem;
}

.new-ui .ui-card.edit .grid-apps .app-entity .app-icon {
  background-color: #FFF;
}

.new-ui .ui-card:hover .card-title,
.new-ui .ui-card:hover .card-description {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui .ui-card:hover .card-action-icon:before {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui .ui-card:hover .card-delete {
  opacity: 1;
  transition: opacity 0.1s ease-out;
}

.new-ui .ui-card:hover .card-tools .card-add {
  left: 50%;
  opacity: 1;
}

.new-ui .ui-card:hover .card-tools .card-add.condition-yes {
  left: 4rem;
}

.new-ui .ui-card:hover .card-tools .card-add.condition-no {
  left: auto;
  right: 4rem;
}

.new-ui .ui-card.card-bg-teal-light .card-preview,
.new-ui .ui-card.card-bg-teal-light .card-number,
.new-ui .ui-card.card-bg-teal-light .card-group,
.new-ui .ui-card.card-bg-teal-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-teal-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-teal-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #6ECED9;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-teal-light .card-title,
.new-ui .ui-card.card-bg-teal-light .card-number,
.new-ui .ui-card.card-bg-teal-light .card-stats,
.new-ui .ui-card.card-bg-teal-light .card-group, .new-ui .ui-card.card-bg-teal-light:hover .card-title,
.new-ui .ui-card.card-bg-teal-light:hover .card-number,
.new-ui .ui-card.card-bg-teal-light:hover .card-stats,
.new-ui .ui-card.card-bg-teal-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-light .card-action-icon,
.new-ui .ui-card.card-bg-teal-light .card-delete, .new-ui .ui-card.card-bg-teal-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-teal-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-teal-light .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-light .card-delete:before, .new-ui .ui-card.card-bg-teal-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-teal-light .card-delete:hover, .new-ui .ui-card.card-bg-teal-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-teal-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-teal-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-teal-light .card-delete:hover:before, .new-ui .ui-card.card-bg-teal-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-teal-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-light .card-connections, .new-ui .ui-card.card-bg-teal-light:hover .card-connections {
  background-color: #268792;
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-light .card-connections:before, .new-ui .ui-card.card-bg-teal-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-light .card-delete, .new-ui .ui-card.card-bg-teal-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-teal-light .card-number, .new-ui .ui-card.card-bg-teal-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-teal-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-teal-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #6ECED9;
  box-shadow: 2px 2px 0 0 #46c0ce;
}

.new-ui .ui-card.card-bg-teal-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-teal-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-teal-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-teal-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-teal-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-teal-light:hover .card-group .ui-card.split-test .card-stats {
  color: #6ECED9;
}

.new-ui .ui-card.card-bg-teal-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-teal-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #6ECED9;
}

.new-ui .ui-card.card-bg-teal-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #4ec3d1;
}

.new-ui .ui-card.card-bg-teal-dark .card-preview,
.new-ui .ui-card.card-bg-teal-dark .card-number,
.new-ui .ui-card.card-bg-teal-dark .card-group,
.new-ui .ui-card.card-bg-teal-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-teal-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-teal-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #0097AA;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-teal-dark .card-title,
.new-ui .ui-card.card-bg-teal-dark .card-number,
.new-ui .ui-card.card-bg-teal-dark .card-stats,
.new-ui .ui-card.card-bg-teal-dark .card-group, .new-ui .ui-card.card-bg-teal-dark:hover .card-title,
.new-ui .ui-card.card-bg-teal-dark:hover .card-number,
.new-ui .ui-card.card-bg-teal-dark:hover .card-stats,
.new-ui .ui-card.card-bg-teal-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-dark .card-action-icon,
.new-ui .ui-card.card-bg-teal-dark .card-delete, .new-ui .ui-card.card-bg-teal-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-teal-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-teal-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-dark .card-delete:before, .new-ui .ui-card.card-bg-teal-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-teal-dark .card-delete:hover, .new-ui .ui-card.card-bg-teal-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-teal-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-teal-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-teal-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-teal-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-teal-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-dark .card-connections, .new-ui .ui-card.card-bg-teal-dark:hover .card-connections {
  background-color: #00181b;
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-dark .card-connections:before, .new-ui .ui-card.card-bg-teal-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-teal-dark .card-delete, .new-ui .ui-card.card-bg-teal-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-teal-dark .card-number, .new-ui .ui-card.card-bg-teal-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-teal-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-teal-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #0097AA;
  box-shadow: 2px 2px 0 0 #006a77;
}

.new-ui .ui-card.card-bg-teal-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-teal-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-teal-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-teal-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-teal-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-teal-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #0097AA;
}

.new-ui .ui-card.card-bg-teal-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-teal-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-teal-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #0097AA;
}

.new-ui .ui-card.card-bg-teal-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #007381;
}

.new-ui .ui-card.card-bg-peach-light .card-preview,
.new-ui .ui-card.card-bg-peach-light .card-number,
.new-ui .ui-card.card-bg-peach-light .card-group,
.new-ui .ui-card.card-bg-peach-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-peach-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-peach-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #E6AB9D;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-peach-light .card-title,
.new-ui .ui-card.card-bg-peach-light .card-number,
.new-ui .ui-card.card-bg-peach-light .card-stats,
.new-ui .ui-card.card-bg-peach-light .card-group, .new-ui .ui-card.card-bg-peach-light:hover .card-title,
.new-ui .ui-card.card-bg-peach-light:hover .card-number,
.new-ui .ui-card.card-bg-peach-light:hover .card-stats,
.new-ui .ui-card.card-bg-peach-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-light .card-action-icon,
.new-ui .ui-card.card-bg-peach-light .card-delete, .new-ui .ui-card.card-bg-peach-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-peach-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-peach-light .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-light .card-delete:before, .new-ui .ui-card.card-bg-peach-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-peach-light .card-delete:hover, .new-ui .ui-card.card-bg-peach-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-peach-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-peach-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-peach-light .card-delete:hover:before, .new-ui .ui-card.card-bg-peach-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-peach-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-light .card-connections, .new-ui .ui-card.card-bg-peach-light:hover .card-connections {
  background-color: #c34d32;
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-light .card-connections:before, .new-ui .ui-card.card-bg-peach-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-light .card-delete, .new-ui .ui-card.card-bg-peach-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-peach-light .card-number, .new-ui .ui-card.card-bg-peach-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-peach-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-peach-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #E6AB9D;
  box-shadow: 2px 2px 0 0 #dc8874;
}

.new-ui .ui-card.card-bg-peach-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-peach-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-peach-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-peach-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-peach-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-peach-light:hover .card-group .ui-card.split-test .card-stats {
  color: #E6AB9D;
}

.new-ui .ui-card.card-bg-peach-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-peach-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #E6AB9D;
}

.new-ui .ui-card.card-bg-peach-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #de8f7c;
}

.new-ui .ui-card.card-bg-peach-dark .card-preview,
.new-ui .ui-card.card-bg-peach-dark .card-number,
.new-ui .ui-card.card-bg-peach-dark .card-group,
.new-ui .ui-card.card-bg-peach-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-peach-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-peach-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #BC5F5F;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-peach-dark .card-title,
.new-ui .ui-card.card-bg-peach-dark .card-number,
.new-ui .ui-card.card-bg-peach-dark .card-stats,
.new-ui .ui-card.card-bg-peach-dark .card-group, .new-ui .ui-card.card-bg-peach-dark:hover .card-title,
.new-ui .ui-card.card-bg-peach-dark:hover .card-number,
.new-ui .ui-card.card-bg-peach-dark:hover .card-stats,
.new-ui .ui-card.card-bg-peach-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-dark .card-action-icon,
.new-ui .ui-card.card-bg-peach-dark .card-delete, .new-ui .ui-card.card-bg-peach-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-peach-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-peach-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-dark .card-delete:before, .new-ui .ui-card.card-bg-peach-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-peach-dark .card-delete:hover, .new-ui .ui-card.card-bg-peach-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-peach-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-peach-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-peach-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-peach-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-peach-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-dark .card-connections, .new-ui .ui-card.card-bg-peach-dark:hover .card-connections {
  background-color: #632929;
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-dark .card-connections:before, .new-ui .ui-card.card-bg-peach-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-peach-dark .card-delete, .new-ui .ui-card.card-bg-peach-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-peach-dark .card-number, .new-ui .ui-card.card-bg-peach-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-peach-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-peach-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #BC5F5F;
  box-shadow: 2px 2px 0 0 #a44444;
}

.new-ui .ui-card.card-bg-peach-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-peach-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-peach-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-peach-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-peach-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-peach-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #BC5F5F;
}

.new-ui .ui-card.card-bg-peach-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-peach-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-peach-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #BC5F5F;
}

.new-ui .ui-card.card-bg-peach-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #ab4747;
}

.new-ui .ui-card.card-bg-pink-light .card-preview,
.new-ui .ui-card.card-bg-pink-light .card-number,
.new-ui .ui-card.card-bg-pink-light .card-group,
.new-ui .ui-card.card-bg-pink-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-pink-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-pink-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #E286A6;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-pink-light .card-title,
.new-ui .ui-card.card-bg-pink-light .card-number,
.new-ui .ui-card.card-bg-pink-light .card-stats,
.new-ui .ui-card.card-bg-pink-light .card-group, .new-ui .ui-card.card-bg-pink-light:hover .card-title,
.new-ui .ui-card.card-bg-pink-light:hover .card-number,
.new-ui .ui-card.card-bg-pink-light:hover .card-stats,
.new-ui .ui-card.card-bg-pink-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-light .card-action-icon,
.new-ui .ui-card.card-bg-pink-light .card-delete, .new-ui .ui-card.card-bg-pink-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-pink-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-pink-light .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-light .card-delete:before, .new-ui .ui-card.card-bg-pink-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-pink-light .card-delete:hover, .new-ui .ui-card.card-bg-pink-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-pink-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-pink-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-pink-light .card-delete:hover:before, .new-ui .ui-card.card-bg-pink-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-pink-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-light .card-connections, .new-ui .ui-card.card-bg-pink-light:hover .card-connections {
  background-color: #af2a58;
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-light .card-connections:before, .new-ui .ui-card.card-bg-pink-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-light .card-delete, .new-ui .ui-card.card-bg-pink-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-pink-light .card-number, .new-ui .ui-card.card-bg-pink-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-pink-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-pink-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #E286A6;
  box-shadow: 2px 2px 0 0 #d85d88;
}

.new-ui .ui-card.card-bg-pink-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-pink-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-pink-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-pink-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-pink-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-pink-light:hover .card-group .ui-card.split-test .card-stats {
  color: #E286A6;
}

.new-ui .ui-card.card-bg-pink-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-pink-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #E286A6;
}

.new-ui .ui-card.card-bg-pink-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #da658e;
}

.new-ui .ui-card.card-bg-pink-dark .card-preview,
.new-ui .ui-card.card-bg-pink-dark .card-number,
.new-ui .ui-card.card-bg-pink-dark .card-group,
.new-ui .ui-card.card-bg-pink-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-pink-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-pink-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #C64873;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-pink-dark .card-title,
.new-ui .ui-card.card-bg-pink-dark .card-number,
.new-ui .ui-card.card-bg-pink-dark .card-stats,
.new-ui .ui-card.card-bg-pink-dark .card-group, .new-ui .ui-card.card-bg-pink-dark:hover .card-title,
.new-ui .ui-card.card-bg-pink-dark:hover .card-number,
.new-ui .ui-card.card-bg-pink-dark:hover .card-stats,
.new-ui .ui-card.card-bg-pink-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-dark .card-action-icon,
.new-ui .ui-card.card-bg-pink-dark .card-delete, .new-ui .ui-card.card-bg-pink-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-pink-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-pink-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-dark .card-delete:before, .new-ui .ui-card.card-bg-pink-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-pink-dark .card-delete:hover, .new-ui .ui-card.card-bg-pink-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-pink-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-pink-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-pink-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-pink-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-pink-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-dark .card-connections, .new-ui .ui-card.card-bg-pink-dark:hover .card-connections {
  background-color: #611e35;
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-dark .card-connections:before, .new-ui .ui-card.card-bg-pink-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-pink-dark .card-delete, .new-ui .ui-card.card-bg-pink-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-pink-dark .card-number, .new-ui .ui-card.card-bg-pink-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-pink-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-pink-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #C64873;
  box-shadow: 2px 2px 0 0 #a7345b;
}

.new-ui .ui-card.card-bg-pink-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-pink-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-pink-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-pink-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-pink-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-pink-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #C64873;
}

.new-ui .ui-card.card-bg-pink-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-pink-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-pink-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #C64873;
}

.new-ui .ui-card.card-bg-pink-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #af3660;
}

.new-ui .ui-card.card-bg-violet-light .card-preview,
.new-ui .ui-card.card-bg-violet-light .card-number,
.new-ui .ui-card.card-bg-violet-light .card-group,
.new-ui .ui-card.card-bg-violet-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-violet-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-violet-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #8D98D8;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-violet-light .card-title,
.new-ui .ui-card.card-bg-violet-light .card-number,
.new-ui .ui-card.card-bg-violet-light .card-stats,
.new-ui .ui-card.card-bg-violet-light .card-group, .new-ui .ui-card.card-bg-violet-light:hover .card-title,
.new-ui .ui-card.card-bg-violet-light:hover .card-number,
.new-ui .ui-card.card-bg-violet-light:hover .card-stats,
.new-ui .ui-card.card-bg-violet-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-light .card-action-icon,
.new-ui .ui-card.card-bg-violet-light .card-delete, .new-ui .ui-card.card-bg-violet-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-violet-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-violet-light .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-light .card-delete:before, .new-ui .ui-card.card-bg-violet-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-violet-light .card-delete:hover, .new-ui .ui-card.card-bg-violet-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-violet-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-violet-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-violet-light .card-delete:hover:before, .new-ui .ui-card.card-bg-violet-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-violet-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-light .card-connections, .new-ui .ui-card.card-bg-violet-light:hover .card-connections {
  background-color: #3746a0;
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-light .card-connections:before, .new-ui .ui-card.card-bg-violet-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-light .card-delete, .new-ui .ui-card.card-bg-violet-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-violet-light .card-number, .new-ui .ui-card.card-bg-violet-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-violet-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-violet-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #8D98D8;
  box-shadow: 2px 2px 0 0 #6776cb;
}

.new-ui .ui-card.card-bg-violet-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-violet-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-violet-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-violet-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-violet-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-violet-light:hover .card-group .ui-card.split-test .card-stats {
  color: #8D98D8;
}

.new-ui .ui-card.card-bg-violet-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-violet-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #8D98D8;
}

.new-ui .ui-card.card-bg-violet-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #6f7dce;
}

.new-ui .ui-card.card-bg-violet-dark .card-preview,
.new-ui .ui-card.card-bg-violet-dark .card-number,
.new-ui .ui-card.card-bg-violet-dark .card-group,
.new-ui .ui-card.card-bg-violet-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-violet-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-violet-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #505EAF;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-violet-dark .card-title,
.new-ui .ui-card.card-bg-violet-dark .card-number,
.new-ui .ui-card.card-bg-violet-dark .card-stats,
.new-ui .ui-card.card-bg-violet-dark .card-group, .new-ui .ui-card.card-bg-violet-dark:hover .card-title,
.new-ui .ui-card.card-bg-violet-dark:hover .card-number,
.new-ui .ui-card.card-bg-violet-dark:hover .card-stats,
.new-ui .ui-card.card-bg-violet-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-dark .card-action-icon,
.new-ui .ui-card.card-bg-violet-dark .card-delete, .new-ui .ui-card.card-bg-violet-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-violet-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-violet-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-dark .card-delete:before, .new-ui .ui-card.card-bg-violet-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-violet-dark .card-delete:hover, .new-ui .ui-card.card-bg-violet-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-violet-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-violet-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-violet-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-violet-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-violet-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-dark .card-connections, .new-ui .ui-card.card-bg-violet-dark:hover .card-connections {
  background-color: #23294d;
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-dark .card-connections:before, .new-ui .ui-card.card-bg-violet-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-violet-dark .card-delete, .new-ui .ui-card.card-bg-violet-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-violet-dark .card-number, .new-ui .ui-card.card-bg-violet-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-violet-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-violet-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #505EAF;
  box-shadow: 2px 2px 0 0 #404b8c;
}

.new-ui .ui-card.card-bg-violet-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-violet-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-violet-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-violet-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-violet-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-violet-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #505EAF;
}

.new-ui .ui-card.card-bg-violet-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-violet-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-violet-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #505EAF;
}

.new-ui .ui-card.card-bg-violet-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #434f93;
}

.new-ui .ui-card.card-bg-blue-light .card-preview,
.new-ui .ui-card.card-bg-blue-light .card-number,
.new-ui .ui-card.card-bg-blue-light .card-group,
.new-ui .ui-card.card-bg-blue-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-blue-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-blue-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #86B2DE;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-blue-light .card-title,
.new-ui .ui-card.card-bg-blue-light .card-number,
.new-ui .ui-card.card-bg-blue-light .card-stats,
.new-ui .ui-card.card-bg-blue-light .card-group, .new-ui .ui-card.card-bg-blue-light:hover .card-title,
.new-ui .ui-card.card-bg-blue-light:hover .card-number,
.new-ui .ui-card.card-bg-blue-light:hover .card-stats,
.new-ui .ui-card.card-bg-blue-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-light .card-action-icon,
.new-ui .ui-card.card-bg-blue-light .card-delete, .new-ui .ui-card.card-bg-blue-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-blue-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-blue-light .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-light .card-delete:before, .new-ui .ui-card.card-bg-blue-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-blue-light .card-delete:hover, .new-ui .ui-card.card-bg-blue-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-blue-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-blue-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-blue-light .card-delete:hover:before, .new-ui .ui-card.card-bg-blue-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-blue-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-light .card-connections, .new-ui .ui-card.card-bg-blue-light:hover .card-connections {
  background-color: #2e6ba8;
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-light .card-connections:before, .new-ui .ui-card.card-bg-blue-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-light .card-delete, .new-ui .ui-card.card-bg-blue-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-blue-light .card-number, .new-ui .ui-card.card-bg-blue-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-blue-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-blue-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #86B2DE;
  box-shadow: 2px 2px 0 0 #5e99d3;
}

.new-ui .ui-card.card-bg-blue-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-blue-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-blue-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-blue-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-blue-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-blue-light:hover .card-group .ui-card.split-test .card-stats {
  color: #86B2DE;
}

.new-ui .ui-card.card-bg-blue-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-blue-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #86B2DE;
}

.new-ui .ui-card.card-bg-blue-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #669ed5;
}

.new-ui .ui-card.card-bg-blue-dark .card-preview,
.new-ui .ui-card.card-bg-blue-dark .card-number,
.new-ui .ui-card.card-bg-blue-dark .card-group,
.new-ui .ui-card.card-bg-blue-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-blue-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-blue-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #4182D9;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-blue-dark .card-title,
.new-ui .ui-card.card-bg-blue-dark .card-number,
.new-ui .ui-card.card-bg-blue-dark .card-stats,
.new-ui .ui-card.card-bg-blue-dark .card-group, .new-ui .ui-card.card-bg-blue-dark:hover .card-title,
.new-ui .ui-card.card-bg-blue-dark:hover .card-number,
.new-ui .ui-card.card-bg-blue-dark:hover .card-stats,
.new-ui .ui-card.card-bg-blue-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-dark .card-action-icon,
.new-ui .ui-card.card-bg-blue-dark .card-delete, .new-ui .ui-card.card-bg-blue-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-blue-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-blue-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-dark .card-delete:before, .new-ui .ui-card.card-bg-blue-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-blue-dark .card-delete:hover, .new-ui .ui-card.card-bg-blue-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-blue-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-blue-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-blue-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-blue-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-blue-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-dark .card-connections, .new-ui .ui-card.card-bg-blue-dark:hover .card-connections {
  background-color: #173f74;
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-dark .card-connections:before, .new-ui .ui-card.card-bg-blue-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-blue-dark .card-delete, .new-ui .ui-card.card-bg-blue-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-blue-dark .card-number, .new-ui .ui-card.card-bg-blue-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-blue-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-blue-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #4182D9;
  box-shadow: 2px 2px 0 0 #2768c1;
}

.new-ui .ui-card.card-bg-blue-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-blue-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-blue-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-blue-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-blue-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-blue-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #4182D9;
}

.new-ui .ui-card.card-bg-blue-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-blue-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-blue-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #4182D9;
}

.new-ui .ui-card.card-bg-blue-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #286dc9;
}

.new-ui .ui-card.card-bg-silver-light .card-preview,
.new-ui .ui-card.card-bg-silver-light .card-number,
.new-ui .ui-card.card-bg-silver-light .card-group,
.new-ui .ui-card.card-bg-silver-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-silver-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-silver-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #A4B3BA;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-silver-light .card-title,
.new-ui .ui-card.card-bg-silver-light .card-number,
.new-ui .ui-card.card-bg-silver-light .card-stats,
.new-ui .ui-card.card-bg-silver-light .card-group, .new-ui .ui-card.card-bg-silver-light:hover .card-title,
.new-ui .ui-card.card-bg-silver-light:hover .card-number,
.new-ui .ui-card.card-bg-silver-light:hover .card-stats,
.new-ui .ui-card.card-bg-silver-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-light .card-action-icon,
.new-ui .ui-card.card-bg-silver-light .card-delete, .new-ui .ui-card.card-bg-silver-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-silver-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-silver-light .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-light .card-delete:before, .new-ui .ui-card.card-bg-silver-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-silver-light .card-delete:hover, .new-ui .ui-card.card-bg-silver-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-silver-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-silver-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-silver-light .card-delete:hover:before, .new-ui .ui-card.card-bg-silver-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-silver-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-light .card-connections, .new-ui .ui-card.card-bg-silver-light:hover .card-connections {
  background-color: #596d76;
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-light .card-connections:before, .new-ui .ui-card.card-bg-silver-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-light .card-delete, .new-ui .ui-card.card-bg-silver-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-silver-light .card-number, .new-ui .ui-card.card-bg-silver-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-silver-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-silver-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #A4B3BA;
  box-shadow: 2px 2px 0 0 #879ba4;
}

.new-ui .ui-card.card-bg-silver-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-silver-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-silver-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-silver-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-silver-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-silver-light:hover .card-group .ui-card.split-test .card-stats {
  color: #A4B3BA;
}

.new-ui .ui-card.card-bg-silver-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-silver-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #A4B3BA;
}

.new-ui .ui-card.card-bg-silver-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #8da0a8;
}

.new-ui .ui-card.card-bg-silver-dark .card-preview,
.new-ui .ui-card.card-bg-silver-dark .card-number,
.new-ui .ui-card.card-bg-silver-dark .card-group,
.new-ui .ui-card.card-bg-silver-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-silver-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-silver-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #6C818D;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-silver-dark .card-title,
.new-ui .ui-card.card-bg-silver-dark .card-number,
.new-ui .ui-card.card-bg-silver-dark .card-stats,
.new-ui .ui-card.card-bg-silver-dark .card-group, .new-ui .ui-card.card-bg-silver-dark:hover .card-title,
.new-ui .ui-card.card-bg-silver-dark:hover .card-number,
.new-ui .ui-card.card-bg-silver-dark:hover .card-stats,
.new-ui .ui-card.card-bg-silver-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-dark .card-action-icon,
.new-ui .ui-card.card-bg-silver-dark .card-delete, .new-ui .ui-card.card-bg-silver-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-silver-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-silver-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-dark .card-delete:before, .new-ui .ui-card.card-bg-silver-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-silver-dark .card-delete:hover, .new-ui .ui-card.card-bg-silver-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-silver-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-silver-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-silver-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-silver-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-silver-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-dark .card-connections, .new-ui .ui-card.card-bg-silver-dark:hover .card-connections {
  background-color: #2e373c;
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-dark .card-connections:before, .new-ui .ui-card.card-bg-silver-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-silver-dark .card-delete, .new-ui .ui-card.card-bg-silver-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-silver-dark .card-number, .new-ui .ui-card.card-bg-silver-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-silver-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-silver-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #6C818D;
  box-shadow: 2px 2px 0 0 #566770;
}

.new-ui .ui-card.card-bg-silver-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-silver-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-silver-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-silver-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-silver-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-silver-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #6C818D;
}

.new-ui .ui-card.card-bg-silver-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-silver-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-silver-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #6C818D;
}

.new-ui .ui-card.card-bg-silver-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #5a6c76;
}

.new-ui .ui-card.card-bg-brown-light .card-preview,
.new-ui .ui-card.card-bg-brown-light .card-number,
.new-ui .ui-card.card-bg-brown-light .card-group,
.new-ui .ui-card.card-bg-brown-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-brown-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-brown-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #B7A29B;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-brown-light .card-title,
.new-ui .ui-card.card-bg-brown-light .card-number,
.new-ui .ui-card.card-bg-brown-light .card-stats,
.new-ui .ui-card.card-bg-brown-light .card-group, .new-ui .ui-card.card-bg-brown-light:hover .card-title,
.new-ui .ui-card.card-bg-brown-light:hover .card-number,
.new-ui .ui-card.card-bg-brown-light:hover .card-stats,
.new-ui .ui-card.card-bg-brown-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-light .card-action-icon,
.new-ui .ui-card.card-bg-brown-light .card-delete, .new-ui .ui-card.card-bg-brown-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-brown-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-brown-light .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-light .card-delete:before, .new-ui .ui-card.card-bg-brown-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-brown-light .card-delete:hover, .new-ui .ui-card.card-bg-brown-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-brown-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-brown-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-brown-light .card-delete:hover:before, .new-ui .ui-card.card-bg-brown-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-brown-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-light .card-connections, .new-ui .ui-card.card-bg-brown-light:hover .card-connections {
  background-color: #715a52;
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-light .card-connections:before, .new-ui .ui-card.card-bg-brown-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-light .card-delete, .new-ui .ui-card.card-bg-brown-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-brown-light .card-number, .new-ui .ui-card.card-bg-brown-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-brown-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-brown-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #B7A29B;
  box-shadow: 2px 2px 0 0 #a2867d;
}

.new-ui .ui-card.card-bg-brown-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-brown-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-brown-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-brown-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-brown-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-brown-light:hover .card-group .ui-card.split-test .card-stats {
  color: #B7A29B;
}

.new-ui .ui-card.card-bg-brown-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-brown-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #B7A29B;
}

.new-ui .ui-card.card-bg-brown-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #a68c83;
}

.new-ui .ui-card.card-bg-brown-dark .card-preview,
.new-ui .ui-card.card-bg-brown-dark .card-number,
.new-ui .ui-card.card-bg-brown-dark .card-group,
.new-ui .ui-card.card-bg-brown-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-brown-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-brown-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #9B6F60;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-brown-dark .card-title,
.new-ui .ui-card.card-bg-brown-dark .card-number,
.new-ui .ui-card.card-bg-brown-dark .card-stats,
.new-ui .ui-card.card-bg-brown-dark .card-group, .new-ui .ui-card.card-bg-brown-dark:hover .card-title,
.new-ui .ui-card.card-bg-brown-dark:hover .card-number,
.new-ui .ui-card.card-bg-brown-dark:hover .card-stats,
.new-ui .ui-card.card-bg-brown-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-dark .card-action-icon,
.new-ui .ui-card.card-bg-brown-dark .card-delete, .new-ui .ui-card.card-bg-brown-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-brown-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-brown-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-dark .card-delete:before, .new-ui .ui-card.card-bg-brown-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-brown-dark .card-delete:hover, .new-ui .ui-card.card-bg-brown-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-brown-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-brown-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-brown-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-brown-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-brown-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-dark .card-connections, .new-ui .ui-card.card-bg-brown-dark:hover .card-connections {
  background-color: #433029;
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-dark .card-connections:before, .new-ui .ui-card.card-bg-brown-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-brown-dark .card-delete, .new-ui .ui-card.card-bg-brown-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-brown-dark .card-number, .new-ui .ui-card.card-bg-brown-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-brown-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-brown-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #9B6F60;
  box-shadow: 2px 2px 0 0 #7c584c;
}

.new-ui .ui-card.card-bg-brown-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-brown-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-brown-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-brown-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-brown-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-brown-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #9B6F60;
}

.new-ui .ui-card.card-bg-brown-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-brown-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-brown-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #9B6F60;
}

.new-ui .ui-card.card-bg-brown-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #825d50;
}

.new-ui .ui-card.card-bg-yellow-light .card-preview,
.new-ui .ui-card.card-bg-yellow-light .card-number,
.new-ui .ui-card.card-bg-yellow-light .card-group,
.new-ui .ui-card.card-bg-yellow-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-yellow-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-yellow-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #E5B05C;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-yellow-light .card-title,
.new-ui .ui-card.card-bg-yellow-light .card-number,
.new-ui .ui-card.card-bg-yellow-light .card-stats,
.new-ui .ui-card.card-bg-yellow-light .card-group, .new-ui .ui-card.card-bg-yellow-light:hover .card-title,
.new-ui .ui-card.card-bg-yellow-light:hover .card-number,
.new-ui .ui-card.card-bg-yellow-light:hover .card-stats,
.new-ui .ui-card.card-bg-yellow-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-light .card-action-icon,
.new-ui .ui-card.card-bg-yellow-light .card-delete, .new-ui .ui-card.card-bg-yellow-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-yellow-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-yellow-light .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-light .card-delete:before, .new-ui .ui-card.card-bg-yellow-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-yellow-light .card-delete:hover, .new-ui .ui-card.card-bg-yellow-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-yellow-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-yellow-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-yellow-light .card-delete:hover:before, .new-ui .ui-card.card-bg-yellow-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-yellow-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-light .card-connections, .new-ui .ui-card.card-bg-yellow-light:hover .card-connections {
  background-color: #9a6819;
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-light .card-connections:before, .new-ui .ui-card.card-bg-yellow-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-light .card-delete, .new-ui .ui-card.card-bg-yellow-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-yellow-light .card-number, .new-ui .ui-card.card-bg-yellow-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-yellow-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-yellow-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #E5B05C;
  box-shadow: 2px 2px 0 0 #de9b30;
}

.new-ui .ui-card.card-bg-yellow-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-yellow-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-yellow-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-yellow-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-yellow-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-yellow-light:hover .card-group .ui-card.split-test .card-stats {
  color: #E5B05C;
}

.new-ui .ui-card.card-bg-yellow-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-yellow-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #E5B05C;
}

.new-ui .ui-card.card-bg-yellow-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #df9f39;
}

.new-ui .ui-card.card-bg-yellow-dark .card-preview,
.new-ui .ui-card.card-bg-yellow-dark .card-number,
.new-ui .ui-card.card-bg-yellow-dark .card-group,
.new-ui .ui-card.card-bg-yellow-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-yellow-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-yellow-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #DE8225;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-yellow-dark .card-title,
.new-ui .ui-card.card-bg-yellow-dark .card-number,
.new-ui .ui-card.card-bg-yellow-dark .card-stats,
.new-ui .ui-card.card-bg-yellow-dark .card-group, .new-ui .ui-card.card-bg-yellow-dark:hover .card-title,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-number,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-stats,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-dark .card-action-icon,
.new-ui .ui-card.card-bg-yellow-dark .card-delete, .new-ui .ui-card.card-bg-yellow-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-yellow-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-dark .card-delete:before, .new-ui .ui-card.card-bg-yellow-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-yellow-dark .card-delete:hover, .new-ui .ui-card.card-bg-yellow-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-yellow-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-yellow-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-yellow-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-dark .card-connections, .new-ui .ui-card.card-bg-yellow-dark:hover .card-connections {
  background-color: #653a0f;
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-dark .card-connections:before, .new-ui .ui-card.card-bg-yellow-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-yellow-dark .card-delete, .new-ui .ui-card.card-bg-yellow-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-yellow-dark .card-number, .new-ui .ui-card.card-bg-yellow-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-yellow-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-yellow-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #DE8225;
  box-shadow: 2px 2px 0 0 #b5681b;
}

.new-ui .ui-card.card-bg-yellow-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-yellow-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-yellow-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-yellow-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #DE8225;
}

.new-ui .ui-card.card-bg-yellow-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-yellow-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-yellow-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #DE8225;
}

.new-ui .ui-card.card-bg-yellow-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #be6e1d;
}

.new-ui .ui-card.card-bg-green-light .card-preview,
.new-ui .ui-card.card-bg-green-light .card-number,
.new-ui .ui-card.card-bg-green-light .card-group,
.new-ui .ui-card.card-bg-green-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-green-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-green-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #7EAD7A;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-green-light .card-title,
.new-ui .ui-card.card-bg-green-light .card-number,
.new-ui .ui-card.card-bg-green-light .card-stats,
.new-ui .ui-card.card-bg-green-light .card-group, .new-ui .ui-card.card-bg-green-light:hover .card-title,
.new-ui .ui-card.card-bg-green-light:hover .card-number,
.new-ui .ui-card.card-bg-green-light:hover .card-stats,
.new-ui .ui-card.card-bg-green-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-light .card-action-icon,
.new-ui .ui-card.card-bg-green-light .card-delete, .new-ui .ui-card.card-bg-green-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-green-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-green-light .card-action-icon:before,
.new-ui .ui-card.card-bg-green-light .card-delete:before, .new-ui .ui-card.card-bg-green-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-green-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-green-light .card-delete:hover, .new-ui .ui-card.card-bg-green-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-green-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-green-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-green-light .card-delete:hover:before, .new-ui .ui-card.card-bg-green-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-green-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-light .card-connections, .new-ui .ui-card.card-bg-green-light:hover .card-connections {
  background-color: #3d5e3a;
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-light .card-connections:before, .new-ui .ui-card.card-bg-green-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-light .card-delete, .new-ui .ui-card.card-bg-green-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-green-light .card-number, .new-ui .ui-card.card-bg-green-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-green-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-green-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #7EAD7A;
  box-shadow: 2px 2px 0 0 #62975d;
}

.new-ui .ui-card.card-bg-green-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-green-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-green-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-green-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-green-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-green-light:hover .card-group .ui-card.split-test .card-stats {
  color: #7EAD7A;
}

.new-ui .ui-card.card-bg-green-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-green-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-green-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-green-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #7EAD7A;
}

.new-ui .ui-card.card-bg-green-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #669d61;
}

.new-ui .ui-card.card-bg-green-dark .card-preview,
.new-ui .ui-card.card-bg-green-dark .card-number,
.new-ui .ui-card.card-bg-green-dark .card-group,
.new-ui .ui-card.card-bg-green-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-green-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-green-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #367C44;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-green-dark .card-title,
.new-ui .ui-card.card-bg-green-dark .card-number,
.new-ui .ui-card.card-bg-green-dark .card-stats,
.new-ui .ui-card.card-bg-green-dark .card-group, .new-ui .ui-card.card-bg-green-dark:hover .card-title,
.new-ui .ui-card.card-bg-green-dark:hover .card-number,
.new-ui .ui-card.card-bg-green-dark:hover .card-stats,
.new-ui .ui-card.card-bg-green-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-dark .card-action-icon,
.new-ui .ui-card.card-bg-green-dark .card-delete, .new-ui .ui-card.card-bg-green-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-green-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-green-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-green-dark .card-delete:before, .new-ui .ui-card.card-bg-green-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-green-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-green-dark .card-delete:hover, .new-ui .ui-card.card-bg-green-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-green-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-green-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-green-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-green-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-green-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-dark .card-connections, .new-ui .ui-card.card-bg-green-dark:hover .card-connections {
  background-color: #0b190d;
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-dark .card-connections:before, .new-ui .ui-card.card-bg-green-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-green-dark .card-delete, .new-ui .ui-card.card-bg-green-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-green-dark .card-number, .new-ui .ui-card.card-bg-green-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-green-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-green-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #367C44;
  box-shadow: 2px 2px 0 0 #275831;
}

.new-ui .ui-card.card-bg-green-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-green-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-green-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-green-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-green-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-green-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #367C44;
}

.new-ui .ui-card.card-bg-green-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-green-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-green-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-green-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #367C44;
}

.new-ui .ui-card.card-bg-green-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #2a6034;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-preview,
.new-ui .ui-card.card-bg-coldgreen-light .card-number,
.new-ui .ui-card.card-bg-coldgreen-light .card-group,
.new-ui .ui-card.card-bg-coldgreen-light .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-light:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #62A8A0;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-title,
.new-ui .ui-card.card-bg-coldgreen-light .card-number,
.new-ui .ui-card.card-bg-coldgreen-light .card-stats,
.new-ui .ui-card.card-bg-coldgreen-light .card-group, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-title,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-number,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-stats,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-action-icon,
.new-ui .ui-card.card-bg-coldgreen-light .card-delete, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-action-icon,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-light .card-delete:before, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-action-icon:hover,
.new-ui .ui-card.card-bg-coldgreen-light .card-delete:hover, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-coldgreen-light .card-delete:hover:before, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-connections, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-connections {
  background-color: #2c4f4b;
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-connections:before, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-delete, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-number, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #62A8A0;
  box-shadow: 2px 2px 0 0 #4d8a83;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-coldgreen-light .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-coldgreen-light .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-group .ui-card.split-test .card-stats {
  color: #62A8A0;
}

.new-ui .ui-card.card-bg-coldgreen-light .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-light .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-coldgreen-light:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-light:hover .card-group .ui-card.split-test .card-delete:before {
  color: #62A8A0;
}

.new-ui .ui-card.card-bg-coldgreen-light:hover:after {
  box-shadow: inset -2px -2px 0 0 #50918a;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-preview,
.new-ui .ui-card.card-bg-coldgreen-dark .card-number,
.new-ui .ui-card.card-bg-coldgreen-dark .card-group,
.new-ui .ui-card.card-bg-coldgreen-dark .card-menu {
  z-index: 5;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-menu {
  right: 10px;
  bottom: 7px;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-menu .menu-toggle:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-dark:after {
  display: block;
  content: "";
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  background-color: #2C7466;
  border-radius: 5px;
  position: absolute;
  top: 6px;
  left: 6px;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-title,
.new-ui .ui-card.card-bg-coldgreen-dark .card-number,
.new-ui .ui-card.card-bg-coldgreen-dark .card-stats,
.new-ui .ui-card.card-bg-coldgreen-dark .card-group, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-title,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-number,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-stats,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-group {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-action-icon,
.new-ui .ui-card.card-bg-coldgreen-dark .card-delete, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-action-icon,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-delete {
  background-color: none;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-dark .card-delete:before, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-delete:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-action-icon:hover,
.new-ui .ui-card.card-bg-coldgreen-dark .card-delete:hover, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-action-icon:hover,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-delete:hover {
  background-color: transparent;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-coldgreen-dark .card-delete:hover:before, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-action-icon:hover:before,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-delete:hover:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-connections, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-connections {
  background-color: #050c0b;
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-connections:before, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-connections:before {
  color: #FFF;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-delete, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-delete {
  top: 10px;
  right: 10px;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-number, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-number {
  left: 12px;
  bottom: 12px;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-group .ui-card.split-test, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-group .ui-card.split-test {
  background-color: #FFF;
  color: #2C7466;
  box-shadow: 2px 2px 0 0 #1e4f45;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-coldgreen-dark .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-coldgreen-dark .card-group .ui-card.split-test .card-stats, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-group .ui-card.split-test .card-title,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-group .ui-card.split-test .card-number,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-group .ui-card.split-test .card-stats {
  color: #2C7466;
}

.new-ui .ui-card.card-bg-coldgreen-dark .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-dark .card-group .ui-card.split-test .card-delete:before, .new-ui .ui-card.card-bg-coldgreen-dark:hover .card-group .ui-card.split-test .card-action-icon:before,
.new-ui .ui-card.card-bg-coldgreen-dark:hover .card-group .ui-card.split-test .card-delete:before {
  color: #2C7466;
}

.new-ui .ui-card.card-bg-coldgreen-dark:hover:after {
  box-shadow: inset -2px -2px 0 0 #21564c;
}

.new-ui .ui-panel {
  background: #f2f3f6;
  background: -moz-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: -webkit-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: linear-gradient(to bottom, #FFF 0%, #f5f9ff 100%);
  border: 1px solid #c1cfe6;
  border-top: 1px solid #d8e1f0;
  border-left: 1px solid #d8e1f0;
  position: relative;
  border-radius: 0.25rem;
  padding: 2rem;
  z-index: 100;
}

.new-ui .ui-panel > footer {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  position: relative;
}

.new-ui .ui-panel > footer:before {
  display: block;
  content: "";
  width: 100%;
  height: 1px;
  background: #c1cfe6;
  box-shadow: 0 1px 0 0 rgba(255, 255, 255, 0.65);
  position: absolute;
  top: 0;
  left: 0;
}

.new-ui .ui-panel .ui-breadcrumb .title {
  font-size: 1.3rem !important;
}

.new-ui .ui-panel .panel-close,
.new-ui .ui-panel .close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.new-ui .ui-panel .panel-close:before,
.new-ui .ui-panel .close:before {
  color: #c1cfe6;
}

.new-ui .ui-panel .panel-header {
  padding: 2rem 1.5rem;
}

.new-ui .ui-panel .panel-header .title {
  font-size: 1.5rem;
}

.new-ui .ui-panel .panel-header .title .button-icon {
  display: inline;
}

.new-ui .ui-panel .panel-header .title .button-icon:before {
  margin-right: 0;
}

.new-ui .ui-panel .panel-content {
  position: relative;
  padding: 1rem;
}

.new-ui .ui-panel .panel-content > header {
  position: static;
}

.new-ui .ui-panel .panel-content .panel-close,
.new-ui .ui-panel .panel-content .close {
  top: -1rem;
  right: -1rem;
  margin-top: 0;
}

.new-ui .ui-panel .panel-content + .panel-content {
  margin-top: 1.25rem;
  padding-top: 1.25rem;
  position: relative;
}

.new-ui .ui-panel .panel-content + .panel-content:before {
  display: block;
  content: "";
  width: calc(100% + 2.5rem);
  height: 1px;
  background: #c1cfe6;
  box-shadow: 0 1px 0 0 rgba(255, 255, 255, 0.65);
  position: absolute;
  top: 0;
  left: -1.25rem;
}

.new-ui .ui-panel .panel-content + .panel-content .panel-close,
.new-ui .ui-panel .panel-content + .panel-content .close {
  top: 0.5rem;
}

.new-ui .ui-panel > footer:before {
  width: calc(100% + 2.5rem);
  left: -1.25rem;
}

.new-ui .ui-panel.no-margin {
  padding: 0;
}

.new-ui .ui-panel.float {
  box-shadow: 3px 3px 6px 0 rgba(93, 117, 154, 0.15);
}

.new-ui .ui-panel.pointer-up:before, .new-ui .ui-panel.pointer-up:after, .new-ui .ui-panel.pointer-up-right:before, .new-ui .ui-panel.pointer-up-right:after {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 30px;
  color: #FFF;
  position: absolute;
  top: -23px;
  left: 40px;
}

.new-ui .ui-panel.pointer-up:before, .new-ui .ui-panel.pointer-up-right:before {
  z-index: 2;
}

.new-ui .ui-panel.pointer-up:after, .new-ui .ui-panel.pointer-up-right:after {
  color: #c1cfe6;
  top: -24px;
}

.new-ui .ui-panel.pointer-up-right:before, .new-ui .ui-panel.pointer-up-right:after {
  left: auto;
  right: 40px;
}

.new-ui .ui-colorpicker {
  clear: both;
  display: inline-block !important;
  width: 350px;
  padding: 1.5rem;
}

.new-ui .ui-colorpicker:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .ui-colorpicker .color-palette {
  clear: both;
  max-width: 350px;
}

.new-ui .ui-colorpicker .color-palette:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .ui-colorpicker .color-palette .color {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 6px;
  margin-bottom: 6px;
  cursor: pointer;
}

.new-ui .ui-colorpicker .color-palette .color:not(.no-color):not(.selected):hover {
  -moz-transform: translate(-1px, -1px);
  -o-transform: translate(-1px, -1px);
  -ms-transform: translate(-1px, -1px);
  -webkit-transform: translate(-1px, -1px);
  transform: translate(-1px, -1px);
  box-shadow: 2px 2px 0 0 rgba(193, 207, 230, 0.65);
}

.new-ui .ui-colorpicker .color-palette .color.no-color {
  box-shadow: inset 0 0 0 2px #c1cfe6;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.new-ui .ui-colorpicker .color-palette .color.no-color:before {
  -moz-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  display: block;
  content: "";
  width: 200%;
  height: 2px;
  background-color: #c1cfe6;
  position: absolute;
  top: 5px;
  left: -6px;
}

.new-ui .ui-colorpicker .color-palette .color.selected {
  box-shadow: 0 0 0 3px #FFF, 0 0 0 4px #c1cfe6;
  border-radius: 1px;
}

.new-ui .ui-colorpicker .selection {
  padding: 0 1.5rem;
}

.new-ui .ui-colorpicker .selection .selected-color-sample {
  width: 100%;
  height: 0;
  padding-top: 100%;
}

.new-ui .ui-colorpicker .selection .selected-color-code {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  padding: 0.5rem;
  font-size: 1.125rem;
  text-align: center;
  margin-top: 1rem;
}

.new-ui .ui-colorpicker .options {
  display: block;
  height: auto;
  margin-top: 1rem;
}

.new-ui .ui-colorpicker .options [class*="button-action"] {
  display: block;
  width: 100%;
  margin-right: 1rem;
  text-align: center;
}

.new-ui .ui-checkbox {
  width: auto;
  height: auto;
  cursor: pointer;
  position: relative;
}

.new-ui .ui-checkbox input[type="checkbox"],
.new-ui .ui-checkbox input[type="radio"] {
  display: none !important;
}

.new-ui .ui-checkbox input[type="checkbox"] + label,
.new-ui .ui-checkbox input[type="radio"] + label,
.new-ui .ui-checkbox .checkbox {
  display: inline-block;
  margin-top: 0;
  white-space: nowrap;
  cursor: pointer;
  vertical-align: top;
}

.new-ui .ui-checkbox label {
  font-size: 0;
}

.new-ui .ui-checkbox input[type="checkbox"] + label,
.new-ui .ui-checkbox input[type="radio"] + label,
.new-ui .ui-checkbox .checkbox,
.new-ui .ui-checkbox .radio {
  width: auto;
  height: 1.375rem;
  padding-left: 2rem;
  text-align: left;
  line-height: 1.375rem;
  font-weight: normal;
  color: #758ba9;
  cursor: pointer;
}

.new-ui .ui-checkbox input[type="checkbox"] + label:before,
.new-ui .ui-checkbox input[type="radio"] + label:before,
.new-ui .ui-checkbox .checkbox:before,
.new-ui .ui-checkbox .radio:before {
  -moz-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  display: block;
  font-family: "glyphs";
  content: "";
  width: 1.375rem;
  height: 1.375rem;
  font-size: 1rem;
  line-height: 1.375rem;
  background: transparent;
  color: #FFF;
  text-align: center;
  position: absolute;
  left: 0;
  top: 50%;
  z-index: 5;
}

.new-ui .ui-checkbox input[type="checkbox"] + label:after,
.new-ui .ui-checkbox input[type="radio"] + label:after,
.new-ui .ui-checkbox .checkbox:after,
.new-ui .ui-checkbox .radio:after {
  -moz-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  display: block;
  content: "";
  width: 1.375rem;
  height: 1.375rem;
  text-align: left;
  position: absolute;
  left: 0;
  top: 50%;
  box-shadow: inset 0 0 0 1px #a7b8d6;
  background: #FFF;
  border-radius: 2px;
}

.new-ui .ui-checkbox input[type="checkbox"] + label:not(.disabled):hover,
.new-ui .ui-checkbox input[type="radio"] + label:not(.disabled):hover,
.new-ui .ui-checkbox .checkbox:not(.disabled):hover,
.new-ui .ui-checkbox .radio:not(.disabled):hover {
  color: #5d759a;
  transition: color 0.25s ease-out;
}

.new-ui .ui-checkbox input[type="checkbox"] + label:not(.disabled):hover:after,
.new-ui .ui-checkbox input[type="radio"] + label:not(.disabled):hover:after,
.new-ui .ui-checkbox .checkbox:not(.disabled):hover:after,
.new-ui .ui-checkbox .radio:not(.disabled):hover:after {
  box-shadow: inset 0 0 0 2px #a7b8d6;
  transition: all 0.25s ease-out;
}

.new-ui .ui-checkbox input[type="checkbox"]:checked + label,
.new-ui .ui-checkbox input[type="radio"]:checked + label,
.new-ui .ui-checkbox .checkbox.checked,
.new-ui .ui-checkbox .radio.checked {
  color: #5d9bfc;
}

.new-ui .ui-checkbox input[type="checkbox"]:checked + label:before,
.new-ui .ui-checkbox input[type="radio"]:checked + label:before,
.new-ui .ui-checkbox .checkbox.checked:before,
.new-ui .ui-checkbox .radio.checked:before {
  color: #FFF;
}

.new-ui .ui-checkbox input[type="checkbox"]:checked + label:after,
.new-ui .ui-checkbox input[type="radio"]:checked + label:after,
.new-ui .ui-checkbox .checkbox.checked:after,
.new-ui .ui-checkbox .radio.checked:after {
  box-shadow: none;
  background: #5d9bfc;
}

.new-ui .ui-checkbox input[type="checkbox"]:checked + label:not(.disabled):hover:after,
.new-ui .ui-checkbox input[type="radio"]:checked + label:not(.disabled):hover:after,
.new-ui .ui-checkbox .checkbox.checked:not(.disabled):hover:after,
.new-ui .ui-checkbox .radio.checked:not(.disabled):hover:after {
  box-shadow: inset 0 0 0 2px #5d9bfc;
}

.new-ui .ui-checkbox input[type="checkbox"]:disabled + label,
.new-ui .ui-checkbox input[type="radio"]:disabled + label,
.new-ui .ui-checkbox .checkbox.disabled,
.new-ui .ui-checkbox .radio.disabled {
  color: #c1cfe6;
  cursor: not-allowed;
}

.new-ui .ui-checkbox input[type="checkbox"]:disabled + label:before,
.new-ui .ui-checkbox input[type="radio"]:disabled + label:before,
.new-ui .ui-checkbox .checkbox.disabled:before,
.new-ui .ui-checkbox .radio.disabled:before {
  display: none;
}

.new-ui .ui-checkbox input[type="checkbox"]:disabled + label:after,
.new-ui .ui-checkbox input[type="radio"]:disabled + label:after,
.new-ui .ui-checkbox .checkbox.disabled:after,
.new-ui .ui-checkbox .radio.disabled:after {
  opacity: 0.525;
}

.new-ui .ui-checkbox input[type="checkbox"]:checked:disabled + label,
.new-ui .ui-checkbox input[type="radio"]:checked:disabled + label,
.new-ui .ui-checkbox .checkbox.checked.disabled,
.new-ui .ui-checkbox .radio.checked.disabled {
  color: #c1cfe6;
}

.new-ui .ui-checkbox input[type="checkbox"]:checked:disabled + label:before,
.new-ui .ui-checkbox input[type="radio"]:checked:disabled + label:before,
.new-ui .ui-checkbox .checkbox.checked.disabled:before,
.new-ui .ui-checkbox .radio.checked.disabled:before {
  display: block;
  color: #FFF;
}

.new-ui .ui-checkbox input[type="checkbox"]:checked:disabled + label:after,
.new-ui .ui-checkbox input[type="radio"]:checked:disabled + label:after,
.new-ui .ui-checkbox .checkbox.checked.disabled:after,
.new-ui .ui-checkbox .radio.checked.disabled:after {
  opacity: 0.525;
}

.new-ui .ui-checkbox input[type="radio"] + label:before, .new-ui .ui-checkbox input[type="radio"] + label:after,
.new-ui .ui-checkbox .radio:before,
.new-ui .ui-checkbox .radio:after {
  border-radius: 100%;
}

.new-ui .ui-checkbox input[type="radio"] + label:before,
.new-ui .ui-checkbox .radio:before {
  width: 0.5rem;
  height: 0.5rem;
  content: "";
  left: 0.45rem;
  background: #FFF !important;
}

.new-ui .ui-checkbox.no-label input[type="checkbox"] + label,
.new-ui .ui-checkbox.no-label .checkmark {
  text-indent: -100000px;
}

.new-ui .ui-checkbox.small input[type="checkbox"] + label,
.new-ui .ui-checkbox.small input[type="radio"] + label,
.new-ui .ui-checkbox.small .checkmark {
  width: 1.75rem;
  height: 1.75rem;
  line-height: 1.75rem;
  padding-left: 2.375rem;
  font-size: 1.15rem;
}

.new-ui .ui-checkbox.small input[type="checkbox"] + label:before,
.new-ui .ui-checkbox.small input[type="radio"] + label:before,
.new-ui .ui-checkbox.small .checkmark:before {
  width: 1.75rem;
  height: 1.75rem;
  font-size: 2.5rem;
  line-height: 1.75rem;
  left: -6px;
  margin-top: 2px;
  top: 50%;
}

.new-ui .ui-checkbox.small input[type="checkbox"] + label:after,
.new-ui .ui-checkbox.small input[type="radio"] + label:after,
.new-ui .ui-checkbox.small .checkmark:after {
  width: 1.75rem;
  height: 1.75rem;
  margin-top: 0.1rem;
}

.new-ui .ui-checkbox.medium input[type="checkbox"] + label,
.new-ui .ui-checkbox.medium input[type="radio"] + label,
.new-ui .ui-checkbox.medium .checkmark {
  width: 2.25rem;
  height: 2.25rem;
  line-height: 2.25rem;
  padding-left: 2.875rem;
  font-size: 1.35rem;
}

.new-ui .ui-checkbox.medium input[type="checkbox"] + label:before,
.new-ui .ui-checkbox.medium input[type="radio"] + label:before,
.new-ui .ui-checkbox.medium .checkmark:before {
  width: 2.25rem;
  height: 2.25rem;
  font-size: 3rem;
  line-height: 2.25rem;
  left: -6px;
  margin-top: -4px;
  top: 50%;
}

.new-ui .ui-checkbox.medium input[type="checkbox"] + label:after,
.new-ui .ui-checkbox.medium input[type="radio"] + label:after,
.new-ui .ui-checkbox.medium .checkmark:after {
  width: 2.25rem;
  height: 2.25rem;
  margin-top: -0.25rem;
}

@media (max-width: 480px) {
  .new-ui .ui-checkbox input[type="checkbox"] + label,
  .new-ui .ui-checkbox input[type="radio"] + label,
  .new-ui .ui-checkbox .checkmark {
    width: auto;
    height: 1.75rem;
    line-height: 1.75rem;
    padding-left: 2.25rem;
    font-size: 1.5rem;
  }
  .new-ui .ui-checkbox input[type="checkbox"] + label:before,
  .new-ui .ui-checkbox input[type="radio"] + label:before,
  .new-ui .ui-checkbox .checkmark:before {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 2.25rem;
    line-height: 1.75rem;
    left: -4px;
    text-indent: -100px;
    margin-top: -8px;
  }
  .new-ui .ui-checkbox input[type="checkbox"] + label:after,
  .new-ui .ui-checkbox input[type="radio"] + label:after,
  .new-ui .ui-checkbox .checkmark:after {
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 100%;
    position: absolute;
    left: 0;
    margin-top: -0.5rem;
  }
  .new-ui .ui-checkbox.medium input[type="checkbox"] + label,
  .new-ui .ui-checkbox.medium input[type="radio"] + label,
  .new-ui .ui-checkbox.medium .checkmark {
    width: auto;
    height: 1.75rem;
    line-height: 1.75rem;
    padding-left: 2.25rem;
    font-size: 1.15rem;
  }
  .new-ui .ui-checkbox.medium input[type="checkbox"] + label:before,
  .new-ui .ui-checkbox.medium input[type="radio"] + label:before,
  .new-ui .ui-checkbox.medium .checkmark:before {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 2.25rem;
    line-height: 1.75rem;
    left: -4px;
    margin-top: -9px;
  }
  .new-ui .ui-checkbox.medium input[type="checkbox"] + label:after,
  .new-ui .ui-checkbox.medium input[type="radio"] + label:after,
  .new-ui .ui-checkbox.medium .checkmark:after {
    width: 1.75rem;
    height: 1.75rem;
    margin-top: -0.55rem;
  }
}

@media (max-width: 420px) {
  .new-ui .ui-checkbox.medium input[type="checkbox"] + label,
  .new-ui .ui-checkbox.medium input[type="radio"] + label,
  .new-ui .ui-checkbox.medium .checkmark {
    font-size: 1rem;
  }
}

.new-ui .ui-checkbox-tag input[type="checkbox"] + label,
.new-ui .ui-checkbox-tag input[type="radio"] + label {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  width: 12rem;
  height: 2.25rem;
  line-height: 2.25rem;
  background: #FFF;
  box-shadow: inset 0 0 0 2px #f2f3f6;
  color: #f2f3f6;
  text-align: center;
  padding: 0;
}

.new-ui .ui-checkbox-tag input[type="checkbox"] + label:hover,
.new-ui .ui-checkbox-tag input[type="radio"] + label:hover {
  background: #FFF;
  color: #f2f3f6;
  transition: all 0.1s ease-out;
}

.new-ui .ui-checkbox-tag input[type="checkbox"]:checked + label,
.new-ui .ui-checkbox-tag input[type="radio"]:checked + label {
  background: #f2f3f6;
  color: #FFF;
}

@keyframes switch-left {
  from {
    left: 1.75rem;
  }
  to {
    left: 0.25rem;
  }
}

@-moz-keyframes switch-left {
  from {
    left: 1.75rem;
  }
  to {
    left: 0.25rem;
  }
}

@-webkit-keyframes switch-left {
  from {
    left: 1.75rem;
  }
  to {
    left: 0.25rem;
  }
}

@keyframes switch-right {
  from {
    left: 0.25rem;
  }
  to {
    left: 1.75rem;
  }
}

@-moz-keyframes switch-right {
  from {
    left: 0.25rem;
  }
  to {
    left: 1.75rem;
  }
}

@-webkit-keyframes switch-right {
  from {
    left: 0.25rem;
  }
  to {
    left: 1.75rem;
  }
}

.new-ui .ui-switch {
  display: inline-block;
  width: 3.5rem;
  height: 2rem;
  margin: 0;
  border-radius: 2rem;
  box-shadow: inset 0 0 0 2px #bababa;
  position: relative;
  vertical-align: top;
  cursor: pointer;
}

.new-ui .ui-switch:after {
  border-radius: 100%;
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  top: 0.25rem;
  text-align: center;
  color: #FFF;
  background: #5d9bfc;
}

.new-ui .ui-switch.is-off:after {
  left: 1.75rem;
  content: "Off";
  background: #bababa;
  -webkit-animation: switch-right 0.25s;
  -moz-animation: switch-right 0.25s;
  animation: switch-right 0.25s;
}

.new-ui .ui-switch.is-on {
  box-shadow: inset 0 0 0 2px #5d9bfc;
}

.new-ui .ui-switch.is-on:after {
  left: 0.25rem;
  content: "On";
  background: #5d9bfc;
  -webkit-animation: switch-left 0.25s;
  -moz-animation: switch-left 0.25s;
  animation: switch-left 0.25s;
}

.new-ui .ui-dropdown-menu {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  box-shadow: 3px 3px 6px 0 rgba(93, 117, 154, 0.15);
  display: block;
  width: auto;
  min-width: 9rem;
  max-width: 25rem;
  height: auto;
  max-height: 30rem;
  margin: 0;
  padding: 0.5em 0;
  font-size: 0.925rem;
  position: absolute;
  top: 2.25rem;
  left: 0;
  z-index: 5000;
  overflow-y: scroll;
  /*
	&::-webkit-scrollbar {
		display: none;
	}
	*/
}

.new-ui .ui-dropdown-menu h1,
.new-ui .ui-dropdown-menu h2,
.new-ui .ui-dropdown-menu h3,
.new-ui .ui-dropdown-menu h4,
.new-ui .ui-dropdown-menu h5 {
  display: block;
  font-family: "Source Sans Pro", "Source Sans Pro", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-weight: 700;
  font-size: 0.755rem;
  padding: 0 1.5em;
  text-transform: uppercase;
  margin-bottom: 0;
  color: #a7b8d6;
}

.new-ui .ui-dropdown-menu h1 + *[class*="list-"],
.new-ui .ui-dropdown-menu h1 + p,
.new-ui .ui-dropdown-menu h2 + *[class*="list-"],
.new-ui .ui-dropdown-menu h2 + p,
.new-ui .ui-dropdown-menu h3 + *[class*="list-"],
.new-ui .ui-dropdown-menu h3 + p,
.new-ui .ui-dropdown-menu h4 + *[class*="list-"],
.new-ui .ui-dropdown-menu h4 + p,
.new-ui .ui-dropdown-menu h5 + *[class*="list-"],
.new-ui .ui-dropdown-menu h5 + p {
  margin-top: 0.5rem;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option {
  display: block;
  width: auto;
  padding: 0.25rem calc(1rem + 1.5rem) 0.25rem 1rem;
  font-weight: normal;
  color: #758ba9;
  cursor: pointer;
  white-space: nowrap;
  position: relative;
  clear: both;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option *[class*="button-"],
.new-ui .ui-dropdown-menu .ui-dropdown-option .ui-toolbar,
.new-ui .ui-dropdown-menu .ui-dropdown-option .ui-tag,
.new-ui .ui-dropdown-menu .ui-dropdown-option .ui-tool {
  display: inline-block;
  vertical-align: top;
  float: right;
  margin-right: -1em;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option .ui-toolbar .ui-tool {
  float: none;
  margin-right: 0;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option .selection-match {
  font-weight: 600;
  color: black;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option:hover {
  color: #FFF;
  background: #5d9bfc;
  transition: all 0.15s ease-out;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option.selected {
  position: relative;
  color: #3378e3;
  font-weight: 600;
  background-color: #f4f8ff;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option.selected:before {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 1.5rem;
  height: 1.5rem;
  color: #5d9bfc;
  font-size: 1rem;
  line-height: 1.5rem;
  text-align: center;
  position: absolute;
  top: 50%;
  right: 0.5em;
  margin-top: -0.75rem;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option .ui-checkbox {
  position: absolute;
  right: 0.125em;
  top: 50%;
  margin-top: -0.7125rem;
}

.new-ui .ui-dropdown-menu .ui-dropdown-option h1,
.new-ui .ui-dropdown-menu .ui-dropdown-option h2,
.new-ui .ui-dropdown-menu .ui-dropdown-option h3,
.new-ui .ui-dropdown-menu .ui-dropdown-option h4,
.new-ui .ui-dropdown-menu .ui-dropdown-option h5 {
  padding: 0;
}

.new-ui .ui-dropdown-menu .list-blocks > div,
.new-ui .ui-dropdown-menu .list-blocks > li,
.new-ui .ui-dropdown-menu .list-blocks .list-item, .new-ui .ui-dropdown-menu.list-blocks > div,
.new-ui .ui-dropdown-menu.list-blocks > li,
.new-ui .ui-dropdown-menu.list-blocks .list-item {
  border-color: #d8e1f0;
}

.new-ui .ui-dropdown-menu.numbers {
  min-width: 15rem;
  padding: 0.25rem;
  text-align: left;
}

.new-ui .ui-dropdown-menu.numbers .ui-dropdown-option {
  display: block;
  width: 2rem;
  height: 2rem;
  padding: 0;
  border-radius: 0.1875rem;
  text-align: center;
  font-size: 1rem;
  line-height: 2rem;
  float: left;
  clear: none;
}

.new-ui .ui-dropdown-menu.numbers .ui-dropdown-option.selected:before {
  display: none;
}

.new-ui .ui-dropdown-menu.numbers .ui-dropdown-option:hover {
  font-weight: 600;
  transition: all 0.15s ease-out;
}

.new-ui .ui-dropdown {
  display: inline-block;
  margin: 0;
  box-sizing: border-box;
  vertical-align: top;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.new-ui .ui-dropdown .dropdown-toggle {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  background: #f2f3f6;
  background: -moz-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: -webkit-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: linear-gradient(to bottom, #FFF 0%, #f5f9ff 100%);
  width: auto;
  height: 2.25rem;
  padding: 0 2.25rem 0 1.25em;
  font-weight: 500;
  font-size: 0.925rem;
  line-height: 2.25rem;
  cursor: pointer;
  white-space: nowrap;
}

.new-ui .ui-dropdown .dropdown-toggle:before {
  font-family: "glyphs";
  content: "";
  width: 2.25rem;
  height: 2.25rem;
  line-height: 2.5rem;
  text-align: center;
  color: #6da7ff;
  position: absolute;
  top: 0;
  right: 0;
}

.new-ui .ui-dropdown .dropdown-toggle:hover:before {
  color: #5d9bfc;
  transition: color 0.1s ease-out;
}

.new-ui .ui-dropdown .dropdown-toggle .color {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-top: 10px;
}

.new-ui .ui-dropdown .dropdown-toggle .color.no-color {
  box-shadow: inset 0 0 0 2px #c1cfe6;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.new-ui .ui-dropdown .dropdown-toggle .color.no-color:before {
  -moz-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  display: block;
  content: "";
  width: 200%;
  height: 2px;
  background-color: #c1cfe6;
  position: absolute;
  top: 5px;
  left: -6px;
}

.new-ui .ui-dropdown .ui-dropdown-menu {
  display: none;
}

.new-ui .ui-dropdown.is-open .dropdown-toggle {
  z-index: 6000;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.new-ui .ui-dropdown.is-open .ui-dropdown-menu {
  display: block;
  margin-top: -1px;
  border-top-left-radius: 0;
}

.new-ui .ui-dropdown.disabled .dropdown-toggle {
  opacity: 0.65;
  cursor: not-allowed;
}

.new-ui .ui-dropdown.disabled.is-open .dropdown-toggle {
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.new-ui .ui-dropdown.disabled.is-open .ui-dropdown-menu {
  display: none !important;
}

.new-ui .ui-dropdown .dropdown-toggle.is-open {
  z-index: 6000;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.new-ui .ui-dropdown .dropdown-toggle.is-open + .ui-dropdown-menu {
  display: block;
  margin-top: -1px;
  border-top-left-radius: 0;
}

.new-ui .ui-dropdown .dropdown-toggle.disabled {
  opacity: 0.65;
  cursor: not-allowed;
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.new-ui .ui-dropdown .dropdown-toggle.disabled + .ui-dropdown-menu {
  display: none;
}

.new-ui .ui-dropdown + .ui-dropdown {
  margin-left: 1rem;
}

.new-ui .ui-dropdown:first-child {
  margin-left: 0;
}

.new-ui .ui-dropdown.fullwidth {
  display: block;
  width: 100%;
}

.new-ui .ui-dropdown.fullwidth .dropdown-toggle,
.new-ui .ui-dropdown.fullwidth .ui-dropdown-menu {
  width: 100%;
  min-width: 100%;
  max-width: 100%;
}

.new-ui .ui-dropdown.style-text .dropdown-toggle {
  height: auto;
  background: none;
  border: none;
  padding: 0;
  line-height: 1em;
}

.new-ui .ui-dropdown.style-text .dropdown-toggle:before {
  display: inline;
  width: auto;
  height: auto;
  line-height: 1.25em;
  text-align: right;
  position: static;
  float: right;
  margin-left: 0.25em;
}

.new-ui .ui-dropdown.style-text .ui-dropdown-menu {
  top: 1.5em;
}

.new-ui .ui-dropdown.no-icon .dropdown-toggle:before {
  display: none;
}

.new-ui .ui-dropdown.no-radius-left .dropdown-toggle {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.new-ui .ui-dropdown.no-radius-right .dropdown-toggle {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

@media (max-width: 1280px) {
  .new-ui .ui-dropdown .dropdown-toggle {
    height: 2.25rem;
    padding: 0.5rem 1.15rem 0.5rem 0.5rem;
    font-weight: 500;
  }
  .new-ui .ui-dropdown .dropdown-toggle:before {
    right: 0.05rem;
  }
}

.new-ui .ui-breadcrumb {
  margin-bottom: 0.125rem;
}

.new-ui .ui-breadcrumb .breadcrumb-current,
.new-ui .ui-breadcrumb .breadcrumb-origin,
.new-ui .ui-breadcrumb .breadcrumb-link {
  display: inline-block;
  line-height: 1.25rem;
  font-size: 1.125rem;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-weight: 400;
  vertical-align: top;
  margin-right: 0.25rem;
  position: relative;
}

.new-ui .ui-breadcrumb .breadcrumb-origin,
.new-ui .ui-breadcrumb .breadcrumb-link {
  padding-right: 1.25rem;
}

.new-ui .ui-breadcrumb .breadcrumb-origin:before,
.new-ui .ui-breadcrumb .breadcrumb-link:before {
  display: block;
  font-family: "glyphs";
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  content: "";
  color: inherit;
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -0.45rem;
  text-align: right;
}

.new-ui .ui-breadcrumb .breadcrumb-origin {
  color: #a7b8d6;
}

.new-ui .ui-breadcrumb .breadcrumb-origin:hover {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui .ui-breadcrumb .breadcrumb-current, .new-ui .ui-breadcrumb .breadcrumb-current.title,
.new-ui .ui-breadcrumb .breadcrumb-current .title {
  font-size: 1.125rem;
}

.new-ui .ui-autocomplete {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  background-color: #FFF;
  height: 3rem;
  position: relative;
}

.new-ui .ui-autocomplete .ui-autocomplete-head {
  position: relative;
}

.new-ui .ui-autocomplete .ui-autocomplete-head .input {
  width: 80%;
}

.new-ui .ui-autocomplete .ui-autocomplete-head .action {
  position: absolute;
  top: 0;
  right: 0;
  transition: width 0.15s ease-out;
}

.new-ui .ui-autocomplete .ui-autocomplete-head input[type="text"] {
  width: 100% !important;
  height: 3rem;
  line-height: 2.95rem;
  padding: 0 1.25em !important;
  border: none !important;
  background-color: transparent !important;
}

.new-ui .ui-autocomplete .ui-autocomplete-head input[type="text"]:hover, .new-ui .ui-autocomplete .ui-autocomplete-head input[type="text"]:focus {
  outline: none !important;
  box-shadow: none !important;
}

.new-ui .ui-autocomplete .ui-autocomplete-head input[type="text"]::-webkit-input-placeholder {
  font-size: 1.125rem;
  letter-spacing: -1px;
}

.new-ui .ui-autocomplete .ui-autocomplete-head input[type="text"]:-moz-placeholder {
  font-size: 1.125rem;
  letter-spacing: -1px;
}

.new-ui .ui-autocomplete .ui-autocomplete-head input[type="text"]::-moz-placeholder {
  font-size: 1.125rem;
  letter-spacing: -1px;
}

.new-ui .ui-autocomplete .ui-autocomplete-head input[type="text"]:-ms-input-placeholder {
  font-size: 1.125rem;
  letter-spacing: -1px;
}

.new-ui .ui-autocomplete .ui-autocomplete-icon,
.new-ui .ui-autocomplete .ui-autocomplete-cancel {
  display: block;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  top: 50%;
  left: -2rem;
  margin-top: -0.75rem;
  background: transparent;
  padding: 0;
}

.new-ui .ui-autocomplete .ui-autocomplete-icon:before,
.new-ui .ui-autocomplete .ui-autocomplete-cancel:before {
  display: block;
  width: 1.5rem;
  height: 1.5rem;
  font-family: "glyphs";
  content: "";
  text-align: center;
  line-height: 1.5rem;
  font-size: 0.875rem;
  color: #c1cfe6;
  position: absolute;
  top: 0;
  right: 0;
}

.new-ui .ui-autocomplete .ui-autocomplete-icon:before {
  content: "";
}

.new-ui .ui-autocomplete .ui-autocomplete-button {
  display: inline-block;
  width: auto;
  height: calc(3rem - 1px);
  padding: 0;
  box-shadow: inset 0 -1px 0 0 #c1cfe6, inset 1px 0 0 0 #c1cfe6;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  color: #758ba9;
  line-height: 3rem;
  font-weight: 400;
  background-color: #FFF;
  position: relative;
  cursor: pointer;
}

.new-ui .ui-autocomplete .ui-autocomplete-button:before {
  display: block;
  width: 3rem;
  height: 3rem;
  font-family: "glyphs";
  content: "";
  text-align: center;
  line-height: 3rem;
  font-size: 1.65rem;
  color: #a7b8d6;
  position: absolute;
  top: 0;
  left: 0;
}

.new-ui .ui-autocomplete .ui-autocomplete-button:hover:before {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui .ui-autocomplete .ui-autocomplete-button.edit, .new-ui .ui-autocomplete .ui-autocomplete-button.create {
  padding-left: 2.5rem;
  padding-right: 1.5rem;
  font-weight: 600;
}

.new-ui .ui-autocomplete .ui-autocomplete-button.edit:before, .new-ui .ui-autocomplete .ui-autocomplete-button.create:before {
  content: "";
  font-size: 1.125rem;
  font-weight: 400;
}

.new-ui .ui-autocomplete .ui-autocomplete-button.edit:before {
  content: "";
}

.new-ui .ui-autocomplete .ui-autocomplete-button.create:before {
  content: "";
}

.new-ui .ui-autocomplete .ui-dropdown-menu {
  display: none;
  width: calc(100% + 2px);
  min-width: calc(100% + 2px);
  max-width: calc(100% + 2px);
  padding: 0;
  position: absolute;
  top: calc(3rem - 2px);
  left: -1px;
  z-index: 10;
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.new-ui .ui-autocomplete .ui-dropdown-menu .ui-dropdown-option {
  position: relative;
}

.new-ui .ui-autocomplete .ui-dropdown-menu .ui-dropdown-option .list-tools {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  top: 50%;
  right: 1rem;
  text-align: right;
}

.new-ui .ui-autocomplete .ui-dropdown-menu .ui-dropdown-option:hover {
  z-index: 100;
  color: #3378e3;
  background: #FFF;
  box-shadow: 0 -1px 0 0 #6da7ff, 1px 0 0 0 #6da7ff, -1px 0 0 0 #6da7ff;
  border-bottom: 1px solid #6da7ff;
  transition: all 0.2s ease-out;
  z-index: 2;
}

.new-ui .ui-autocomplete .ui-dropdown-menu > .ui-dropdown-option:last-child:hover {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom: none;
  box-shadow: 0 -1px 0 0 #6da7ff, 0 1px 0 0 #6da7ff;
  transition: all 0.2s ease-out;
}

.new-ui .ui-autocomplete .ui-dropdown-menu.list-blocks > div,
.new-ui .ui-autocomplete .ui-dropdown-menu.list-blocks > li,
.new-ui .ui-autocomplete .ui-dropdown-menu.list-blocks .list-item {
  padding: 0.5em 1.425em;
  font-size: 1rem;
}

.new-ui .ui-autocomplete .ui-dropdown-menu:hover {
  border-color: #a7b8d6;
  transition: all 0.2s ease-out;
  z-index: 2;
}

.new-ui .ui-autocomplete .ui-panel {
  margin-top: 0.825rem;
  width: calc(100% + 2px);
  margin-left: -2px;
}

.new-ui .ui-autocomplete.is-open, .new-ui .ui-autocomplete.active {
  background-color: #eff5ff;
  box-shadow: 0 0 0 3px rgba(128, 178, 255, 0.125);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none;
  transition: all 0.25s ease-out;
}

.new-ui .ui-autocomplete.is-open .ui-dropdown-menu, .new-ui .ui-autocomplete.active .ui-dropdown-menu {
  display: block;
  opacity: 1;
  transition: all 0.25s ease-out;
  z-index: 100;
}

.new-ui .ui-autocomplete.is-open .ui-autocomplete-button, .new-ui .ui-autocomplete.active .ui-autocomplete-button {
  color: #5d759a;
  border-bottom-right-radius: 0;
  box-shadow: inset 1px 0 0 0 #c1cfe6, -3px 0 0 0 rgba(242, 243, 246, 0.75);
}

.new-ui .ui-datepicker,
.new-ui .ui-datepicker-2 {
  display: inline-block;
  width: auto;
  min-width: 240px;
  background-color: #FFF;
}

.new-ui .ui-datepicker table,
.new-ui .ui-datepicker-2 table {
  width: auto;
  table-layout: fixed;
  font-size: 1rem;
  border-collapse: separate;
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  border: 1px solid #c1cfe6;
  border-top: none;
  padding: 0.5rem;
}

.new-ui .ui-datepicker table .navigation,
.new-ui .ui-datepicker-2 table .navigation {
  background: #5d9bfc;
  position: relative;
  color: #FFF;
  border: 1px solid #3378e3;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.new-ui .ui-datepicker table .navigation .previous,
.new-ui .ui-datepicker table .navigation .next,
.new-ui .ui-datepicker table .navigation .selection,
.new-ui .ui-datepicker-2 table .navigation .previous,
.new-ui .ui-datepicker-2 table .navigation .next,
.new-ui .ui-datepicker-2 table .navigation .selection {
  display: inline-block;
  width: auto;
  height: 2.5rem;
  vertical-align: top;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.new-ui .ui-datepicker table .navigation .previous,
.new-ui .ui-datepicker table .navigation .next,
.new-ui .ui-datepicker-2 table .navigation .previous,
.new-ui .ui-datepicker-2 table .navigation .next {
  display: block;
  width: 2rem;
  height: 2.5rem;
  font-size: 0;
  position: absolute;
  top: 0;
  cursor: pointer;
}

.new-ui .ui-datepicker table .navigation .previous:before,
.new-ui .ui-datepicker table .navigation .next:before,
.new-ui .ui-datepicker-2 table .navigation .previous:before,
.new-ui .ui-datepicker-2 table .navigation .next:before {
  display: block;
  font-family: "glyphs";
  width: 100%;
  height: 100%;
  line-height: 2.5rem;
  font-size: 0.875rem;
  text-align: center;
  color: #a7b8d6;
  color: #FFF;
}

.new-ui .ui-datepicker table .navigation .previous,
.new-ui .ui-datepicker-2 table .navigation .previous {
  left: 0;
}

.new-ui .ui-datepicker table .navigation .previous:before,
.new-ui .ui-datepicker-2 table .navigation .previous:before {
  content: "";
}

.new-ui .ui-datepicker table .navigation .next,
.new-ui .ui-datepicker-2 table .navigation .next {
  right: 0;
}

.new-ui .ui-datepicker table .navigation .next:before,
.new-ui .ui-datepicker-2 table .navigation .next:before {
  content: "";
}

.new-ui .ui-datepicker table .navigation .selection,
.new-ui .ui-datepicker-2 table .navigation .selection {
  font-size: 1rem;
  line-height: 2.5rem;
  color: #FFF;
  font-weight: 600;
}

.new-ui .ui-datepicker table .navigation .selection .ui-dropdown .dropdown-toggle,
.new-ui .ui-datepicker-2 table .navigation .selection .ui-dropdown .dropdown-toggle {
  height: 2.5rem;
  line-height: 2.5rem;
  font-weight: 600;
  color: #FFF;
  text-align: center;
  position: relative;
}

.new-ui .ui-datepicker table .navigation .selection .ui-dropdown .dropdown-toggle:before,
.new-ui .ui-datepicker-2 table .navigation .selection .ui-dropdown .dropdown-toggle:before {
  display: none;
  content: "";
  width: 100%;
  height: 2px;
  position: absolute;
  left: -4px;
  top: auto;
  bottom: 0.5rem;
  background-color: #3378e3;
}

.new-ui .ui-datepicker table .navigation .selection .ui-dropdown .dropdown-toggle:hover:before,
.new-ui .ui-datepicker-2 table .navigation .selection .ui-dropdown .dropdown-toggle:hover:before {
  border-radius: 0.1875rem;
  background-color: rgba(0, 0, 0, 0.125);
}

.new-ui .ui-datepicker table .navigation .selection .ui-dropdown .ui-dropdown-menu,
.new-ui .ui-datepicker-2 table .navigation .selection .ui-dropdown .ui-dropdown-menu {
  text-align: left;
  font-weight: normal;
  color: #758ba9;
}

.new-ui .ui-datepicker table .navigation .selection .ui-dropdown .ui-dropdown-menu.is-open,
.new-ui .ui-datepicker-2 table .navigation .selection .ui-dropdown .ui-dropdown-menu.is-open {
  top: 2.25rem;
}

.new-ui .ui-datepicker table thead tr th,
.new-ui .ui-datepicker table thead tr td,
.new-ui .ui-datepicker table tbody tr th,
.new-ui .ui-datepicker table tbody tr td,
.new-ui .ui-datepicker-2 table thead tr th,
.new-ui .ui-datepicker-2 table thead tr td,
.new-ui .ui-datepicker-2 table tbody tr th,
.new-ui .ui-datepicker-2 table tbody tr td {
  width: 1.5rem;
  height: 1.5rem;
  padding: 0;
  text-align: center;
  border-radius: 0;
  vertical-align: middle;
}

.new-ui .ui-datepicker table thead tr th,
.new-ui .ui-datepicker-2 table thead tr th {
  color: black;
  padding-bottom: 0.5rem;
  border-radius: 0 !important;
}

.new-ui .ui-datepicker table tbody tr,
.new-ui .ui-datepicker-2 table tbody tr {
  height: 2rem;
}

.new-ui .ui-datepicker table tbody tr td,
.new-ui .ui-datepicker-2 table tbody tr td {
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  font-weight: 500;
  color: #758ba9;
  cursor: pointer;
  position: relative;
}

.new-ui .ui-datepicker table tbody tr td.empty,
.new-ui .ui-datepicker-2 table tbody tr td.empty {
  cursor: auto;
}

.new-ui .ui-datepicker table tbody tr td.selected,
.new-ui .ui-datepicker-2 table tbody tr td.selected {
  box-shadow: inset 0 0 0 1px #3378e3;
  background-color: #5d9bfc;
  color: #FFF;
  font-weight: 600;
  border-radius: 0.25rem;
}

.new-ui .ui-datepicker table tbody tr td.today,
.new-ui .ui-datepicker-2 table tbody tr td.today {
  box-shadow: inset 0 0 0 1px #3378e3;
  font-weight: 600;
  border-radius: 0.25rem;
}

.new-ui .ui-datepicker table tbody tr td:not(.empty):not(.selected):hover,
.new-ui .ui-datepicker-2 table tbody tr td:not(.empty):not(.selected):hover {
  background-color: rgba(168, 201, 252, 0.375);
  color: #3378e3;
  font-weight: 600;
  border-radius: 0.25rem;
  transition: all 0.15s ease-out;
}

.new-ui .ui-datepicker table.calendar thead .legend,
.new-ui .ui-datepicker-2 table.calendar thead .legend {
  background-color: #5d9bfc;
}

.new-ui .ui-datepicker table.calendar thead .legend th,
.new-ui .ui-datepicker-2 table.calendar thead .legend th {
  border-top: 1px solid #c1cfe6;
  border-right: 1px solid #c1cfe6;
  border-bottom: 1px solid #c1cfe6;
  color: #FFF;
}

.new-ui .ui-datepicker table.calendar thead .legend th:last-child,
.new-ui .ui-datepicker-2 table.calendar thead .legend th:last-child {
  border-right: none;
}

.new-ui .ui-datepicker table.calendar tbody tr,
.new-ui .ui-datepicker-2 table.calendar tbody tr {
  border-bottom: 1px solid #c1cfe6;
}

.new-ui .ui-datepicker table.calendar tbody tr td,
.new-ui .ui-datepicker-2 table.calendar tbody tr td {
  height: 2.75rem;
  line-height: 2.5rem;
  border-right: 1px solid #c1cfe6;
  border-bottom: 1px solid #c1cfe6;
}

.new-ui .ui-datepicker table.calendar tbody tr td.selected,
.new-ui .ui-datepicker-2 table.calendar tbody tr td.selected {
  box-shadow: inset 1px 1px 0 0 #3378e3, inset 1px 1px 0 0 #3378e3;
  border-right: 1px solid #3378e3;
  border-bottom: 1px solid #3378e3;
  border-radius: 0;
}

.new-ui .ui-datepicker table.calendar tbody tr td:not(.empty):not(.selected):hover,
.new-ui .ui-datepicker-2 table.calendar tbody tr td:not(.empty):not(.selected):hover {
  background-color: rgba(168, 201, 252, 0.375);
  color: #3378e3;
  font-weight: 600;
}

.new-ui .ui-datepicker table.calendar tbody tr:last-child td,
.new-ui .ui-datepicker-2 table.calendar tbody tr:last-child td {
  border-bottom: none;
}

.new-ui .ui-datepicker .ui-datepicker-head,
.new-ui .ui-datepicker-2 .ui-datepicker-head {
  clear: both;
  display: inline-block;
  width: 100%;
  padding: 0.75rem;
  background: #5d9bfc;
  position: relative;
  color: #FFF;
  border: 1px solid #3378e3;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  white-space: nowrap;
}

.new-ui .ui-datepicker .ui-datepicker-head:after,
.new-ui .ui-datepicker-2 .ui-datepicker-head:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .ui-datepicker .ui-datepicker-head .menu-horizontal,
.new-ui .ui-datepicker-2 .ui-datepicker-head .menu-horizontal {
  display: inline-block;
  width: auto;
}

.new-ui .ui-datepicker .ui-datepicker-head .menu-horizontal li,
.new-ui .ui-datepicker-2 .ui-datepicker-head .menu-horizontal li {
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
}

.new-ui .ui-datepicker .ui-datepicker-head .menu-horizontal.selection li,
.new-ui .ui-datepicker-2 .ui-datepicker-head .menu-horizontal.selection li {
  padding: 0.5rem 1.25rem 0.625rem 1.25rem;
}

.new-ui .ui-datepicker .ui-datepicker-head .menu-horizontal.selection li.is-active,
.new-ui .ui-datepicker-2 .ui-datepicker-head .menu-horizontal.selection li.is-active {
  background-color: #3378e3;
  border-radius: 0.25rem;
}

.new-ui .ui-datepicker .ui-datepicker-head .menu-horizontal.tools,
.new-ui .ui-datepicker-2 .ui-datepicker-head .menu-horizontal.tools {
  float: right;
}

.new-ui .ui-datepicker .ui-datepicker-head .menu-horizontal.tools li,
.new-ui .ui-datepicker-2 .ui-datepicker-head .menu-horizontal.tools li {
  padding: 0.5rem 0.5rem 0.625rem 0.5rem;
  color: #a8c9fc;
}

.new-ui .ui-datepicker .ui-datepicker-head .menu-horizontal.tools li:hover,
.new-ui .ui-datepicker-2 .ui-datepicker-head .menu-horizontal.tools li:hover {
  color: #FFF;
  transition: all 0.15s ease-out;
}

.new-ui .ui-datepicker .ui-datepicker-head.pointer-up:before,
.new-ui .ui-datepicker-2 .ui-datepicker-head.pointer-up:before {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  font-size: 20px;
  color: #3378e3;
  position: absolute;
  top: -15px;
  left: 20px;
}

.new-ui .ui-datepicker .ui-datepicker-navigation,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation {
  padding: 1.75rem 2.5rem;
  border: 1px solid #c1cfe6;
  border-top: none;
  position: relative;
}

.new-ui .ui-datepicker .ui-datepicker-navigation .previous,
.new-ui .ui-datepicker .ui-datepicker-navigation .next,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .previous,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .next {
  display: block;
  width: 1.25rem;
  height: 1.25rem;
  position: absolute;
  top: 50%;
  margin-top: -0.625rem;
  cursor: pointer;
}

.new-ui .ui-datepicker .ui-datepicker-navigation .previous:before,
.new-ui .ui-datepicker .ui-datepicker-navigation .next:before,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .previous:before,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .next:before {
  font-family: "glyphs";
  font-size: 1.5rem;
  color: #c1cfe6;
}

.new-ui .ui-datepicker .ui-datepicker-navigation .previous:hover:before,
.new-ui .ui-datepicker .ui-datepicker-navigation .next:hover:before,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .previous:hover:before,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .next:hover:before {
  color: #758ba9;
  transition: all 0.15s ease-out;
}

.new-ui .ui-datepicker .ui-datepicker-navigation .previous,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .previous {
  left: 1.25rem;
}

.new-ui .ui-datepicker .ui-datepicker-navigation .previous:before,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .previous:before {
  content: "";
}

.new-ui .ui-datepicker .ui-datepicker-navigation .next,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .next {
  right: 1.25rem;
}

.new-ui .ui-datepicker .ui-datepicker-navigation .next:before,
.new-ui .ui-datepicker-2 .ui-datepicker-navigation .next:before {
  content: "";
}

.new-ui .ui-datepicker .ui-datepicker-content,
.new-ui .ui-datepicker-2 .ui-datepicker-content {
  padding: 1.25rem 2.5rem;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.new-ui .ui-datepicker .ui-datepicker-content.no-padding,
.new-ui .ui-datepicker-2 .ui-datepicker-content.no-padding {
  padding: 0;
}

.new-ui .ui-datepicker .ui-datepicker-content.months, .new-ui .ui-datepicker .ui-datepicker-content.weeks, .new-ui .ui-datepicker .ui-datepicker-content.days,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days {
  width: 100%;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table, .new-ui .ui-datepicker .ui-datepicker-content.weeks table, .new-ui .ui-datepicker .ui-datepicker-content.days table,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table {
  width: auto;
  min-width: 50rem;
  margin: 0;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td {
  height: 0;
  text-align: center;
  font-weight: 600;
  color: #c1cfe6;
  font-size: 1.25rem;
  border-right: 1px solid #c1cfe6;
  border-bottom: 1px solid #c1cfe6;
  position: relative;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td .label, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td .label, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td .label {
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: inline-block;
  font-weight: 600;
  font-size: 1rem;
  color: #c1cfe6;
  position: absolute;
  top: 50%;
  left: 50%;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td:not(.empty):not(.selected):hover, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td:not(.empty):not(.selected):hover, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td:not(.empty):not(.selected):hover,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td:not(.empty):not(.selected):hover,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td:not(.empty):not(.selected):hover,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td:not(.empty):not(.selected):hover {
  background-color: rgba(168, 201, 252, 0.15);
  border-radius: 0;
  border-color: #c1cfe6 !important;
  border-right-color: #c1cfe6 !important;
  transition: all 0.15s ease-out;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td:not(.empty):not(.selected):hover .label, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td:not(.empty):not(.selected):hover .label, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td:not(.empty):not(.selected):hover .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td:not(.empty):not(.selected):hover .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td:not(.empty):not(.selected):hover .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td:not(.empty):not(.selected):hover .label {
  color: #a7b8d6;
  transition: color 0.15s ease-out;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td.selected, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td.selected, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td.selected,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td.selected,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td.selected,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td.selected {
  background-color: rgba(168, 201, 252, 0.375);
  border-radius: 0;
  box-shadow: none;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td.selected .label, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td.selected .label, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td.selected .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td.selected .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td.selected .label,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td.selected .label {
  color: #5d9bfc;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td.selected:before, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td.selected:before, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td.selected:before,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td.selected:before,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td.selected:before,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td.selected:before {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 20px;
  height: 20px;
  border-radius: 20px;
  line-height: 20px;
  color: #FFF;
  font-size: 1rem;
  font-weight: normal;
  text-align: center;
  background-color: #5d9bfc;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td.exclude:before, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td.exclude:before, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td.exclude:before,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td.exclude:before,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td.exclude:before,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td.exclude:before {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 20px;
  height: 20px;
  border-radius: 20px;
  line-height: 20px;
  color: #e26464;
  font-size: 1rem;
  font-weight: normal;
  text-align: center;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td:last-child, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td:last-child, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td:last-child,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td:last-child,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td:last-child,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td:last-child {
  border-right: 0;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr:last-child td, .new-ui .ui-datepicker .ui-datepicker-content.weeks table tr:last-child td, .new-ui .ui-datepicker .ui-datepicker-content.days table tr:last-child td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr:last-child td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr:last-child td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr:last-child td {
  border-bottom: none;
}

.new-ui .ui-datepicker .ui-datepicker-content.months table tr td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.months table tr td {
  width: 16.666666%;
  padding: 16.666666% 0 0 0;
}

.new-ui .ui-datepicker .ui-datepicker-content.weeks table tr td, .new-ui .ui-datepicker .ui-datepicker-content.days table tr td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.weeks table tr td,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td {
  width: 14.285714%;
  padding: 14.285714% 0 0 0;
}

.new-ui .ui-datepicker .ui-datepicker-content.days table,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table {
  min-width: 30rem;
}

.new-ui .ui-datepicker .ui-datepicker-content.days table tr td.selected:before,
.new-ui .ui-datepicker-2 .ui-datepicker-content.days table tr td.selected:before {
  width: 14px;
  height: 14px;
  border-radius: 14px;
  line-height: 16px;
  font-size: 12px;
}

.new-ui .ui-datepicker.multiselect,
.new-ui .ui-datepicker-2.multiselect {
  display: inline-block;
  width: auto;
}

.new-ui .ui-datepicker.multiselect table,
.new-ui .ui-datepicker-2.multiselect table {
  padding: 0;
  border: none;
  margin-right: 3.25rem;
}

.new-ui .ui-datepicker.multiselect table .navigation,
.new-ui .ui-datepicker-2.multiselect table .navigation {
  background: transparent;
  position: relative;
  border: none;
  border-radius: 0;
  position: relative;
  box-shadow: inset 0 0 0 1px red;
}

.new-ui .ui-datepicker.multiselect table .navigation .previous,
.new-ui .ui-datepicker.multiselect table .navigation .next,
.new-ui .ui-datepicker-2.multiselect table .navigation .previous,
.new-ui .ui-datepicker-2.multiselect table .navigation .next {
  color: #758ba9;
}

.new-ui .ui-datepicker.multiselect table .navigation .selection,
.new-ui .ui-datepicker-2.multiselect table .navigation .selection {
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  color: black;
  position: absolute;
  left: 50%;
  top: -4.375rem;
}

.new-ui .ui-datepicker.multiselect table:last-child,
.new-ui .ui-datepicker-2.multiselect table:last-child {
  margin-right: 0;
}

.new-ui .ui-datepicker.small .ui-datepicker-head,
.new-ui .ui-datepicker-2.small .ui-datepicker-head {
  padding: 0;
}

.new-ui .ui-datepicker.small .ui-datepicker-head .menu-horizontal li,
.new-ui .ui-datepicker-2.small .ui-datepicker-head .menu-horizontal li {
  font-size: 0.75rem;
}

.new-ui .ui-datepicker.small .ui-datepicker-content table tr td,
.new-ui .ui-datepicker-2.small .ui-datepicker-content table tr td {
  font-size: 0.75rem;
}

.new-ui .ui-datepicker.small .ui-datepicker-content table tr td .label,
.new-ui .ui-datepicker-2.small .ui-datepicker-content table tr td .label {
  font-size: 0.825rem;
}

.new-ui .ui-datepicker.small .ui-datepicker-content table tr td.selected,
.new-ui .ui-datepicker-2.small .ui-datepicker-content table tr td.selected {
  background-color: rgba(168, 201, 252, 0.375);
  border-radius: 0;
  box-shadow: none;
}

.new-ui .ui-datepicker.small .ui-datepicker-content table tr td.selected .label,
.new-ui .ui-datepicker-2.small .ui-datepicker-content table tr td.selected .label {
  color: #5d9bfc;
}

.new-ui .ui-datepicker.small .ui-datepicker-content table tr td.selected:before, .new-ui .ui-datepicker.small .ui-datepicker-content table tr td.exclude:before,
.new-ui .ui-datepicker-2.small .ui-datepicker-content table tr td.selected:before,
.new-ui .ui-datepicker-2.small .ui-datepicker-content table tr td.exclude:before {
  width: 12px;
  height: 12px;
  border-radius: 12px;
  line-height: 12px;
  font-size: 10px;
}

.new-ui .ui-datepicker.small .ui-datepicker-content table tr:last-child td,
.new-ui .ui-datepicker-2.small .ui-datepicker-content table tr:last-child td {
  border-bottom: none;
}

.new-ui .ui-datepicker.small .ui-datepicker-content.months table, .new-ui .ui-datepicker.small .ui-datepicker-content.weeks table, .new-ui .ui-datepicker.small .ui-datepicker-content.days table,
.new-ui .ui-datepicker-2.small .ui-datepicker-content.months table,
.new-ui .ui-datepicker-2.small .ui-datepicker-content.weeks table,
.new-ui .ui-datepicker-2.small .ui-datepicker-content.days table {
  min-width: 25rem;
}

.new-ui .ui-datepicker.medium .ui-datepicker-head,
.new-ui .ui-datepicker-2.medium .ui-datepicker-head {
  padding: 0.35rem;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content table tr td,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content table tr td {
  font-size: 0.825rem;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content table tr td .label,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content table tr td .label {
  font-size: 0.9rem;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content table tr td.selected,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content table tr td.selected {
  background-color: rgba(168, 201, 252, 0.375);
  border-radius: 0;
  box-shadow: none;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content table tr td.selected .label,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content table tr td.selected .label {
  color: #5d9bfc;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content table tr td.selected:before,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content table tr td.selected:before {
  width: 16px;
  height: 16px;
  border-radius: 16px;
  line-height: 16px;
  font-size: 13px;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content table tr td.exclude:before,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content table tr td.exclude:before {
  width: 16px;
  height: 16px;
  border-radius: 16px;
  line-height: 16px;
  font-size: 13px;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content table tr:last-child td,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content table tr:last-child td {
  border-bottom: none;
}

.new-ui .ui-datepicker.medium .ui-datepicker-content.months table, .new-ui .ui-datepicker.medium .ui-datepicker-content.weeks table, .new-ui .ui-datepicker.medium .ui-datepicker-content.days table,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content.months table,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content.weeks table,
.new-ui .ui-datepicker-2.medium .ui-datepicker-content.days table {
  min-width: 35rem;
}

.new-ui .ui-datepicker.dropdown,
.new-ui .ui-datepicker-2.dropdown {
  position: absolute;
  top: 2.65rem;
  left: 0;
  z-index: 10;
}

.new-ui .ui-weekday-picker,
.new-ui .ui-month-picker,
.new-ui .ui-day-picker {
  position: relative;
}

.new-ui .ui-date {
  position: relative;
}

.new-ui .ui-date .ui-datepicker,
.new-ui .ui-date .ui-datepicker-2 {
  position: absolute;
  top: 2.25rem;
  left: 0;
  z-index: 50;
}

.new-ui .ui-toolbar {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  display: block;
  width: 100%;
  height: 2rem;
  min-height: 1.25rem;
  padding: 0;
  color: #a7b8d6;
  background-color: #eff5ff;
  border: 1px solid #c1cfe6;
  text-align: right;
}

.new-ui .ui-toolbar .title {
  text-align: left;
}

.new-ui .ui-toolbar > a:hover,
.new-ui .ui-toolbar > .ui-tool:hover {
  color: #304565;
}

.new-ui .ui-toolbar .tool {
  text-align: left;
}

.new-ui .ui-toolbar .tool-group {
  display: inline-block;
  width: auto;
  text-align: left;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  border-left: 1px solid #c1cfe6;
}

.new-ui .ui-toolbar .tool-group .menu-horizontal {
  display: inline-block;
  width: auto;
  height: 4rem;
}

.new-ui .ui-toolbar .tool-group .menu-horizontal li {
  height: 4rem;
  vertical-align: top;
}

.new-ui .ui-toolbar .tool-group .label {
  display: inline-block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #5d9bfc;
  margin-right: 1rem;
}

.new-ui .ui-toolbar.header, .new-ui .ui-toolbar.flowchart-toolbar {
  height: 4rem;
}

.new-ui .ui-toolbar.header .title,
.new-ui .ui-toolbar.header .tool,
.new-ui .ui-toolbar.header .tool-icon,
.new-ui .ui-toolbar.header .tool-group, .new-ui .ui-toolbar.flowchart-toolbar .title,
.new-ui .ui-toolbar.flowchart-toolbar .tool,
.new-ui .ui-toolbar.flowchart-toolbar .tool-icon,
.new-ui .ui-toolbar.flowchart-toolbar .tool-group {
  height: 4rem;
  line-height: 4rem;
  vertical-align: top;
}

.new-ui .ui-toolbar.header .tool,
.new-ui .ui-toolbar.header .tool-icon, .new-ui .ui-toolbar.flowchart-toolbar .tool,
.new-ui .ui-toolbar.flowchart-toolbar .tool-icon {
  cursor: pointer;
}

.new-ui .ui-toolbar.header .tool, .new-ui .ui-toolbar.flowchart-toolbar .tool {
  margin-right: 1.25rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #5d759a;
}

.new-ui .ui-toolbar.header .tool:last-child, .new-ui .ui-toolbar.flowchart-toolbar .tool:last-child {
  margin-right: 0;
}

.new-ui .ui-toolbar.header .tool-icon, .new-ui .ui-toolbar.flowchart-toolbar .tool-icon {
  margin-right: 0;
}

.new-ui .ui-toolbar.header .tool-icon:before, .new-ui .ui-toolbar.flowchart-toolbar .tool-icon:before {
  font-size: 1.35rem;
}

.new-ui .ui-toolbar.header .sidebar-toggle:before, .new-ui .ui-toolbar.flowchart-toolbar .sidebar-toggle:before {
  font-size: 1.625rem;
}

.new-ui .ui-toolbar.header .sidebar-toggle .ui-count, .new-ui .ui-toolbar.flowchart-toolbar .sidebar-toggle .ui-count {
  position: absolute;
  top: 0.5rem;
  right: -0.75rem;
}

.new-ui .ui-toolbar.header .sidebar-toggle.is-active, .new-ui .ui-toolbar.flowchart-toolbar .sidebar-toggle.is-active {
  background-color: #FFF;
  box-shadow: -20px 0 0 0 #FFF, 20px 0 0 0 #FFF;
}

.new-ui .ui-toolbar.header .sidebar-toggle.is-active:before, .new-ui .ui-toolbar.flowchart-toolbar .sidebar-toggle.is-active:before {
  color: #5d9bfc;
}

.new-ui .ui-toolbar.selectable {
  display: inline-block;
  width: auto;
  height: auto;
  min-height: auto;
  white-space: normal;
  box-shadow: none;
  overflow: visible;
  border-radius: 0.25rem;
}

.new-ui .ui-toolbar.selectable,
.new-ui .ui-toolbar.selectable > ul {
  font-size: 0;
  overflow: visible;
  margin-left: 1px;
}

.new-ui .ui-toolbar.selectable > li,
.new-ui .ui-toolbar.selectable .option-select,
.new-ui .ui-toolbar.selectable > ul > li,
.new-ui .ui-toolbar.selectable > ul .option-select {
  display: inline-block;
  width: auto;
  height: auto;
  padding: 0.5em 0.75em;
  font-size: 0.925rem;
  cursor: pointer;
  border: 1px solid #c1cfe6;
  border-collapse: collapse;
  margin-left: -1px;
  position: relative;
  background-color: #FFF;
  font-weight: 600;
}

.new-ui .ui-toolbar.selectable > li:first-child,
.new-ui .ui-toolbar.selectable .option-select:first-child,
.new-ui .ui-toolbar.selectable > ul > li:first-child,
.new-ui .ui-toolbar.selectable > ul .option-select:first-child {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.new-ui .ui-toolbar.selectable > li:last-child,
.new-ui .ui-toolbar.selectable .option-select:last-child,
.new-ui .ui-toolbar.selectable > ul > li:last-child,
.new-ui .ui-toolbar.selectable > ul .option-select:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.new-ui .ui-toolbar.selectable > li.is-active, .new-ui .ui-toolbar.selectable > li.active,
.new-ui .ui-toolbar.selectable .option-select.is-active,
.new-ui .ui-toolbar.selectable .option-select.active,
.new-ui .ui-toolbar.selectable > ul > li.is-active,
.new-ui .ui-toolbar.selectable > ul > li.active,
.new-ui .ui-toolbar.selectable > ul .option-select.is-active,
.new-ui .ui-toolbar.selectable > ul .option-select.active {
  border-color: #3378e3;
  background-color: #5d9bfc;
  color: #FFF;
  z-index: 2;
}

.new-ui .ui-toolbar.selectable > li:hover,
.new-ui .ui-toolbar.selectable .option-select:hover,
.new-ui .ui-toolbar.selectable > ul > li:hover,
.new-ui .ui-toolbar.selectable > ul .option-select:hover {
  color: #5d9bfc;
  transition: all 0.15s ease-out;
}

.new-ui .ui-toolbar.selectable > li:hover.is-active, .new-ui .ui-toolbar.selectable > li:hover.active,
.new-ui .ui-toolbar.selectable .option-select:hover.is-active,
.new-ui .ui-toolbar.selectable .option-select:hover.active,
.new-ui .ui-toolbar.selectable > ul > li:hover.is-active,
.new-ui .ui-toolbar.selectable > ul > li:hover.active,
.new-ui .ui-toolbar.selectable > ul .option-select:hover.is-active,
.new-ui .ui-toolbar.selectable > ul .option-select:hover.active {
  color: #FFF;
  background-color: #5d9bfc;
}

.new-ui .ui-toolbar.selectable.small > li,
.new-ui .ui-toolbar.selectable.small .option-select {
  padding: 0.25em 0.37em;
  font-size: 0.825rem;
}

.new-ui .ui-toolbar.selectable.boolean > li,
.new-ui .ui-toolbar.selectable.boolean .option-select,
.new-ui .ui-toolbar.selectable.boolean > ul > li,
.new-ui .ui-toolbar.selectable.boolean > ul .option-select {
  width: 2.5rem;
  padding: 0.5em 0;
  text-align: center;
}

.new-ui .ui-toolbar.selectable.boolean.small > li,
.new-ui .ui-toolbar.selectable.boolean.small .option-select,
.new-ui .ui-toolbar.selectable.boolean.small > ul > li,
.new-ui .ui-toolbar.selectable.boolean.small > ul .option-select {
  padding: 0.25em 0;
}

.new-ui .ui-toolbar.selectable.is-disabled, .new-ui .ui-toolbar.selectable.disabled {
  cursor: not-allowed !important;
}

.new-ui .ui-tabs {
  clear: both;
  height: auto;
  cursor: pointer;
  padding: 0;
}

.new-ui .ui-tabs:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .ui-tabs .tab-navigation {
  overflow: hidden;
}

.new-ui .ui-tabs .tab-navigation > ul {
  display: block;
  width: 100%;
  height: auto;
}

.new-ui .ui-tabs .tab-navigation > ul > li,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle {
  display: inline-block;
  width: auto;
  height: auto;
  min-height: 2.75rem;
  padding: 0.75rem 2rem;
  margin-left: -0.075rem;
  vertical-align: middle;
  font-weight: 600;
  font-size: 1.125rem;
  text-align: center;
  color: #758ba9;
  box-shadow: 1px 0 0 0 #c1cfe6, 2px 0 0 0 rgba(255, 255, 255, 0.75);
  background: #e0e9f9;
  box-shadow: inset 0 0 0 1px #c1cfe6, inset 0 -4px 0 0 rgba(193, 207, 230, 0.25);
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  cursor: pointer;
  position: relative;
}

.new-ui .ui-tabs .tab-navigation > ul > li:first-child,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle:first-child {
  margin-left: 0;
}

.new-ui .ui-tabs .tab-navigation > ul > li:hover,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle:hover {
  color: #5d9bfc;
  transition: color 0.25s ease-out;
}

.new-ui .ui-tabs .tab-navigation > ul > li[class*="glyph-"]:before, .new-ui .ui-tabs .tab-navigation > ul > li[class*="icon-"]:before,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle[class*="glyph-"]:before,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle[class*="icon-"]:before {
  font-size: 1.5rem;
  margin-right: 0.5rem;
  margin-top: 1px;
}

.new-ui .ui-tabs .tab-navigation > ul > li[class*="glyph-chart"]:before,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle[class*="glyph-chart"]:before {
  font-size: 1.25rem;
}

.new-ui .ui-tabs .tab-navigation > ul > li.is-open, .new-ui .ui-tabs .tab-navigation > ul > li.active,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle.is-open,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle.active {
  background: #FFF;
  color: #5d9bfc;
  box-shadow: inset 0 0 0 1px #c1cfe6;
}

.new-ui .ui-tabs .tab-navigation > ul > li.is-open:after, .new-ui .ui-tabs .tab-navigation > ul > li.active:after,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle.is-open:after,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle.active:after {
  display: block;
  content: "";
  width: calc(100% - 1px);
  height: 4px;
  background: #FFF;
  position: absolute;
  bottom: -2px;
  left: 1px;
}

.new-ui .ui-tabs .tab-navigation > ul > li.disabled,
.new-ui .ui-tabs .tab-navigation > ul .tab-toggle.disabled {
  background: rgba(255, 255, 255, 0.05);
  box-shadow: inset 0 0 0 1px rgba(193, 207, 230, 0.45), inset 0 -4px 0 0 rgba(216, 225, 240, 0.625);
  color: #c1cfe6;
}

.new-ui .ui-tabs .tab-navigation.even {
  background: #f2f3f6;
  box-shadow: inset 0 -1px 0 0 #c1cfe6, inset 0 -4px 0 0 rgba(216, 225, 240, 0.575);
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.new-ui .ui-tabs .tab-navigation.even > ul {
  display: table;
  width: 100%;
}

.new-ui .ui-tabs .tab-navigation.even > ul > li,
.new-ui .ui-tabs .tab-navigation.even > ul .tab-toggle {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  box-shadow: 1px 0 0 0 #c1cfe6, 2px 0 0 0 rgba(255, 255, 255, 0.75);
  cursor: pointer;
}

.new-ui .ui-tabs .ui-toolbar:not(.selectable) {
  box-shadow: inset 0 1px 0 0 #c1cfe6, inset 1px 0 0 0 #c1cfe6, inset -1px 0 0 0 #c1cfe6;
  margin-top: -1px;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.new-ui .ui-tabs .tab-panel {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  display: none;
  width: 100%;
  box-sizing: border-box;
  margin-top: -1px;
  padding: 3rem;
}

.new-ui .ui-tabs .tab-panel.is-open {
  display: block;
}

.new-ui .ui-tabs .ui-toolbar + .tab-panel {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-top-color: #d8e1f0;
}

.new-ui .ui-tabs.is-open .tab-panel {
  display: block;
}

.new-ui .ui-popup {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  display: none;
  width: auto;
  max-width: 30rem;
  height: auto;
  max-height: 12rem;
  padding: 1.25rem;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}

.new-ui .ui-popup .popup-message {
  display: inline-block;
  vertical-align: middle;
  font-size: 1.125rem;
  line-height: 1.2em;
  font-weight: normal;
  color: #2f313a;
}

.new-ui .ui-popup.confirmation {
  padding-right: 6rem;
}

.new-ui .ui-popup.confirmation .confirm-yes,
.new-ui .ui-popup.confirmation .confirm-no {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  display: inline-block;
  width: auto;
  height: 1rem;
  font-size: 0;
  line-height: 0.9em;
  text-transform: uppercase;
  position: absolute;
  top: 50%;
}

.new-ui .ui-popup.confirmation .confirm-yes:before,
.new-ui .ui-popup.confirmation .confirm-no:before {
  display: inline-block;
  font-family: "glyphs";
  font-size: 1rem;
  line-height: 1rem;
  color: #c1cfe6;
}

.new-ui .ui-popup.confirmation .confirm-yes {
  right: 3rem;
}

.new-ui .ui-popup.confirmation .confirm-yes:before {
  content: "";
}

.new-ui .ui-popup.confirmation .confirm-yes:hover:before {
  color: #50a475;
  transition: color 0.1s ease-out;
}

.new-ui .ui-popup.confirmation .confirm-no {
  right: 1.25rem;
}

.new-ui .ui-popup.confirmation .confirm-no:before {
  content: "";
}

.new-ui .ui-popup.confirmation .confirm-no:hover:before {
  color: #e26464;
  transition: color 0.1s ease-out;
}

.new-ui .ui-popup.is-open {
  display: block;
}

.new-ui *[data-type="cm-toggle"].is-open + .ui-popup {
  display: block;
}

.new-ui .ui-alert {
  display: block;
  width: 100%;
  height: auto;
  padding: 1.25rem;
  border-radius: 0.25rem;
  position: relative;
  font-size: 1.15rem;
  margin: 1.5rem auto;
}

.new-ui .ui-alert .ui-close:before {
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
  transform: scale(1);
  font-family: "glyphs";
  display: "inline";
  content: "";
  width: 1rem;
  height: 1rem;
  font-size: 0.75rem;
  line-height: 1rem;
  text-align: center;
  position: absolute;
  top: 0.35rem;
  right: 0.35rem;
  cursor: pointer;
}

.new-ui .ui-alert .ui-close:hover:before {
  -moz-transform: scale(1.25);
  -o-transform: scale(1.25);
  -ms-transform: scale(1.25);
  -webkit-transform: scale(1.25);
  transform: scale(1.25);
  transition: transform 0.15s ease-out;
}

.new-ui .ui-alert[class*="icon-"], .new-ui .ui-alert[class*="glyph-"] {
  padding-left: 4rem;
}

.new-ui .ui-alert[class*="icon-"]:before, .new-ui .ui-alert[class*="glyph-"]:before {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  display: table-cell;
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  font-size: 1.25rem;
  position: absolute;
  top: 50%;
  left: 1rem;
}

.new-ui .ui-alert.error {
  background: rgba(224, 152, 152, 0.25);
  border: 1px solid #e26464;
  color: #e26464;
  border-left: 4px solid #e26464;
}

.new-ui .ui-alert.error .ui-close {
  color: #e26464;
}

.new-ui .ui-alert.success {
  background: rgba(139, 195, 163, 0.25);
  border: 1px solid #50a475;
  color: #50a475;
  border-left: 4px solid #50a475;
}

.new-ui .ui-alert.success .ui-close {
  color: #50a475;
}

.new-ui .ui-alert.warning {
  background: rgba(240, 216, 134, 0.25);
  border: 1px solid #f0d886;
  color: #e59400;
  border-left: 4px solid #f0d886;
}

.new-ui .ui-alert.warning .ui-close {
  color: #f0d886;
}

.new-ui .ui-alert.information {
  background: rgba(128, 178, 255, 0.25);
  border: 1px solid #80b2ff;
  color: #5d9bfc;
  border-left: 4px solid #80b2ff;
}

.new-ui .ui-alert.information .ui-close {
  color: #f0d886;
}

.new-ui .ui-alert + .new-ui .ui-alert {
  margin-top: 1rem;
}

.new-ui .ui-notification {
  display: block;
  width: 100%;
  height: auto;
  text-align: center;
  position: relative;
  /*
	FOR REFERENCE:

	<section class="page-section">
		<div class="ui-notification system-message">
			<h2>Title is optional</h2>
			<p>This is a system message.</p>
		</div>
	</section>

	*/
}

.new-ui .ui-notification p {
  display: block;
  width: auto;
  margin: 0 0 1.25rem 0;
  font-size: 1.25rem;
  line-height: 1.125em;
  color: #758ba9;
}

.new-ui .ui-notification h1,
.new-ui .ui-notification h2,
.new-ui .ui-notification h3 {
  color: #686cbe;
  margin-top: 0.5em;
}

.new-ui .ui-notification h1 + p,
.new-ui .ui-notification h2 + p,
.new-ui .ui-notification h3 + p {
  margin-top: 1.25em;
}

.new-ui .ui-notification.system-message {
  padding-top: 3rem;
}

.new-ui .ui-notification.system-message .klicky {
  width: 6rem;
  height: auto;
}

.new-ui .ui-notification.system-message.small .klicky {
  width: 4rem;
}

.new-ui .system-page {
  padding: 5rem 0;
}

.new-ui .system-page header {
  text-align: center;
  position: relative;
}

.new-ui .system-page header h1 {
  font-size: 2.625rem;
  line-height: 1.25em;
  color: #5d9bfc;
  font-weight: 500;
}

.new-ui .system-page header .klicky {
  overflow: visible;
}

.new-ui .system-page header .klicky img {
  width: 100%;
  height: auto;
}

.new-ui .system-page .system-message {
  display: block;
  width: 70%;
  margin: 0 auto;
}

.new-ui .system-page .system-message .message {
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.35em;
}

.new-ui .system-page .system-message .button-action.google-logo {
  display: block;
  width: 25rem;
  margin: 0 auto 1.25rem auto;
  padding-left: 5rem;
  text-align: center;
  position: relative;
}

.new-ui .system-page .system-message .button-action.google-logo .google-icon {
  display: block;
  width: 2.5rem;
  height: 2.5rem;
  background-image: url("/build/images/google_chrome.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  position: absolute;
  top: 50%;
  left: 2rem;
  margin-top: -1.25rem;
}

.new-ui .system-page.page-404 header h1 {
  font-size: 6rem;
}

.new-ui .system-page.page-404 header .klicky {
  width: 16rem;
  margin: 0 auto;
}

.new-ui .system-page.page-load header .klicky {
  width: 8rem;
  position: relative;
  margin: 0 auto;
}

.new-ui .system-page.page-load header .klicky img {
  margin-left: -2rem;
}

.new-ui .system-page.page-load header .klicky .ui-spinner {
  position: absolute;
  bottom: -9.5%;
  right: -1%;
  margin-right: 1rem;
}

.new-ui *[class*="ui-tooltip"] {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  display: block;
  padding: 0.25rem 0.75rem 0.325rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-indent: 0;
  color: #758ba9;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 500;
  white-space: nowrap;
  box-shadow: 3px 3px 4px 0 rgba(93, 117, 154, 0.15);
}

.new-ui *[class*="ui-tooltip"]:after {
  display: block;
  font-family: "glyphs";
  color: #FFF;
  position: absolute;
  z-index: 510;
  width: 16px;
  height: 16px;
  font-size: 17px;
  line-height: 16px;
  text-align: center;
}

.new-ui *[class*="ui-tooltip"].top:after {
  text-shadow: 0 1px 0 #c1cfe6, -1px 0 0 #c1cfe6, 2px 0 0 #c1cfe6, 3px 3px 3px rgba(93, 117, 154, 0.15);
  bottom: -13px;
  left: 16px;
  content: "";
}

.new-ui *[class*="ui-tooltip"].bottom:after {
  text-shadow: 1px 0 0 #c1cfe6, -1px 0 0 #c1cfe6;
  top: -11px;
  left: 16px;
  content: "";
}

.new-ui *[class*="ui-tooltip"].left:after {
  text-shadow: 0 1px 0 #c1cfe6, -1px 0 0 #c1cfe6;
  top: 50%;
  left: -13px;
  content: "";
  margin-top: -8px;
}

.new-ui *[class*="ui-tooltip"].right:after {
  text-shadow: 2px 0 0 #c1cfe6, 3px 3px 3px rgba(93, 117, 154, 0.15);
  top: 50%;
  right: -10px;
  content: "";
  margin-top: -8px;
}

.new-ui [ui-tooltip] {
  position: relative;
}

.new-ui [ui-tooltip]:after {
  display: inline-block;
  width: auto;
  height: auto;
  padding: 0.25em 0.5em;
  font-size: 0.75rem;
  line-height: 1em;
  background: #5d759a;
  color: #FFF;
  content: attr(ui-tooltip);
  position: absolute;
  bottom: -1.25rem;
  left: -10000%;
  z-index: 50;
  box-shadow: 2px 2px 3px 0 rgba(48, 69, 101, 0.275);
  white-space: nowrap;
  opacity: 0;
}

.new-ui [ui-tooltip]:hover:after {
  left: 0.25rem;
  opacity: 1;
  transition: opacity 0.15s ease-out;
  transition-delay: 0.75s;
}

.new-ui .list-stats {
  display: inline-block;
  width: auto;
  height: auto;
  vertical-align: top;
  text-align: center;
}

.new-ui .list-stats .stat {
  display: inline-block;
  vertical-align: top;
  width: auto;
  height: auto;
  margin-right: 0.65rem;
}

.new-ui .list-stats .stat .stat-label,
.new-ui .list-stats .stat .stat-title,
.new-ui .list-stats .stat .stat-data,
.new-ui .list-stats .stat .stat-count {
  display: inline-block;
  vertical-align: top;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  line-height: 1rem;
}

.new-ui .list-stats .stat .stat-label,
.new-ui .list-stats .stat .stat-title {
  text-transform: uppercase;
}

.new-ui .list-stats .stat .stat-label[class*="glyph-"], .new-ui .list-stats .stat .stat-label[class*="icon-"],
.new-ui .list-stats .stat .stat-title[class*="glyph-"],
.new-ui .list-stats .stat .stat-title[class*="icon-"] {
  font-size: 0;
}

.new-ui .list-stats .stat .stat-label[class*="glyph-"]:before, .new-ui .list-stats .stat .stat-label[class*="icon-"]:before,
.new-ui .list-stats .stat .stat-title[class*="glyph-"]:before,
.new-ui .list-stats .stat .stat-title[class*="icon-"]:before {
  font-size: 1rem;
}

.new-ui .list-stats .stat .stat-data,
.new-ui .list-stats .stat .stat-count {
  margin-right: 0.125rem;
  font-weight: 500;
}

.new-ui .list-stats .stat:last-child {
  margin-right: 0;
}

.new-ui .list-stats.small .stat .stat-label,
.new-ui .list-stats.small .stat .stat-title {
  font-size: 0.5rem;
  line-height: 1.5em;
  text-indent: -100000px;
  padding-left: 0.925rem;
  position: relative;
}

.new-ui .list-stats.small .stat .stat-label[class*="glyph-"], .new-ui .list-stats.small .stat .stat-label[class*="icon-"],
.new-ui .list-stats.small .stat .stat-title[class*="glyph-"],
.new-ui .list-stats.small .stat .stat-title[class*="icon-"] {
  font-size: 0;
}

.new-ui .list-stats.small .stat .stat-label[class*="glyph-"]:before, .new-ui .list-stats.small .stat .stat-label[class*="icon-"]:before,
.new-ui .list-stats.small .stat .stat-title[class*="glyph-"]:before,
.new-ui .list-stats.small .stat .stat-title[class*="icon-"]:before {
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  font-size: 0.75rem;
  line-height: 1.125em;
  text-indent: 0;
  position: absolute;
  top: 1px;
  left: 0;
}

.new-ui .list-stats.small .stat .stat-data,
.new-ui .list-stats.small .stat .stat-count {
  font-size: 0.75rem;
  line-height: 1.125em;
}

.new-ui .ui-cockpit {
  height: 100%;
  background: #FFF;
}

.new-ui .ui-cockpit .page-header {
  margin-top: 4rem;
}

.new-ui .page-section.ui-flowchart {
  height: calc(100vh - 120px);
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.new-ui .card-group {
  border-radius: 0.25rem;
  background-color: #fafbff;
  border: 1px solid #c1cfe6;
  padding: 20px;
}

.new-ui .split-test .card-group {
  border: none;
}

.new-ui .card-group-connection {
  height: 3rem;
  position: relative;
}

.new-ui .card-group-connection:before {
  display: block;
  content: "";
  width: 4px;
  height: 100%;
  background-color: #d8e1f0;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -2px;
}

.new-ui .ui-flowchart .flowchart-canvas {
  position: relative;
  opacity: 0;
}

.new-ui .ui-flowchart .flowchart-container {
  display: inline-block;
  position: relative;
  width: auto;
  text-align: center;
  white-space: nowrap;
}

.new-ui .ui-flowchart .flowchart-connections {
  position: absolute;
}

.new-ui .ui-flowchart .flowchart-toolbar {
  position: fixed;
  right: 0;
  top: 82px;
  z-index: 1200;
  border: none;
  border-bottom: 1px solid rgba(193, 207, 230, 0.75);
}

.new-ui .ui-flowchart .flowchart-toolbar .toolbar-head {
  height: 4rem;
  text-align: left;
  position: absolute;
  left: 1rem;
  top: 0;
}

.new-ui .ui-flowchart .flowchart-toolbar .flowchart-settings {
  display: block;
  width: 30px;
  height: 30px;
  position: absolute;
  top: 50%;
  left: 0;
  margin-top: -15px;
}

.new-ui .ui-flowchart .flowchart-toolbar .flowchart-settings:before {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 100%;
  height: 100%;
  font-size: 30px;
  line-height: 30px;
  text-align: center;
  color: #5d9bfc;
}

.new-ui .ui-flowchart .flowchart-toolbar .flowchart-title {
  display: inline-block;
  width: auto;
  height: 4rem;
  font-size: 1.15rem;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  line-height: 4rem;
  color: black;
  font-weight: 400;
  margin-left: 2.75rem;
  position: relative;
}

.new-ui .ui-flowchart .flowchart-toolbar .flowchart-title .flowchart-status {
  -moz-transform: translateY(-12px);
  -o-transform: translateY(-12px);
  -ms-transform: translateY(-12px);
  -webkit-transform: translateY(-12px);
  transform: translateY(-12px);
}

.new-ui .ui-flowchart .flowchart-toolbar .add-action {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  position: absolute;
  top: 50%;
  right: 0.75rem;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group {
  height: 4rem;
  position: relative;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li {
  cursor: pointer;
  vertical-align: top;
  margin-right: 0.5rem;
  line-height: 4rem;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li,
.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li a {
  height: 4rem;
  line-height: 4rem;
  color: #758ba9;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li:before,
.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li a:before {
  height: 4rem;
  line-height: 4rem;
  font-size: 1.5rem;
  color: #c1cfe6;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li a {
  display: block;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li [class*="button-"] {
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li:last-child {
  margin-right: 0;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li:hover:before,
.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li a:hover:before {
  color: #5d9bfc;
  transition: color 0.25s ease-out;
}

.new-ui .ui-flowchart .flowchart-toolbar .tool-group > li .flowchart-status {
  height: 1.75em;
  line-height: 1.685em;
  color: #FFF;
  margin-right: 0.5rem;
}

.new-ui .ui-flowchart .ui-card {
  display: block;
  width: 18rem;
  border-bottom-color: transparent;
  box-shadow: 3px 0 0 0 #FFF, -3px 0 0 0 #FFF;
}

.new-ui .ui-flowchart .ui-card.condition-head {
  padding-bottom: 1.5rem;
}

.new-ui .ui-flowchart .ui-card.start {
  padding-top: 1.375rem;
}

.new-ui .ui-flowchart .ui-card.start .card-action-icon {
  width: 2.5rem;
  height: 2.5rem;
  margin: 0;
  position: absolute;
  top: -2.875rem;
  left: 50%;
  margin-left: -1.25rem;
  border-radius: 2.5rem;
  background-color: #5d9bfc;
  z-index: 50;
}

.new-ui .ui-flowchart .ui-card.start .card-action-icon:before {
  display: inline-block;
  font-size: 1.625rem;
  line-height: 2.575rem;
  text-indent: 0.25rem;
  color: #FFF;
}

.new-ui .ui-flowchart .ui-card.start:hover .card-action-icon:before {
  color: #FFF;
}

.new-ui .ui-flowchart .ui-card.add-card {
  border: 1px dashed #c1cfe6;
  border-top: 1px dashed #c1cfe6 !important;
  margin-left: -1px;
}

.new-ui .ui-flowchart .ui-card.add-card button {
  font-family: "Source Sans Pro", "Source Sans Pro", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
}

.new-ui .ui-flowchart .ui-card.goto, .new-ui .ui-flowchart .ui-card.stop, .new-ui .ui-flowchart .ui-card.last-card, .new-ui .ui-flowchart .ui-card.connection-ghost {
  border-bottom: 1px solid #c1cfe6;
}

.new-ui .ui-flowchart .ui-card.connection-ghost {
  border-top: 1px solid #c1cfe6 !important;
}

.new-ui .ui-flowchart .ui-card.is-active {
  -moz-transform: scale(1.05);
  -o-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -webkit-transform: scale(1.05);
  transform: scale(1.05);
  background-color: #80b2ff;
  border-color: #5d9bfc;
  border-radius: 0.1875rem;
  z-index: 100;
}

.new-ui .ui-flowchart .ui-card.is-active:before {
  display: block;
  content: "";
  width: 95%;
  height: 4px;
  background-color: rgba(193, 207, 230, 0.325);
  position: absolute;
  left: 2.5%;
  bottom: -5px;
  z-index: 20;
}

.new-ui .ui-flowchart .ui-card.is-active .card-preview,
.new-ui .ui-flowchart .ui-card.is-active .card-quick-menu {
  display: none;
}

.new-ui .ui-flowchart .ui-card.is-active .card-quick-edit {
  display: block;
}

.new-ui .ui-flowchart .ui-card.is-active .card-tools .card-add,
.new-ui .ui-flowchart .ui-card.is-active .card-tools .card-add-below {
  display: block;
  opacity: 1;
  left: 0;
}

.new-ui .ui-flowchart .ui-card.is-active .card-number {
  color: #3378e3;
}

.new-ui .ui-flowchart .ui-card.connection-ghost {
  margin-top: 2rem;
  border-top: 1px solid #c1cfe6;
}

.new-ui .ui-flowchart .ui-card.connection-ghost:before {
  display: block;
  content: "";
  width: 3px;
  height: 2rem;
  background-color: #c1cfe6;
  position: absolute;
  left: 50%;
  top: -2rem;
  margin-left: -1px;
}

.new-ui .ui-flowchart .ui-card.connection-ghost .card-delete {
  display: none;
}

.new-ui .ui-flowchart .ui-card[class*="card-bg-"].start .card-action-icon, .new-ui .ui-flowchart .ui-card[class*="card-bg-"].start:hover .card-action-icon {
  background-color: #5d9bfc;
}

.new-ui .ui-flowchart .ui-card[class*="card-bg-"].start .card-action-icon:before, .new-ui .ui-flowchart .ui-card[class*="card-bg-"].start:hover .card-action-icon:before {
  color: #FFF;
}

.new-ui .ui-flowchart .ui-card.not-allowed {
  overflow: hidden;
}

.new-ui .ui-flowchart .ui-card.not-allowed:before {
  font-family: "glyphs";
  display: block;
  content: "";
  width: calc(100% - 4px);
  height: 4rem;
  font-size: 2.65rem;
  line-height: 4rem;
  color: #e26464;
  background-color: rgba(255, 255, 255, 0.825);
  position: absolute;
  top: 50%;
  left: 2px;
  margin-top: -2rem;
  z-index: 45;
  box-shadow: 0 -4rem 0 0 rgba(255, 255, 255, 0.825), 0 4rem 0 0 rgba(255, 255, 255, 0.825);
}

.new-ui .ui-flowchart .ui-card-add {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: block;
  width: 18rem;
  height: 0;
  margin: 0 auto;
  position: relative;
  cursor: pointer;
  white-space: normal;
  z-index: 50;
}

.new-ui .ui-flowchart .ui-card-add .marker {
  display: none;
  width: 24px;
  height: 24px;
  border-radius: 0.25rem;
  background-color: #5d9bfc;
  position: absolute;
  top: 50%;
  margin-top: -12px;
  left: -30px;
}

.new-ui .ui-flowchart .ui-card-add .marker:before, .new-ui .ui-flowchart .ui-card-add .marker:after {
  font-family: "glyphs";
  display: block;
  line-height: 24px;
  position: absolute;
  height: 24px;
  top: 0;
}

.new-ui .ui-flowchart .ui-card-add .marker:before {
  content: "";
  width: 10px;
  font-size: 21px;
  line-height: 27px;
  color: #5d9bfc;
  right: 0;
  text-align: left;
}

.new-ui .ui-flowchart .ui-card-add .marker:after {
  content: "";
  width: 24px;
  font-size: 16px;
  line-height: 24px;
  color: #FFF;
  text-align: center;
  left: 0;
}

.new-ui .ui-flowchart .ui-card-add:before {
  display: block;
  content: "";
  width: 100%;
  height: 5px;
  border-bottom: 2px dashed #5d9bfc;
  border-left: 1px solid #c1cfe6;
  border-right: 1px solid #c1cfe6;
  background-color: #FFF;
  position: absolute;
  top: -3px;
  left: 0;
  z-index: 30;
  opacity: 0;
}

.new-ui .ui-flowchart .ui-card-add:after {
  content: "";
  display: block;
  width: 18rem;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 0;
  margin-top: -10px;
  z-index: 20;
  opacity: 0;
}

.new-ui .ui-flowchart .ui-card-add:hover:before, .new-ui .ui-flowchart .ui-card-add:hover:after {
  opacity: 1;
  transition: opacity 0.15s ease-out;
  transition-delay: 0.15s;
}

.new-ui .ui-flowchart .ui-card-add:hover .marker {
  -webkit-animation: bounce-right 2s 1;
  -moz-animation: bounce-right 2s 1;
  -o-animation: bounce-right 2s 1;
  animation: bounce-right 2s 1;
  display: block;
}

.new-ui .ui-flowchart .condition-group {
  display: inline-block;
  font-size: 0;
  width: auto;
  height: auto;
  position: relative;
}

.new-ui .ui-flowchart .condition-group > .condition-connection,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection {
  position: relative;
}

.new-ui .ui-flowchart .condition-group > .condition-connection:before, .new-ui .ui-flowchart .condition-group > .condition-connection:after,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection:before,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection:after {
  display: none;
  width: 16px;
  height: 12px;
  font-family: "glyphs";
  content: "";
  text-align: center;
  color: #d3e0f5;
  font-size: 24px;
  position: absolute;
  bottom: 2px;
}

.new-ui .ui-flowchart .condition-group > .condition-connection:before,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection:before {
  left: -12.75px;
}

.new-ui .ui-flowchart .condition-group > .condition-connection:after,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection:after {
  right: -4.75px;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .expand-group,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group {
  width: 2rem;
  height: 1rem;
  display: block;
  position: absolute;
  left: 50%;
  top: 0.5rem;
  margin-left: -1rem;
  z-index: 50;
  cursor: pointer;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .expand-group span,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group span {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .expand-group span:before, .new-ui .ui-flowchart .condition-group > .condition-connection .expand-group span:after,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group span:before,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group span:after {
  display: block;
  font-family: "glyphs";
  width: 1rem;
  height: 1rem;
  font-size: 1rem;
  line-height: 1rem;
  text-align: center;
  color: #c1cfe6;
  position: absolute;
  top: 0;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .expand-group span:before,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group span:before {
  content: "";
  left: 0;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .expand-group span:after,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group span:after {
  content: "";
  right: 0;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .expand-group:hover span:before,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group:hover span:before {
  left: -0.25rem;
  transition: left 0.15s ease-out;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .expand-group:hover span:after,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .expand-group:hover span:after {
  right: -0.25rem;
  transition: right 0.15s ease-out;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .close-group,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .close-group {
  display: none;
  width: 22px;
  height: 22px;
  border-radius: 3px;
  background-color: #FFF;
  box-shadow: inset 0 0 0 2px #d3e0f5;
  position: absolute;
  top: -13px;
  left: 50%;
  margin-left: -11px;
  font-size: 0;
  cursor: pointer;
  z-index: 15;
}

.new-ui .ui-flowchart .condition-group > .condition-connection .close-group:before,
.new-ui .ui-flowchart .condition-group > .group-loop > .condition-connection .close-group:before {
  display: block;
  content: "";
  width: 22px;
  height: 22px;
  line-height: 22px;
  font-family: "glyphs";
  color: #d3e0f5;
  font-size: 14px;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
}

.new-ui .ui-flowchart .condition-group > .group-yes,
.new-ui .ui-flowchart .condition-group > .group-no {
  display: inline-block;
  width: 0;
  height: auto;
  font-size: 0;
  vertical-align: top;
  margin-top: -1px;
  padding-top: 2rem;
  position: relative;
  white-space: nowrap;
}

.new-ui .ui-flowchart .condition-group > .group-yes > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .card-entity > .dropzone > .add-card,
.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .condition-group,
.new-ui .ui-flowchart .condition-group > .group-yes > .dropzone > .ui-card.add-card,
.new-ui .ui-flowchart .condition-group > .group-no > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .card-entity > .dropzone > .add-card,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .condition-group,
.new-ui .ui-flowchart .condition-group > .group-no > .dropzone > .ui-card.add-card {
  display: none;
  opacity: 0;
}

.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .card-entity > .ui-card-add,
.new-ui .ui-flowchart .condition-group > .group-yes > .card-entity > .ui-card-add,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .card-entity > .ui-card-add,
.new-ui .ui-flowchart .condition-group > .group-no > .card-entity > .ui-card-add {
  display: none;
}

.new-ui .ui-flowchart .condition-group > .group-yes:before,
.new-ui .ui-flowchart .condition-group > .group-no:before {
  display: block;
  width: 9rem;
  height: 2rem;
  border-top: 1px solid #c1cfe6;
  border-bottom: 1px solid #c1cfe6;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-size: 0.625rem;
  line-height: 2rem;
  font-weight: 600;
  text-transform: uppercase;
  color: #5d9bfc;
  background-color: #f0f5ff;
  text-align: center;
  position: absolute;
  top: 0;
  z-index: 15;
  cursor: pointer;
}

.new-ui .ui-flowchart .condition-group > .group-yes:after,
.new-ui .ui-flowchart .condition-group > .group-no:after {
  display: none;
  content: "";
  width: 18rem;
  height: 1px;
  position: absolute;
  left: 0;
  top: 2rem;
  background-color: #c1cfe6;
  margin-top: -1px;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-close .condition-group, .new-ui .ui-flowchart .condition-group > .group-yes.is-closed .condition-group,
.new-ui .ui-flowchart .condition-group > .group-no.is-close .condition-group,
.new-ui .ui-flowchart .condition-group > .group-no.is-closed .condition-group {
  display: none;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-close:before, .new-ui .ui-flowchart .condition-group > .group-yes.is-closed:before,
.new-ui .ui-flowchart .condition-group > .group-no.is-close:before,
.new-ui .ui-flowchart .condition-group > .group-no.is-closed:before {
  border-bottom: none;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active,
.new-ui .ui-flowchart .condition-group > .group-no.is-active {
  width: 18rem;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active:after,
.new-ui .ui-flowchart .condition-group > .group-no.is-active:after {
  display: block;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .card-entity > .dropzone > .add-card,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .dropzone > .ui-card.add-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .card-entity > .dropzone > .add-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .dropzone > .ui-card.add-card {
  display: block;
  opacity: 1;
  transition: opacity 0.15s ease-out;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .card-entity > .ui-card-add,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .card-entity > .ui-card-add,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .card-entity > .ui-card-add,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .card-entity > .ui-card-add {
  display: block;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .condition-group,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .condition-group,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .condition-group,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .condition-group {
  display: inline-block;
  opacity: 1;
  transition: opacity 0.15s ease-out;
  margin-top: -1px;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .condition-group.is-open:first-child > .card-entity > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .condition-group.is-open:first-child > .card-entity > .ui-card {
  border-top-color: transparent;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active:before,
.new-ui .ui-flowchart .condition-group > .group-no.is-active:before {
  background-color: #FFF;
  border: 1px solid #c1cfe6;
  border-bottom: none;
  box-shadow: inset 0 -1px 0 0 #FFF, 0 1px 0 -1px #FFF;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-incomplete:after,
.new-ui .ui-flowchart .condition-group > .group-no.is-incomplete:after {
  display: none;
}

.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .card-entity > .ui-card-add .marker,
.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .condition-group > .card-entity > .ui-card-add .marker {
  left: -30px;
}

.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .card-entity > .ui-card-add:hover .marker,
.new-ui .ui-flowchart .condition-group > .group-yes > .group-loop > .condition-group > .card-entity > .ui-card-add:hover .marker {
  -webkit-animation: bounce-right 2s 1;
  -moz-animation: bounce-right 2s 1;
  -o-animation: bounce-right 2s 1;
  animation: bounce-right 2s 1;
}

.new-ui .ui-flowchart .condition-group > .group-yes:before {
  content: attr(data-translation);
  border-left: 1px solid #c1cfe6;
  left: 0;
}

.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group > .group-yes.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head {
  margin-right: 0;
}

.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .card-entity > .ui-card-add .marker,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .condition-group > .card-entity > .ui-card-add .marker {
  right: -30px;
  left: auto;
}

.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .card-entity > .ui-card-add .marker:before,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .condition-group > .card-entity > .ui-card-add .marker:before {
  content: "";
  right: auto;
  left: -12px;
  text-align: right;
}

.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .card-entity > .ui-card-add:hover .marker,
.new-ui .ui-flowchart .condition-group > .group-no > .group-loop > .condition-group > .card-entity > .ui-card-add:hover .marker {
  -webkit-animation: bounce-left 2s 1;
  -moz-animation: bounce-left 2s 1;
  -o-animation: bounce-left 2s 1;
  animation: bounce-left 2s 1;
}

.new-ui .ui-flowchart .condition-group > .group-no:before {
  content: attr(data-translation);
  border-right: 1px solid #c1cfe6;
  right: 0;
}

.new-ui .ui-flowchart .condition-group > .group-no.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group > .group-no.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head {
  margin-left: 0;
}

.new-ui .ui-flowchart .condition-group > .group-no.is-active:before {
  right: 0;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open {
  float: right;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open > .card-entity > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open > .card-entity > .ui-card-add.decision, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open > .card-entity > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open > .card-entity > .ui-card-add.decision {
  margin-right: 0;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .condition-group.is-open > .condition-connection > .close-group,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .condition-group.is-open > .condition-connection > .close-group, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .condition-group.is-open > .condition-connection > .close-group,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .condition-group.is-open > .condition-connection > .close-group {
  left: auto;
  right: 5rem;
  margin-left: 0;
  margin-right: -5px;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open, .new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open {
  float: left;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open > .card-entity > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open > .card-entity > .ui-card-add.decision, .new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open > .card-entity > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open > .card-entity > .ui-card-add.decision {
  margin-left: 0;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .condition-group.is-open > .condition-connection .close-group,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .condition-group.is-open > .condition-connection .close-group, .new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .condition-group.is-open > .condition-connection .close-group,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .condition-group.is-open > .condition-connection .close-group {
  left: 5rem;
  margin-left: -2px;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes > .dropzone.is-active, .new-ui .ui-flowchart .condition-group.is-close > .group-yes > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes > .group-loop > .card-entity > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .group-loop > .card-entity > .dropzone.active, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .dropzone.is-active, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .group-loop > .card-entity > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .group-loop > .card-entity > .dropzone.active {
  display: none;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes > .dropzone.is-active:after, .new-ui .ui-flowchart .condition-group.is-close > .group-yes > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes > .group-loop > .card-entity > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no > .group-loop > .card-entity > .dropzone.active:after, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .dropzone.is-active:after, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes > .group-loop > .card-entity > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no > .group-loop > .card-entity > .dropzone.active:after {
  display: none;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .dropzone.is-active, .new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity > .dropzone.active, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .dropzone.is-active, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity > .dropzone.active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity > .dropzone.is-active,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity > .dropzone.active {
  display: block;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .dropzone.is-active:after, .new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity > .dropzone.active:after, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .dropzone.is-active:after, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity > .dropzone.active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity > .dropzone.is-active:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity > .dropzone.active:after {
  display: block;
}

.new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add:before, .new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add.decision:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:before, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add:before, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add.decision:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:before {
  display: block;
  content: "";
  width: 100%;
  height: 1px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 30;
  opacity: 0;
}

.new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add:after, .new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add.decision:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:after, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add:after, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add.decision:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:after {
  height: 6px;
  top: 0;
  left: 0;
  margin-top: 0;
}

.new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add:hover:before, .new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add:hover:after, .new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add.decision:hover:before, .new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add.decision:hover:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:after,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:before,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:after, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add:hover:before, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add:hover:after, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add.decision:hover:before, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add.decision:hover:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover:after,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:before,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover:after {
  opacity: 1;
  transition: opacity 0.25s ease-out;
  transition-delay: 0.275s;
}

.new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add:hover .marker, .new-ui .ui-flowchart .condition-group.is-close > .card-entity:first-child > .ui-card-add.decision:hover .marker,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover .marker,
.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover .marker,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover .marker,
.new-ui .ui-flowchart .condition-group.is-close > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover .marker, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add:hover .marker, .new-ui .ui-flowchart .condition-group.is-closed > .card-entity:first-child > .ui-card-add.decision:hover .marker,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover .marker,
.new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover .marker,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add:hover .marker,
.new-ui .ui-flowchart .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity:first-child > .ui-card-add.decision:hover .marker {
  opacity: 1;
  transition: opacity 0.15s ease-out;
  transition-delay: 0.1s;
}

.new-ui .ui-flowchart .condition-group.is-close > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:before, .new-ui .ui-flowchart .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity:first-child > .ui-card-add:before {
  top: -1px;
}

.new-ui .ui-flowchart .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .card-entity > .condition-head {
  margin-bottom: 2rem;
}

.new-ui .ui-flowchart .condition-group.is-open > .condition-head:before,
.new-ui .ui-flowchart .condition-group.is-open > .group-loop > .condition-head:before,
.new-ui .ui-flowchart .condition-group.is-open > .card-entity > .condition-head:before {
  -moz-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  display: inline-block;
  content: "";
  width: 0;
  height: 2rem;
  position: absolute;
  border-left: 2px solid #d3e0f5;
  left: 50%;
  bottom: -2rem;
}

.new-ui .ui-flowchart .condition-group.is-open > .card-entity:first-child .ui-card {
  border-bottom: 1px solid #c1cfe6;
}

.new-ui .ui-flowchart .condition-group.is-open > .condition-connection,
.new-ui .ui-flowchart .condition-group.is-open > .group-loop > .condition-connection {
  width: calc(100% - 7rem);
  height: 2rem;
  border: 2px solid #d3e0f5;
  border-bottom: none;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  margin-top: -1px;
  margin-left: 3.5rem;
}

.new-ui .ui-flowchart .condition-group.is-open > .condition-connection .close-group, .new-ui .ui-flowchart .condition-group.is-open > .condition-connection:before, .new-ui .ui-flowchart .condition-group.is-open > .condition-connection:after,
.new-ui .ui-flowchart .condition-group.is-open > .group-loop > .condition-connection .close-group,
.new-ui .ui-flowchart .condition-group.is-open > .group-loop > .condition-connection:before,
.new-ui .ui-flowchart .condition-group.is-open > .group-loop > .condition-connection:after {
  display: block;
}

.new-ui .ui-flowchart .condition-group.is-open > .condition-connection .expand-group,
.new-ui .ui-flowchart .condition-group.is-open > .group-loop > .condition-connection .expand-group {
  display: none;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-yes,
.new-ui .ui-flowchart .condition-group.is-open > .group-no {
  background-color: transparent;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-yes:before,
.new-ui .ui-flowchart .condition-group.is-open > .group-no:before {
  width: 40%;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-yes {
  margin-right: 2rem;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-no {
  margin-left: 2rem;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-no:before {
  right: 0;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open {
  float: right;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open > .card-entity > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open > .card-entity > .ui-card-add.decision {
  margin-right: 0;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .condition-group.is-open > .condition-connection .close-group,
.new-ui .ui-flowchart .condition-group.is-open > .group-yes.is-active > .group-loop > .condition-group.is-open > .condition-connection .close-group {
  left: auto;
  right: 5rem;
  margin-left: 0;
  margin-right: -5px;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open {
  float: left;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open > .card-entity > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open > .group-loop > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open > .card-entity > .condition-head,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open > .group-loop > .ui-card-add.decision,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open > .card-entity > .ui-card-add.decision {
  margin-left: 0;
}

.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .condition-group.is-open > .condition-connection .close-group,
.new-ui .ui-flowchart .condition-group.is-open > .group-no.is-active > .group-loop > .condition-group.is-open > .condition-connection .close-group {
  left: 5rem;
  margin-left: -4.5px;
}

.new-ui .ui-flowchart .notification {
  display: inline-block;
  font-style: italic;
  font-size: 1rem;
  color: #c1cfe6;
}

.new-ui .ui-flowchart .notification:before {
  display: inline-block;
  font-family: "glyphs";
  content: "";
  font-size: 1rem;
  margin-right: 0.25em;
  font-style: normal;
  color: #c1cfe6;
  line-height: 1.5rem;
}

.new-ui .ui-flowchart.automation-active .flowchart-status {
  display: inline-block;
}

.new-ui .ui-flowchart.is-panning {
  cursor: move !important;
}

.new-ui .ui-rulebuilder {
  width: 75%;
  margin: 0 auto;
}

.new-ui .ui-rulebuilder .ui-card.rule:not(.feature) {
  border-bottom: none;
}

.new-ui .ui-rulebuilder .ui-card.rule:last-child {
  border-bottom: 1px solid #c1cfe6;
}

.new-ui .ui-rulebuilder .ui-card.add-card button {
  font-size: 1.15rem;
}

.new-ui .ui-rulebuilder .rule-section {
  clear: both;
  position: relative;
}

.new-ui .ui-rulebuilder .rule-section:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .ui-rulebuilder .rule-section .rule-progression,
.new-ui .ui-rulebuilder .rule-section .card-group {
  float: left;
}

.new-ui .ui-rulebuilder .rule-section .rule-progression {
  width: 7rem;
  height: 5rem;
}

.new-ui .ui-rulebuilder .rule-section .rule-progression .ui-badge-circle {
  margin-top: 2.4rem;
}

.new-ui .ui-rulebuilder .rule-section .rule-progression .ui-badge-circle.completed {
  box-shadow: inset 0 0 0 3px #5d9bfc;
  color: #5d9bfc;
}

.new-ui .ui-rulebuilder .rule-section .card-group {
  width: calc(100% - 7rem);
}

.new-ui .ui-rulebuilder .card-group-connection {
  clear: both;
  width: calc(100% - 7rem);
  float: right;
}

.new-ui .ui-rulebuilder .card-group-connection:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .dropzone {
  width: 18rem;
  height: 0;
  margin: 0 auto;
  border-left: 1px dashed #d8e1f0;
  border-right: 1px dashed #d8e1f0;
  background: rgba(240, 245, 255, 0.65);
  position: relative;
  /* Note: I removed the transition due to flickering when entering the dropzone from below */
}

.new-ui .dropzone p {
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: inline-block;
  width: auto;
  height: auto;
  font-size: 0.875rem;
  font-style: italic;
  color: #758ba9;
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 0;
  pointer-events: none;
}

.new-ui .dropzone.is-active, .new-ui .dropzone.active {
  border-top: 1px solid #c1cfe6;
}

.new-ui .dropzone.is-active:after, .new-ui .dropzone.active:after {
  font-family: "glyphs";
  display: block;
  content: "";
  width: 1.25rem;
  height: 1.25rem;
  font-size: 1rem;
  line-height: 1.25rem;
  border-radius: 12px;
  color: #5d9bfc;
  background-color: #FFF;
  box-shadow: 0 0 12px 1px rgba(255, 255, 255, 0.65);
  position: absolute;
  top: -0.675rem;
  left: 50%;
  margin-left: -0.75rem;
  z-index: 40;
}

.new-ui .dropzone.is-active:before, .new-ui .dropzone.active:before {
  display: block;
  content: "";
  width: 100%;
  height: 4rem;
  position: absolute;
  top: -2rem;
  left: 0;
  z-index: 50;
}

.new-ui .dropzone.dropable {
  height: 3.5rem;
  user-select: none;
  cursor: pointer !important;
}

.new-ui .dropzone.dropable p {
  opacity: 1;
}

.new-ui .dropzone.dropable.active:after {
  display: none;
}

.new-ui .dropzone.dropable .ui-card.add-card {
  background: rgba(240, 245, 255, 0.65);
}

.new-ui .dropzone.dropable .ui-card.add-card button.button-text {
  opacity: 1;
}

.new-ui .dropzone-goto {
  border-color: #5d9bfc !important;
  box-shadow: inset 0 0 0 4px #FFF, 0 0 0 2px #5d9bfc !important;
  z-index: 50;
}

.new-ui .dropzone-goto.ui-card:hover, .new-ui .dropzone-goto.ui-card.dropable {
  box-shadow: inset 0 0 0 50rem #5d759a !important;
}

.new-ui .dropzone-goto.ui-card:hover .card-title,
.new-ui .dropzone-goto.ui-card:hover .card-number,
.new-ui .dropzone-goto.ui-card:hover .card-stats, .new-ui .dropzone-goto.ui-card.dropable .card-title,
.new-ui .dropzone-goto.ui-card.dropable .card-number,
.new-ui .dropzone-goto.ui-card.dropable .card-stats {
  color: #FFF;
}

.new-ui .dropzone-goto.ui-card:hover .card-action-icon,
.new-ui .dropzone-goto.ui-card:hover .card-delete, .new-ui .dropzone-goto.ui-card.dropable .card-action-icon,
.new-ui .dropzone-goto.ui-card.dropable .card-delete {
  background-color: none;
}

.new-ui .dropzone-goto.ui-card:hover .card-action-icon:before,
.new-ui .dropzone-goto.ui-card:hover .card-delete:before, .new-ui .dropzone-goto.ui-card.dropable .card-action-icon:before,
.new-ui .dropzone-goto.ui-card.dropable .card-delete:before {
  color: #FFF;
}

.new-ui .dropzone-goto.ui-card:hover .card-action-icon:hover,
.new-ui .dropzone-goto.ui-card:hover .card-delete:hover, .new-ui .dropzone-goto.ui-card.dropable .card-action-icon:hover,
.new-ui .dropzone-goto.ui-card.dropable .card-delete:hover {
  background-color: transparent;
}

.new-ui .dropzone-goto.ui-card:hover .card-action-icon:hover:before,
.new-ui .dropzone-goto.ui-card:hover .card-delete:hover:before, .new-ui .dropzone-goto.ui-card.dropable .card-action-icon:hover:before,
.new-ui .dropzone-goto.ui-card.dropable .card-delete:hover:before {
  color: #FFF;
}

.new-ui .condition-group.is-closed > .card-entity > .condition-head.dropzone-goto {
  box-shadow: inset 0 -1px 0 0 #5d9bfc, inset 0 0 0 4px #FFF, 0 0 0 1px #5d9bfc !important;
}

.new-ui .condition-group.is-closed > .group-yes.is-active > .group-loop > .card-entity > .ui-card.dropzone-goto,
.new-ui .condition-group.is-closed > .group-no.is-active > .group-loop > .card-entity > .ui-card.dropzone-goto {
  box-shadow: inset 0 1px 0 0 #5d9bfc, inset 0 0 0 4px #FFF, 0 0 0 1px #5d9bfc !important;
}

.new-ui .card-ghost {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  box-shadow: 3px 3px 6px 0 rgba(93, 117, 154, 0.15);
  position: fixed;
  left: -100000px;
  top: -100000px;
  cursor: all-scroll !important;
  border-bottom: 1px solid #c1cfe6;
}

.new-ui #main-header .site-brand,
.new-ui #main-header .site-name {
  display: inline-block;
  width: auto;
  height: 4rem;
  line-height: 4rem;
  vertical-align: top;
}

.new-ui #main-header .site-name {
  color: #FFF;
  color: #686cbe;
  text-transform: uppercase;
  font-size: 1rem;
  font-weight: 600;
}

.new-ui #main-header .main-menu {
  vertical-align: top;
}

.new-ui #main-header .ui-playground-button {
  position: absolute;
  top: 0.85rem;
  right: 0.85rem;
}

.new-ui #main-header .ui-playground-button:before {
  font-size: 1.35em;
  line-height: 0.925em;
}

.new-ui .grid-1-5 {
  width: 20%;
}

.new-ui .grid-2-5 {
  width: 40%;
}

.new-ui .grid-3-5 {
  width: 60%;
}

.new-ui .grid-4-5 {
  width: 80%;
}

.new-ui .grid-5-5 {
  width: 100%;
}

.new-ui .grid-1-12 {
  width: 8.333333%;
}

.new-ui .grid-2-12 {
  width: 16.666666%;
}

.new-ui .grid-3-12 {
  width: 25%;
}

.new-ui .grid-4-12 {
  width: 33.333333%;
}

.new-ui .grid-5-12 {
  width: 41.666666%;
}

.new-ui .grid-6-12 {
  width: 50%;
}

.new-ui .grid-7-12 {
  width: 58.333333%;
}

.new-ui .grid-8-12 {
  width: 66.666666%;
}

.new-ui .grid-9-12 {
  width: 75%;
}

.new-ui .grid-10-12 {
  width: 83.333333%;
}

.new-ui .grid-11-12 {
  width: 91.666666%;
}

.new-ui .grid-12-12 {
  width: 100%;
}

.new-ui .grid-1-24 {
  width: 4.166666%;
}

.new-ui .grid-2-24 {
  width: 8.333333%;
}

.new-ui .grid-3-24 {
  width: 12.5%;
}

.new-ui .grid-4-24 {
  width: 16.666666%;
}

.new-ui .grid-5-24 {
  width: 20.833333%;
}

.new-ui .grid-6-24 {
  width: 25%;
}

.new-ui .grid-7-24 {
  width: 29.166666%;
}

.new-ui .grid-8-24 {
  width: 33.333333%;
}

.new-ui .grid-9-24 {
  width: 37.5%;
}

.new-ui .grid-10-24 {
  width: 41.666666%;
}

.new-ui .grid-11-24 {
  width: 45.833333%;
}

.new-ui .grid-12-24 {
  width: 50%;
}

.new-ui .grid-13-24 {
  width: 54.166666%;
}

.new-ui .grid-14-24 {
  width: 58.333333%;
}

.new-ui .grid-15-24 {
  width: 62.5%;
}

.new-ui .grid-16-24 {
  width: 66.666666%;
}

.new-ui .grid-17-24 {
  width: 70.833333%;
}

.new-ui .grid-18-24 {
  width: 75%;
}

.new-ui .grid-19-24 {
  width: 79.166666%;
}

.new-ui .grid-20-24 {
  width: 83.333333%;
}

.new-ui .grid-21-24 {
  width: 87.5%;
}

.new-ui .grid-22-24 {
  width: 91.666666%;
}

.new-ui .grid-23-24 {
  width: 95.833333%;
}

.new-ui .grid-24-24 {
  width: 100%;
}

.new-ui .grid,
.new-ui *[class*="columns-"] {
  display: block;
  position: relative;
  box-sizing: border-box;
  clear: both;
}

.new-ui .grid:after,
.new-ui *[class*="columns-"]:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .grid *,
.new-ui *[class*="columns-"] * {
  box-sizing: inherit;
}

.new-ui .grid *[class*="grid-"],
.new-ui .grid .column,
.new-ui *[class*="columns-"] *[class*="grid-"],
.new-ui *[class*="columns-"] .column {
  display: block;
  position: relative;
  float: left;
}

.new-ui .grid *[class*="grid-"].padding-right-gutter-small {
  padding-right: 1rem !important;
}

.new-ui .grid *[class*="grid-"].padding-left-gutter-small {
  padding-left: 1rem !important;
}

.new-ui .grid *[class*="grid-"].padding-right-gutter-medium {
  padding-right: 2rem !important;
}

.new-ui .grid *[class*="grid-"].padding-left-gutter-medium {
  padding-left: 2rem !important;
}

.new-ui .grid *[class*="grid-"].padding-right-gutter-large {
  padding-right: 3rem !important;
}

.new-ui .grid *[class*="grid-"].padding-left-gutter-large {
  padding-left: 3rem !important;
}

.new-ui .grid *[class*="grid-"].padding-right-gutter-xlarge {
  padding-right: 4rem !important;
}

.new-ui .grid *[class*="grid-"].padding-left-gutter-xlarge {
  padding-left: 4rem !important;
}

.new-ui .grid *[class*="grid-"].padding-right {
  padding-right: 1.5rem !important;
}

.new-ui .grid *[class*="grid-"].padding-left {
  padding-left: 1.5rem !important;
}

@media (max-width: 1280px) {
  .new-ui .grid.responsive *[class*="grid-"] {
    width: 20%;
  }
}

@media (max-width: 1024px) {
  .new-ui .grid.responsive *[class*="grid-"] {
    width: 25%;
  }
}

@media (max-width: 768px) {
  .new-ui .grid.responsive *[class*="grid-"] {
    width: 33.333333%;
  }
}

@media (max-width: 560px) {
  .new-ui .grid.responsive *[class*="grid-"] {
    width: 50%;
  }
}

@media (max-width: 480px) {
  .new-ui .grid.responsive *[class*="grid-"] {
    width: 100% !important;
    float: none !important;
  }
}

.new-ui .columns-2.gutter-small .column {
  width: calc(50% - 0.5rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-2.gutter-small .column:nth-child(2n + 2) {
  margin-right: 0;
}

.new-ui .columns-2.gutter-medium .column {
  width: calc(50% - 1rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-2.gutter-medium .column:nth-child(2n + 2) {
  margin-right: 0;
}

.new-ui .columns-2.gutter-large .column {
  width: calc(50% - 1.5rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-2.gutter-large .column:nth-child(2n + 2) {
  margin-right: 0;
}

.new-ui .columns-2.gutter-xlarge .column {
  width: calc(50% - 2rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-2.gutter-xlarge .column:nth-child(2n + 2) {
  margin-right: 0;
}

.new-ui .columns-2 .column {
  width: calc(50% - 0.75rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-2 .column:nth-child(2n + 2) {
  margin-right: 0;
}

.new-ui .columns-3.gutter-small .column {
  width: calc(33.333333% - 0.666666667rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-3.gutter-small .column:nth-child(3n + 3) {
  margin-right: 0;
}

.new-ui .columns-3.gutter-medium .column {
  width: calc(33.333333% - 1.333333333rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-3.gutter-medium .column:nth-child(3n + 3) {
  margin-right: 0;
}

.new-ui .columns-3.gutter-large .column {
  width: calc(33.333333% - 2rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-3.gutter-large .column:nth-child(3n + 3) {
  margin-right: 0;
}

.new-ui .columns-3.gutter-xlarge .column {
  width: calc(33.333333% - 2.666666667rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-3.gutter-xlarge .column:nth-child(3n + 3) {
  margin-right: 0;
}

.new-ui .columns-3 .column {
  width: calc(33.333333% - 1rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-3 .column:nth-child(3n + 3) {
  margin-right: 0;
}

.new-ui .columns-4.gutter-small .column {
  width: calc(25% - 0.75rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-4.gutter-small .column:nth-child(4n + 4) {
  margin-right: 0;
}

.new-ui .columns-4.gutter-medium .column {
  width: calc(25% - 1.5rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-4.gutter-medium .column:nth-child(4n + 4) {
  margin-right: 0;
}

.new-ui .columns-4.gutter-large .column {
  width: calc(25% - 2.25rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-4.gutter-large .column:nth-child(4n + 4) {
  margin-right: 0;
}

.new-ui .columns-4.gutter-xlarge .column {
  width: calc(25% - 3rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-4.gutter-xlarge .column:nth-child(4n + 4) {
  margin-right: 0;
}

.new-ui .columns-4 .column {
  width: calc(25% - 1.125rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-4 .column:nth-child(4n + 4) {
  margin-right: 0;
}

.new-ui .columns-5.gutter-small .column {
  width: calc(20% - 0.8rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-5.gutter-small .column:nth-child(5n + 5) {
  margin-right: 0;
}

.new-ui .columns-5.gutter-medium .column {
  width: calc(20% - 1.6rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-5.gutter-medium .column:nth-child(5n + 5) {
  margin-right: 0;
}

.new-ui .columns-5.gutter-large .column {
  width: calc(20% - 2.4rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-5.gutter-large .column:nth-child(5n + 5) {
  margin-right: 0;
}

.new-ui .columns-5.gutter-xlarge .column {
  width: calc(20% - 3.2rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-5.gutter-xlarge .column:nth-child(5n + 5) {
  margin-right: 0;
}

.new-ui .columns-5 .column {
  width: calc(20% - 1.2rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-5 .column:nth-child(5n + 5) {
  margin-right: 0;
}

.new-ui .columns-6.gutter-small .column {
  width: calc(16.666666% - 0.833333333rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-6.gutter-small .column:nth-child(6n + 6) {
  margin-right: 0;
}

.new-ui .columns-6.gutter-medium .column {
  width: calc(16.666666% - 1.666666667rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-6.gutter-medium .column:nth-child(6n + 6) {
  margin-right: 0;
}

.new-ui .columns-6.gutter-large .column {
  width: calc(16.666666% - 2.5rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-6.gutter-large .column:nth-child(6n + 6) {
  margin-right: 0;
}

.new-ui .columns-6.gutter-xlarge .column {
  width: calc(16.666666% - 3.333333333rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-6.gutter-xlarge .column:nth-child(6n + 6) {
  margin-right: 0;
}

.new-ui .columns-6 .column {
  width: calc(16.666666% - 1.25rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-6 .column:nth-child(6n + 6) {
  margin-right: 0;
}

.new-ui .columns-8.gutter-small .column {
  width: calc(12.5% - 0.875rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-8.gutter-small .column:nth-child(8n + 8) {
  margin-right: 0;
}

.new-ui .columns-8.gutter-medium .column {
  width: calc(12.5% - 1.75rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-8.gutter-medium .column:nth-child(8n + 8) {
  margin-right: 0;
}

.new-ui .columns-8.gutter-large .column {
  width: calc(12.5% - 2.625rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-8.gutter-large .column:nth-child(8n + 8) {
  margin-right: 0;
}

.new-ui .columns-8.gutter-xlarge .column {
  width: calc(12.5% - 3.5rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-8.gutter-xlarge .column:nth-child(8n + 8) {
  margin-right: 0;
}

.new-ui .columns-8 .column {
  width: calc(12.5% - 1.3125rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-8 .column:nth-child(8n + 8) {
  margin-right: 0;
}

.new-ui .columns-12.gutter-small .column {
  width: calc(8.333333% - 0.916666667rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-12.gutter-small .column:nth-child(12n + 12) {
  margin-right: 0;
}

.new-ui .columns-12.gutter-medium .column {
  width: calc(8.333333% - 1.833333333rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-12.gutter-medium .column:nth-child(12n + 12) {
  margin-right: 0;
}

.new-ui .columns-12.gutter-large .column {
  width: calc(8.333333% - 2.75rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-12.gutter-large .column:nth-child(12n + 12) {
  margin-right: 0;
}

.new-ui .columns-12.gutter-xlarge .column {
  width: calc(8.333333% - 3.666666667rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-12.gutter-xlarge .column:nth-child(12n + 12) {
  margin-right: 0;
}

.new-ui .columns-12 .column {
  width: calc(8.333333% - 1.375rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-12 .column:nth-child(12n + 12) {
  margin-right: 0;
}

.new-ui .columns-24.gutter-small .column {
  width: calc(4.166666% - 0.958333333rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.new-ui .columns-24.gutter-small .column:nth-child(24n + 24) {
  margin-right: 0;
}

.new-ui .columns-24.gutter-medium .column {
  width: calc(4.166666% - 1.916666667rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.new-ui .columns-24.gutter-medium .column:nth-child(24n + 24) {
  margin-right: 0;
}

.new-ui .columns-24.gutter-large .column {
  width: calc(4.166666% - 2.875rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.new-ui .columns-24.gutter-large .column:nth-child(24n + 24) {
  margin-right: 0;
}

.new-ui .columns-24.gutter-xlarge .column {
  width: calc(4.166666% - 3.833333333rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.new-ui .columns-24.gutter-xlarge .column:nth-child(24n + 24) {
  margin-right: 0;
}

.new-ui .columns-24 .column {
  width: calc(4.166666% - 1.4375rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.new-ui .columns-24 .column:nth-child(24n + 24) {
  margin-right: 0;
}

@media (min-width: 1024px) and (max-width: 1280px) {
  .new-ui .columns-5.gutter-small .column,
  .new-ui .columns-6.gutter-small .column,
  .new-ui .columns-8.gutter-small .column,
  .new-ui .columns-12.gutter-small .column {
    width: calc(25% - 0.75rem);
    margin-right: 1rem !important;
  }
  .new-ui .columns-5.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-small .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .new-ui .columns-5.gutter-medium .column,
  .new-ui .columns-6.gutter-medium .column,
  .new-ui .columns-8.gutter-medium .column,
  .new-ui .columns-12.gutter-medium .column {
    width: calc(25% - 1.5rem);
    margin-right: 2rem !important;
  }
  .new-ui .columns-5.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-medium .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .new-ui .columns-5.gutter-large .column,
  .new-ui .columns-6.gutter-large .column,
  .new-ui .columns-8.gutter-large .column,
  .new-ui .columns-12.gutter-large .column {
    width: calc(25% - 2.25rem);
    margin-right: 3rem !important;
  }
  .new-ui .columns-5.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-large .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .new-ui .columns-5.gutter-xlarge .column,
  .new-ui .columns-6.gutter-xlarge .column,
  .new-ui .columns-8.gutter-xlarge .column,
  .new-ui .columns-12.gutter-xlarge .column {
    width: calc(25% - 3rem);
    margin-right: 4rem !important;
  }
  .new-ui .columns-5.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .new-ui .columns-5 .column,
  .new-ui .columns-6 .column,
  .new-ui .columns-8 .column,
  .new-ui .columns-12 .column {
    width: calc(25% - 1.125rem);
    margin-right: 1.5rem !important;
  }
  .new-ui .columns-5 .column:nth-child(4n+4),
  .new-ui .columns-6 .column:nth-child(4n+4),
  .new-ui .columns-8 .column:nth-child(4n+4),
  .new-ui .columns-12 .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
}

@media (min-width: 850px) and (max-width: 1024px) {
  .new-ui .columns-4.gutter-small .column,
  .new-ui .columns-5.gutter-small .column,
  .new-ui .columns-6.gutter-small .column,
  .new-ui .columns-8.gutter-small .column,
  .new-ui .columns-12.gutter-small .column {
    width: calc(33.333333% - 0.666666667rem);
    margin-right: 1rem !important;
  }
  .new-ui .columns-4.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-small .column:nth-child(4n+4) {
    margin-right: 1rem !important;
  }
  .new-ui .columns-4.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-small .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .new-ui .columns-4.gutter-medium .column,
  .new-ui .columns-5.gutter-medium .column,
  .new-ui .columns-6.gutter-medium .column,
  .new-ui .columns-8.gutter-medium .column,
  .new-ui .columns-12.gutter-medium .column {
    width: calc(33.333333% - 1.333333333rem);
    margin-right: 2rem !important;
  }
  .new-ui .columns-4.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-medium .column:nth-child(4n+4) {
    margin-right: 2rem !important;
  }
  .new-ui .columns-4.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-medium .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .new-ui .columns-4.gutter-large .column,
  .new-ui .columns-5.gutter-large .column,
  .new-ui .columns-6.gutter-large .column,
  .new-ui .columns-8.gutter-large .column,
  .new-ui .columns-12.gutter-large .column {
    width: calc(33.333333% - 2rem);
    margin-right: 3rem !important;
  }
  .new-ui .columns-4.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-large .column:nth-child(4n+4) {
    margin-right: 3rem !important;
  }
  .new-ui .columns-4.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-large .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .new-ui .columns-4.gutter-xlarge .column,
  .new-ui .columns-5.gutter-xlarge .column,
  .new-ui .columns-6.gutter-xlarge .column,
  .new-ui .columns-8.gutter-xlarge .column,
  .new-ui .columns-12.gutter-xlarge .column {
    width: calc(33.333333% - 2.666666667rem);
    margin-right: 4rem !important;
  }
  .new-ui .columns-4.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 4rem !important;
  }
  .new-ui .columns-4.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-xlarge .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .new-ui .columns-4 .column,
  .new-ui .columns-5 .column,
  .new-ui .columns-6 .column,
  .new-ui .columns-8 .column,
  .new-ui .columns-12 .column {
    width: calc(33.333333% - 1rem);
    margin-right: 1.5rem !important;
  }
  .new-ui .columns-4 .column:nth-child(4n+4),
  .new-ui .columns-5 .column:nth-child(4n+4),
  .new-ui .columns-6 .column:nth-child(4n+4),
  .new-ui .columns-8 .column:nth-child(4n+4),
  .new-ui .columns-12 .column:nth-child(4n+4) {
    margin-right: 1.5rem !important;
  }
  .new-ui .columns-4 .column:nth-child(3n+3),
  .new-ui .columns-5 .column:nth-child(3n+3),
  .new-ui .columns-6 .column:nth-child(3n+3),
  .new-ui .columns-8 .column:nth-child(3n+3),
  .new-ui .columns-12 .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
}

@media (min-width: 680px) and (max-width: 850px) {
  .new-ui .columns-3.gutter-small .column,
  .new-ui .columns-4.gutter-small .column,
  .new-ui .columns-5.gutter-small .column,
  .new-ui .columns-6.gutter-small .column,
  .new-ui .columns-8.gutter-small .column,
  .new-ui .columns-12.gutter-small .column {
    width: calc(50% - 0.5rem);
    margin-right: 1rem !important;
  }
  .new-ui .columns-3.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-4.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-small .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-small .column:nth-child(4n+4) {
    margin-right: 1rem !important;
  }
  .new-ui .columns-3.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-4.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-small .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-small .column:nth-child(3n+3) {
    margin-right: 1rem !important;
  }
  .new-ui .columns-3.gutter-small .column:nth-child(2n+2),
  .new-ui .columns-4.gutter-small .column:nth-child(2n+2),
  .new-ui .columns-5.gutter-small .column:nth-child(2n+2),
  .new-ui .columns-6.gutter-small .column:nth-child(2n+2),
  .new-ui .columns-8.gutter-small .column:nth-child(2n+2),
  .new-ui .columns-12.gutter-small .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .columns-3.gutter-medium .column,
  .new-ui .columns-4.gutter-medium .column,
  .new-ui .columns-5.gutter-medium .column,
  .new-ui .columns-6.gutter-medium .column,
  .new-ui .columns-8.gutter-medium .column,
  .new-ui .columns-12.gutter-medium .column {
    width: calc(50% - 1rem);
    margin-right: 2rem !important;
  }
  .new-ui .columns-3.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-4.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-medium .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-medium .column:nth-child(4n+4) {
    margin-right: 2rem !important;
  }
  .new-ui .columns-3.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-4.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-medium .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-medium .column:nth-child(3n+3) {
    margin-right: 2rem !important;
  }
  .new-ui .columns-3.gutter-medium .column:nth-child(2n+2),
  .new-ui .columns-4.gutter-medium .column:nth-child(2n+2),
  .new-ui .columns-5.gutter-medium .column:nth-child(2n+2),
  .new-ui .columns-6.gutter-medium .column:nth-child(2n+2),
  .new-ui .columns-8.gutter-medium .column:nth-child(2n+2),
  .new-ui .columns-12.gutter-medium .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .columns-3.gutter-large .column,
  .new-ui .columns-4.gutter-large .column,
  .new-ui .columns-5.gutter-large .column,
  .new-ui .columns-6.gutter-large .column,
  .new-ui .columns-8.gutter-large .column,
  .new-ui .columns-12.gutter-large .column {
    width: calc(50% - 1.5rem);
    margin-right: 3rem !important;
  }
  .new-ui .columns-3.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-4.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-large .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-large .column:nth-child(4n+4) {
    margin-right: 3rem !important;
  }
  .new-ui .columns-3.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-4.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-large .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-large .column:nth-child(3n+3) {
    margin-right: 3rem !important;
  }
  .new-ui .columns-3.gutter-large .column:nth-child(2n+2),
  .new-ui .columns-4.gutter-large .column:nth-child(2n+2),
  .new-ui .columns-5.gutter-large .column:nth-child(2n+2),
  .new-ui .columns-6.gutter-large .column:nth-child(2n+2),
  .new-ui .columns-8.gutter-large .column:nth-child(2n+2),
  .new-ui .columns-12.gutter-large .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .columns-3.gutter-xlarge .column,
  .new-ui .columns-4.gutter-xlarge .column,
  .new-ui .columns-5.gutter-xlarge .column,
  .new-ui .columns-6.gutter-xlarge .column,
  .new-ui .columns-8.gutter-xlarge .column,
  .new-ui .columns-12.gutter-xlarge .column {
    width: calc(50% - 2rem);
    margin-right: 4rem !important;
  }
  .new-ui .columns-3.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-4.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-5.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-6.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-8.gutter-xlarge .column:nth-child(4n+4),
  .new-ui .columns-12.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 4rem !important;
  }
  .new-ui .columns-3.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-4.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-5.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-6.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-8.gutter-xlarge .column:nth-child(3n+3),
  .new-ui .columns-12.gutter-xlarge .column:nth-child(3n+3) {
    margin-right: 4rem !important;
  }
  .new-ui .columns-3.gutter-xlarge .column:nth-child(2n+2),
  .new-ui .columns-4.gutter-xlarge .column:nth-child(2n+2),
  .new-ui .columns-5.gutter-xlarge .column:nth-child(2n+2),
  .new-ui .columns-6.gutter-xlarge .column:nth-child(2n+2),
  .new-ui .columns-8.gutter-xlarge .column:nth-child(2n+2),
  .new-ui .columns-12.gutter-xlarge .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .columns-3 .column,
  .new-ui .columns-4 .column,
  .new-ui .columns-5 .column,
  .new-ui .columns-6 .column,
  .new-ui .columns-8 .column,
  .new-ui .columns-12 .column {
    width: calc(50% - 0.75rem);
    margin-right: 1.5rem !important;
  }
  .new-ui .columns-3 .column:nth-child(4n+4),
  .new-ui .columns-4 .column:nth-child(4n+4),
  .new-ui .columns-5 .column:nth-child(4n+4),
  .new-ui .columns-6 .column:nth-child(4n+4),
  .new-ui .columns-8 .column:nth-child(4n+4),
  .new-ui .columns-12 .column:nth-child(4n+4) {
    margin-right: 1.5rem !important;
  }
  .new-ui .columns-3 .column:nth-child(3n+3),
  .new-ui .columns-4 .column:nth-child(3n+3),
  .new-ui .columns-5 .column:nth-child(3n+3),
  .new-ui .columns-6 .column:nth-child(3n+3),
  .new-ui .columns-8 .column:nth-child(3n+3),
  .new-ui .columns-12 .column:nth-child(3n+3) {
    margin-right: 1.5rem !important;
  }
  .new-ui .columns-3 .column:nth-child(2n+2),
  .new-ui .columns-4 .column:nth-child(2n+2),
  .new-ui .columns-5 .column:nth-child(2n+2),
  .new-ui .columns-6 .column:nth-child(2n+2),
  .new-ui .columns-8 .column:nth-child(2n+2),
  .new-ui .columns-12 .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
}

@media (min-width: 480px) and (max-width: 680px) {
  .new-ui .columns-2 .column,
  .new-ui .columns-3 .column,
  .new-ui .columns-4 .column,
  .new-ui .columns-5 .column,
  .new-ui .columns-6 .column,
  .new-ui .columns-8 .column,
  .new-ui .columns-12 .column {
    width: 100% !important;
    margin-right: 0 !important;
    float: none !important;
  }
  .new-ui .replace-above.gutter-small .column {
    width: calc(50% - 0.5rem);
    margin-right: 1rem !important;
  }
  .new-ui .replace-above.gutter-small .column:nth-child(4n+4) {
    margin-right: 1rem !important;
  }
  .new-ui .replace-above.gutter-small .column:nth-child(3n+3) {
    margin-right: 1rem !important;
  }
  .new-ui .replace-above.gutter-small .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .replace-above.gutter-medium .column {
    width: calc(50% - 1rem);
    margin-right: 2rem !important;
  }
  .new-ui .replace-above.gutter-medium .column:nth-child(4n+4) {
    margin-right: 2rem !important;
  }
  .new-ui .replace-above.gutter-medium .column:nth-child(3n+3) {
    margin-right: 2rem !important;
  }
  .new-ui .replace-above.gutter-medium .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .replace-above.gutter-large .column {
    width: calc(50% - 1.5rem);
    margin-right: 3rem !important;
  }
  .new-ui .replace-above.gutter-large .column:nth-child(4n+4) {
    margin-right: 3rem !important;
  }
  .new-ui .replace-above.gutter-large .column:nth-child(3n+3) {
    margin-right: 3rem !important;
  }
  .new-ui .replace-above.gutter-large .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .replace-above.gutter-xlarge .column {
    width: calc(50% - 2rem);
    margin-right: 4rem !important;
  }
  .new-ui .replace-above.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 4rem !important;
  }
  .new-ui .replace-above.gutter-xlarge .column:nth-child(3n+3) {
    margin-right: 4rem !important;
  }
  .new-ui .replace-above.gutter-xlarge .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .new-ui .replace-above .column {
    width: calc(50% - 0.75rem);
    margin-right: 1.5rem !important;
  }
  .new-ui .replace-above .column:nth-child(4n+4) {
    margin-right: 1.5rem !important;
  }
  .new-ui .replace-above .column:nth-child(3n+3) {
    margin-right: 1.5rem !important;
  }
  .new-ui .replace-above .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
}

@media (max-width: 480px) {
  .new-ui .columns-2 .column,
  .new-ui .columns-3 .column,
  .new-ui .columns-4 .column,
  .new-ui .columns-5 .column,
  .new-ui .columns-6 .column,
  .new-ui .columns-8 .column,
  .new-ui .columns-12 .column {
    width: 100% !important;
    margin-right: 0 !important;
    float: none !important;
  }
}

.new-ui table,
.new-ui .ui-table {
  table-layout: fixed;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.new-ui table tr,
.new-ui .ui-table tr {
  position: relative;
}

.new-ui table tr th,
.new-ui table tr td,
.new-ui .ui-table tr th,
.new-ui .ui-table tr td {
  clear: both;
  position: relative;
}

.new-ui table tr th:after,
.new-ui table tr td:after,
.new-ui .ui-table tr th:after,
.new-ui .ui-table tr td:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui table tr th.align-right,
.new-ui table tr td.align-right,
.new-ui .ui-table tr th.align-right,
.new-ui .ui-table tr td.align-right {
  text-align: right;
}

.new-ui table tr th.align-center,
.new-ui table tr td.align-center,
.new-ui .ui-table tr th.align-center,
.new-ui .ui-table tr td.align-center {
  text-align: center;
}

.new-ui table tr th.align-left,
.new-ui table tr td.align-left,
.new-ui .ui-table tr th.align-left,
.new-ui .ui-table tr td.align-left {
  text-align: left;
}

.new-ui table thead tr th,
.new-ui .ui-table thead tr th {
  padding: 1rem;
  font-weight: 700;
  vertical-align: top;
  color: #FFF;
  font-size: 0.85rem;
  letter-spacing: 1px;
  text-align: left;
}

.new-ui table thead tr th:first-child,
.new-ui .ui-table thead tr th:first-child {
  border-top-left-radius: 0.25rem;
}

.new-ui table thead tr th:last-child,
.new-ui .ui-table thead tr th:last-child {
  border-top-right-radius: 0.25rem;
}

.new-ui table .table-sort,
.new-ui .ui-table .table-sort {
  cursor: pointer;
  padding-right: 0.75rem;
  position: relative;
}

.new-ui table .table-sort .table-sort-toggle,
.new-ui .ui-table .table-sort .table-sort-toggle {
  width: 1rem;
  height: 1rem;
  position: absolute;
  top: 50%;
  right: 0.75rem;
  margin-top: -0.5rem;
  border-radius: 100%;
  text-align: center;
}

.new-ui table .table-sort .table-sort-toggle:before,
.new-ui .ui-table .table-sort .table-sort-toggle:before {
  display: block;
  color: #80b2ff;
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  position: absolute;
  top: 0;
  right: 0;
  text-indent: 2px;
}

.new-ui table .table-sort .table-sort-toggle:hover,
.new-ui .ui-table .table-sort .table-sort-toggle:hover {
  background: #f2f3f6;
}

.new-ui table .table-sort .table-sort-toggle:hover:before,
.new-ui .ui-table .table-sort .table-sort-toggle:hover:before {
  color: #FFF;
}

.new-ui table .highlight,
.new-ui .ui-table .highlight {
  color: #f2f3f6 !important;
  background: rgba(242, 243, 246, 0.15);
}

.new-ui table .highlight:before, .new-ui table .highlight[class*="icon-"]:before,
.new-ui table .highlight *[class*="icon-"]:before,
.new-ui .ui-table .highlight:before,
.new-ui .ui-table .highlight[class*="icon-"]:before,
.new-ui .ui-table .highlight *[class*="icon-"]:before {
  color: #f2f3f6;
}

.new-ui table.table-default tr th,
.new-ui table.table-default tr td,
.new-ui .ui-table.table-default tr th,
.new-ui .ui-table.table-default tr td {
  clear: both;
  position: relative;
}

.new-ui table.table-default tr th:after,
.new-ui table.table-default tr td:after,
.new-ui .ui-table.table-default tr th:after,
.new-ui .ui-table.table-default tr td:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui table.table-default tr th *[class*="ui-action"],
.new-ui table.table-default tr td *[class*="ui-action"],
.new-ui .ui-table.table-default tr th *[class*="ui-action"],
.new-ui .ui-table.table-default tr td *[class*="ui-action"] {
  -moz-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  position: absolute;
  top: 50%;
  right: 0.5rem;
}

.new-ui table.table-default tr th *[class*="ui-action"]:before,
.new-ui table.table-default tr td *[class*="ui-action"]:before,
.new-ui .ui-table.table-default tr th *[class*="ui-action"]:before,
.new-ui .ui-table.table-default tr td *[class*="ui-action"]:before {
  color: #bababa;
}

.new-ui table.table-default tr th *[class*="ui-action"]:hover:before,
.new-ui table.table-default tr td *[class*="ui-action"]:hover:before,
.new-ui .ui-table.table-default tr th *[class*="ui-action"]:hover:before,
.new-ui .ui-table.table-default tr td *[class*="ui-action"]:hover:before {
  color: #5d9bfc;
  transition: color 0.1s ease-out;
}

.new-ui table.table-default tr th.align-center, .new-ui table.table-default tr th.center,
.new-ui table.table-default tr td.align-center,
.new-ui table.table-default tr td.center,
.new-ui .ui-table.table-default tr th.align-center,
.new-ui .ui-table.table-default tr th.center,
.new-ui .ui-table.table-default tr td.align-center,
.new-ui .ui-table.table-default tr td.center {
  text-align: center;
}

.new-ui table.table-default thead,
.new-ui .ui-table.table-default thead {
  background: #e0e9f9;
  border: 1px solid #c9d7f0;
}

.new-ui table.table-default thead tr th,
.new-ui .ui-table.table-default thead tr th {
  padding: 0.75rem;
  font-weight: 700;
  vertical-align: top;
  color: #5d759a;
  font-size: 0.75rem;
  letter-spacing: 1px;
  text-align: left;
}

.new-ui table.table-default thead tr th:first-child,
.new-ui .ui-table.table-default thead tr th:first-child {
  border-top-left-radius: 0.25rem;
}

.new-ui table.table-default thead tr th:last-child,
.new-ui .ui-table.table-default thead tr th:last-child {
  border-top-right-radius: 0.25rem;
}

.new-ui table.table-default tbody,
.new-ui .ui-table.table-default tbody {
  border: 1px solid #c9d7f0;
}

.new-ui table.table-default tbody tr,
.new-ui .ui-table.table-default tbody tr {
  border-bottom: 1px solid #c9d7f0;
}

.new-ui table.table-default tbody tr td,
.new-ui .ui-table.table-default tbody tr td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  box-sizing: border-box;
  line-height: 1.15rem;
  font-weight: normal;
  color: #5d759a;
}

.new-ui table.table-default tbody tr td[class*="icon-"]:before,
.new-ui .ui-table.table-default tbody tr td[class*="icon-"]:before {
  font-size: 1.35rem;
}

.new-ui table.table-default tbody tr td.actions,
.new-ui .ui-table.table-default tbody tr td.actions {
  padding-right: 2rem;
}

.new-ui table.table-default tbody tr td .selection-match,
.new-ui .ui-table.table-default tbody tr td .selection-match {
  font-weight: 600;
}

.new-ui table.table-default tbody tr:hover,
.new-ui .ui-table.table-default tbody tr:hover {
  background: #f4f8ff;
  transition: background 0.15 ease-out;
}

.new-ui table.table-default tbody tr.ui-notification.error, .new-ui table.table-default tbody tr.ui-validation.error,
.new-ui .ui-table.table-default tbody tr.ui-notification.error,
.new-ui .ui-table.table-default tbody tr.ui-validation.error {
  border: 1px solid #e09898;
  position: relative;
}

.new-ui table.table-default tbody tr.ui-notification.error td, .new-ui table.table-default tbody tr.ui-validation.error td,
.new-ui .ui-table.table-default tbody tr.ui-notification.error td,
.new-ui .ui-table.table-default tbody tr.ui-validation.error td {
  background: rgba(226, 100, 100, 0.25);
  color: #e26464;
  text-align: right;
}

.new-ui table.table-default tbody tr.ui-notification.error td:before, .new-ui table.table-default tbody tr.ui-validation.error td:before,
.new-ui .ui-table.table-default tbody tr.ui-notification.error td:before,
.new-ui .ui-table.table-default tbody tr.ui-validation.error td:before {
  display: block;
  content: "";
  width: calc(100% + 2px);
  height: 1px;
  background: #e09898;
  position: absolute;
  top: -1px;
  left: -1px;
}

.new-ui table.table-default tbody tr.ui-notification.error td span:before, .new-ui table.table-default tbody tr.ui-validation.error td span:before,
.new-ui .ui-table.table-default tbody tr.ui-notification.error td span:before,
.new-ui .ui-table.table-default tbody tr.ui-validation.error td span:before {
  margin-right: 0.75rem;
}

.new-ui table.table-default tbody tr.ui-notification.warning, .new-ui table.table-default tbody tr.ui-validation.warning,
.new-ui .ui-table.table-default tbody tr.ui-notification.warning,
.new-ui .ui-table.table-default tbody tr.ui-validation.warning {
  border: 1px solid #f0d886;
  position: relative;
}

.new-ui table.table-default tbody tr.ui-notification.warning td, .new-ui table.table-default tbody tr.ui-validation.warning td,
.new-ui .ui-table.table-default tbody tr.ui-notification.warning td,
.new-ui .ui-table.table-default tbody tr.ui-validation.warning td {
  background: rgba(240, 216, 134, 0.25);
  color: orange;
  text-align: right;
}

.new-ui table.table-default tbody tr.ui-notification.warning td:before, .new-ui table.table-default tbody tr.ui-validation.warning td:before,
.new-ui .ui-table.table-default tbody tr.ui-notification.warning td:before,
.new-ui .ui-table.table-default tbody tr.ui-validation.warning td:before {
  display: block;
  content: "";
  width: calc(100% + 2px);
  height: 1px;
  background: #f0d886;
  position: absolute;
  top: -1px;
  left: -1px;
}

.new-ui table.table-default tbody tr.ui-notification.warning td span:before, .new-ui table.table-default tbody tr.ui-validation.warning td span:before,
.new-ui .ui-table.table-default tbody tr.ui-notification.warning td span:before,
.new-ui .ui-table.table-default tbody tr.ui-validation.warning td span:before {
  margin-right: 0.75rem;
}

.new-ui table.table-default tbody tr.ui-notification.info, .new-ui table.table-default tbody tr.ui-validation.info,
.new-ui .ui-table.table-default tbody tr.ui-notification.info,
.new-ui .ui-table.table-default tbody tr.ui-validation.info {
  border: 1px solid #80b2ff;
  position: relative;
}

.new-ui table.table-default tbody tr.ui-notification.info td, .new-ui table.table-default tbody tr.ui-validation.info td,
.new-ui .ui-table.table-default tbody tr.ui-notification.info td,
.new-ui .ui-table.table-default tbody tr.ui-validation.info td {
  background: rgba(128, 178, 255, 0.25);
  color: #80b2ff;
  text-align: right;
}

.new-ui table.table-default tbody tr.ui-notification.info td:before, .new-ui table.table-default tbody tr.ui-validation.info td:before,
.new-ui .ui-table.table-default tbody tr.ui-notification.info td:before,
.new-ui .ui-table.table-default tbody tr.ui-validation.info td:before {
  display: block;
  content: "";
  width: calc(100% + 2px);
  height: 1px;
  background: #80b2ff;
  position: absolute;
  top: -1px;
  left: -1px;
}

.new-ui table.table-default tbody tr.ui-notification.info td span:before, .new-ui table.table-default tbody tr.ui-validation.info td span:before,
.new-ui .ui-table.table-default tbody tr.ui-notification.info td span:before,
.new-ui .ui-table.table-default tbody tr.ui-validation.info td span:before {
  margin-right: 0.75rem;
}

.new-ui table.table-default.blue thead,
.new-ui .ui-table.table-default.blue thead {
  background: #80b2ff;
  border: 1px solid #6da7ff;
}

.new-ui table.table-default.blue thead tr th,
.new-ui .ui-table.table-default.blue thead tr th {
  color: #FFF;
}

.new-ui table.table-default.blue tbody,
.new-ui .ui-table.table-default.blue tbody {
  border-bottom: 1px solid #c9d7f0;
}

.new-ui table.table-default.blue tbody tr,
.new-ui .ui-table.table-default.blue tbody tr {
  border-bottom: 1px solid #6da7ff;
}

.new-ui table.table-list thead,
.new-ui .ui-table.table-list thead {
  background: none;
  border: none;
  border-top: 2px solid #5d9bfc;
  border-bottom: 2px solid #5d9bfc;
}

.new-ui table.table-list thead tr th,
.new-ui .ui-table.table-list thead tr th {
  padding: 0.65rem;
  line-height: 1.1em;
  font-weight: 700;
  color: #5d9bfc;
  font-size: 0.75rem;
  vertical-align: middle;
}

.new-ui table.table-list thead tr th:first-child,
.new-ui .ui-table.table-list thead tr th:first-child {
  border-top-left-radius: none;
}

.new-ui table.table-list thead tr th:last-child,
.new-ui .ui-table.table-list thead tr th:last-child {
  border-top-right-radius: none;
}

.new-ui table.table-list tbody,
.new-ui .ui-table.table-list tbody {
  border: none;
}

.new-ui table.table-list tbody tr,
.new-ui .ui-table.table-list tbody tr {
  border-bottom: none;
}

.new-ui table.table-list tbody tr:nth-child(even),
.new-ui .ui-table.table-list tbody tr:nth-child(even) {
  background: #FFF;
}

.new-ui table.table-list tbody tr td,
.new-ui .ui-table.table-list tbody tr td {
  padding: 0.5rem;
  font-size: 0.85rem;
  line-height: 1.1em;
}

.new-ui table.table-blocks,
.new-ui .ui-table.table-blocks {
  border-radius: 0;
}

.new-ui table.table-blocks thead,
.new-ui .ui-table.table-blocks thead {
  background: none;
  border: 0;
}

.new-ui table.table-blocks thead tr th,
.new-ui .ui-table.table-blocks thead tr th {
  padding: 0.75rem;
  font-weight: 600;
  vertical-align: top;
  color: #5d759a;
  letter-spacing: 0;
  font-size: 1rem;
  text-align: left;
  border-radius: 0;
}

.new-ui table.table-blocks tbody,
.new-ui .ui-table.table-blocks tbody {
  border: 1px solid #e0e9f9;
  border-bottom: 0;
}

.new-ui table.table-blocks tbody tr,
.new-ui .ui-table.table-blocks tbody tr {
  border-top: 1px solid #e0e9f9;
}

.new-ui table.table-blocks tbody tr td,
.new-ui .ui-table.table-blocks tbody tr td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  box-sizing: border-box;
  line-height: 1.15rem;
  font-weight: normal;
  color: #5d759a;
}

.new-ui table.table-blocks tbody tr:last-child td,
.new-ui .ui-table.table-blocks tbody tr:last-child td {
  box-shadow: inset 0 -1px 0 0 #e0e9f9, inset 0 1px 0 0 #e0e9f9;
}

.new-ui table.table-blocks tbody tr:hover,
.new-ui .ui-table.table-blocks tbody tr:hover {
  background: #f4f8ff;
  border-left: 1px solid #b8c8e4;
  border-right: 1px solid #b8c8e4;
  transition: all 0.15s ease-out;
}

.new-ui table.table-blocks tbody tr:hover td,
.new-ui .ui-table.table-blocks tbody tr:hover td {
  box-shadow: inset 0 1px 0 0 #b8c8e4;
  transition: all 0.25s ease-out;
}

.new-ui table.table-blocks tbody tr:hover + tr,
.new-ui .ui-table.table-blocks tbody tr:hover + tr {
  border-top: 1px solid #b8c8e4;
  transition: all 0.25s ease-out;
}

.new-ui table.table-blocks tbody tr:hover + tr:last-child td,
.new-ui .ui-table.table-blocks tbody tr:hover + tr:last-child td {
  box-shadow: inset 0 -1px 0 0 #e0e9f9, inset 0 1px 0 0 #b8c8e4;
  transition: all 0.25s ease-out;
}

.new-ui table.table-blocks tbody tr:hover:last-child td,
.new-ui .ui-table.table-blocks tbody tr:hover:last-child td {
  box-shadow: inset 0 -1px 0 0 #b8c8e4, inset 0 1px 0 0 #b8c8e4;
  transition: all 0.25s ease-out;
}

.new-ui table.table-finance,
.new-ui .ui-table.table-finance {
  margin-bottom: 2rem;
  border: 2px solid #80b2ff;
}

.new-ui table.table-finance tr td,
.new-ui .ui-table.table-finance tr td {
  padding: 1rem 0.5rem;
}

.new-ui table.table-finance tbody tr td,
.new-ui .ui-table.table-finance tbody tr td {
  font-size: 1.15rem;
  border-bottom: 2px solid #80b2ff;
}

.new-ui table.table-finance tfoot,
.new-ui .ui-table.table-finance tfoot {
  border: 2px solid #5d9bfc;
}

.new-ui table.table-finance tfoot tr,
.new-ui .ui-table.table-finance tfoot tr {
  background: #5d9bfc;
  font-size: 1.15rem;
}

.new-ui table.table-finance tfoot tr td,
.new-ui .ui-table.table-finance tfoot tr td {
  border: none;
  color: #FFF;
}

.new-ui table.table-entity thead,
.new-ui .ui-table.table-entity thead {
  background: none;
  border: none;
  border-bottom: 2px solid #a8c9fc;
}

.new-ui table.table-entity thead tr th,
.new-ui .ui-table.table-entity thead tr th {
  padding: 0.65rem;
  line-height: 1.1em;
  font-weight: 700;
  color: #5d9bfc;
  font-size: 0.75rem;
  vertical-align: middle;
}

.new-ui table.table-entity thead tr th:first-child,
.new-ui .ui-table.table-entity thead tr th:first-child {
  border-top-left-radius: none;
}

.new-ui table.table-entity thead tr th:last-child,
.new-ui .ui-table.table-entity thead tr th:last-child {
  border-top-right-radius: none;
}

.new-ui table.table-entity tbody,
.new-ui .ui-table.table-entity tbody {
  border: none;
}

.new-ui table.table-entity tbody tr,
.new-ui .ui-table.table-entity tbody tr {
  border-bottom: 1px solid #a8c9fc;
}

.new-ui table.table-entity tbody tr.highlight,
.new-ui .ui-table.table-entity tbody tr.highlight {
  color: #5d9bfc;
  box-shadow: 6px 0 0 0 #5d9bfc !important;
}

.new-ui table.table-entity tbody tr:last-child,
.new-ui .ui-table.table-entity tbody tr:last-child {
  border-bottom: none;
}

.new-ui table.table-entity tbody tr td,
.new-ui .ui-table.table-entity tbody tr td {
  padding: 0.75rem 0.5rem;
  font-size: 0.9rem;
  line-height: 1.1em;
  vertical-align: middle;
}

@media (max-width: 500px) {
  .new-ui table .table-mobile,
  .new-ui .ui-table .table-mobile {
    display: block !important;
  }
  .new-ui table .table-mobile.label,
  .new-ui .ui-table .table-mobile.label {
    font-size: 0.825rem;
    text-transform: uppercase;
    color: #7e7e7e;
    display: inline-block !important;
    width: auto;
    margin-right: 0.5rem;
  }
  .new-ui table .type-link,
  .new-ui .ui-table .type-link {
    font-weight: 600;
  }
  .new-ui table.style-clean tr,
  .new-ui .ui-table.style-clean tr {
    border-bottom: 1px solid #bababa;
  }
  .new-ui table.style-clean tr:first-of-type td,
  .new-ui .ui-table.style-clean tr:first-of-type td {
    padding-top: 0;
  }
  .new-ui table.table-finance tbody tr td,
  .new-ui .ui-table.table-finance tbody tr td {
    text-align: center;
    border-bottom: none;
  }
  .new-ui table.table-finance tfoot tr td,
  .new-ui .ui-table.table-finance tfoot tr td {
    border: none;
    text-align: center;
  }
}

.new-ui form.ui-form {
  clear: both;
}

.new-ui form.ui-form:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui form.ui-form.grid input[class*="grid-"],
.new-ui form.ui-form.grid textarea[class*="grid-"],
.new-ui form.ui-form .grid input[class*="grid-"],
.new-ui form.ui-form .grid textarea[class*="grid-"] {
  margin-top: 0 !important;
}

.new-ui form.ui-form p {
  margin-bottom: 0;
}

.new-ui form.ui-form *.inline {
  display: inline-block !important;
}

.new-ui form.ui-form *.required:before {
  display: inline-block;
  width: auto;
  height: inherit;
  font-size: 1.5em;
  line-height: inherit;
  vertical-align: middle;
  content: "*";
  color: #5d9bfc;
  margin-right: 0.5rem;
}

.new-ui .ui-form input[type="text"],
.new-ui .ui-form input[type="search"],
.new-ui .ui-form input[type="password"],
.new-ui .ui-form input[type="email"],
.new-ui .ui-form input[type="phone"],
.new-ui .ui-form input[type="address"],
.new-ui .ui-form input[type="search"],
.new-ui .ui-form input[type="date"],
.new-ui .ui-form input[type="month"],
.new-ui .ui-form input[type="number"],
.new-ui .ui-form input[type="url"],
.new-ui .ui-form input[type="tel"],
.new-ui .ui-form input[type="range"],
.new-ui .ui-form input[type="datetime"],
.new-ui .ui-form input[type="week"],
.new-ui .ui-form textarea {
  padding: 0.5rem;
  outline: none;
  border: 1px solid #c1cfe6;
  box-shadow: none;
  vertical-align: middle;
  -webkit-appearance: none;
  color: #304565;
  background: #FFF;
  border-radius: 2px;
  width: 100%;
  font-weight: normal;
  font-size: 1rem;
  -webkit-appearance: none;
}

.new-ui .ui-form input[type="text"]:hover, .new-ui .ui-form input[type="text"]:focus,
.new-ui .ui-form input[type="search"]:hover,
.new-ui .ui-form input[type="search"]:focus,
.new-ui .ui-form input[type="password"]:hover,
.new-ui .ui-form input[type="password"]:focus,
.new-ui .ui-form input[type="email"]:hover,
.new-ui .ui-form input[type="email"]:focus,
.new-ui .ui-form input[type="phone"]:hover,
.new-ui .ui-form input[type="phone"]:focus,
.new-ui .ui-form input[type="address"]:hover,
.new-ui .ui-form input[type="address"]:focus,
.new-ui .ui-form input[type="search"]:hover,
.new-ui .ui-form input[type="search"]:focus,
.new-ui .ui-form input[type="date"]:hover,
.new-ui .ui-form input[type="date"]:focus,
.new-ui .ui-form input[type="month"]:hover,
.new-ui .ui-form input[type="month"]:focus,
.new-ui .ui-form input[type="number"]:hover,
.new-ui .ui-form input[type="number"]:focus,
.new-ui .ui-form input[type="url"]:hover,
.new-ui .ui-form input[type="url"]:focus,
.new-ui .ui-form input[type="tel"]:hover,
.new-ui .ui-form input[type="tel"]:focus,
.new-ui .ui-form input[type="range"]:hover,
.new-ui .ui-form input[type="range"]:focus,
.new-ui .ui-form input[type="datetime"]:hover,
.new-ui .ui-form input[type="datetime"]:focus,
.new-ui .ui-form input[type="week"]:hover,
.new-ui .ui-form input[type="week"]:focus,
.new-ui .ui-form textarea:hover,
.new-ui .ui-form textarea:focus {
  background: white;
  transition: all 0.15s ease-out;
  box-shadow: 0 0 0 3px rgba(128, 178, 255, 0.125);
}

.new-ui .ui-form input[type="text"]:focus,
.new-ui .ui-form input[type="search"]:focus,
.new-ui .ui-form input[type="password"]:focus,
.new-ui .ui-form input[type="email"]:focus,
.new-ui .ui-form input[type="phone"]:focus,
.new-ui .ui-form input[type="address"]:focus,
.new-ui .ui-form input[type="search"]:focus,
.new-ui .ui-form input[type="date"]:focus,
.new-ui .ui-form input[type="month"]:focus,
.new-ui .ui-form input[type="number"]:focus,
.new-ui .ui-form input[type="url"]:focus,
.new-ui .ui-form input[type="tel"]:focus,
.new-ui .ui-form input[type="range"]:focus,
.new-ui .ui-form input[type="datetime"]:focus,
.new-ui .ui-form input[type="week"]:focus,
.new-ui .ui-form textarea:focus {
  border: 1px solid #c1cfe6;
  transition: all 0.15s ease-out;
}

.new-ui .ui-form input[type="text"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="search"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="password"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="email"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="phone"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="address"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="search"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="date"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="month"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="number"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="url"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="tel"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="range"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="datetime"]::-webkit-input-placeholder,
.new-ui .ui-form input[type="week"]::-webkit-input-placeholder,
.new-ui .ui-form textarea::-webkit-input-placeholder {
  color: #a7b8d6;
  text-transform: none;
  font-style: italic;
  font-weight: 600;
}

.new-ui .ui-form input[type="text"]:-moz-placeholder,
.new-ui .ui-form input[type="search"]:-moz-placeholder,
.new-ui .ui-form input[type="password"]:-moz-placeholder,
.new-ui .ui-form input[type="email"]:-moz-placeholder,
.new-ui .ui-form input[type="phone"]:-moz-placeholder,
.new-ui .ui-form input[type="address"]:-moz-placeholder,
.new-ui .ui-form input[type="search"]:-moz-placeholder,
.new-ui .ui-form input[type="date"]:-moz-placeholder,
.new-ui .ui-form input[type="month"]:-moz-placeholder,
.new-ui .ui-form input[type="number"]:-moz-placeholder,
.new-ui .ui-form input[type="url"]:-moz-placeholder,
.new-ui .ui-form input[type="tel"]:-moz-placeholder,
.new-ui .ui-form input[type="range"]:-moz-placeholder,
.new-ui .ui-form input[type="datetime"]:-moz-placeholder,
.new-ui .ui-form input[type="week"]:-moz-placeholder,
.new-ui .ui-form textarea:-moz-placeholder {
  color: #a7b8d6;
  text-transform: none;
  font-style: italic;
  font-weight: 600;
}

.new-ui .ui-form input[type="text"]::-moz-placeholder,
.new-ui .ui-form input[type="search"]::-moz-placeholder,
.new-ui .ui-form input[type="password"]::-moz-placeholder,
.new-ui .ui-form input[type="email"]::-moz-placeholder,
.new-ui .ui-form input[type="phone"]::-moz-placeholder,
.new-ui .ui-form input[type="address"]::-moz-placeholder,
.new-ui .ui-form input[type="search"]::-moz-placeholder,
.new-ui .ui-form input[type="date"]::-moz-placeholder,
.new-ui .ui-form input[type="month"]::-moz-placeholder,
.new-ui .ui-form input[type="number"]::-moz-placeholder,
.new-ui .ui-form input[type="url"]::-moz-placeholder,
.new-ui .ui-form input[type="tel"]::-moz-placeholder,
.new-ui .ui-form input[type="range"]::-moz-placeholder,
.new-ui .ui-form input[type="datetime"]::-moz-placeholder,
.new-ui .ui-form input[type="week"]::-moz-placeholder,
.new-ui .ui-form textarea::-moz-placeholder {
  color: #a7b8d6;
  text-transform: none;
  font-style: italic;
  font-weight: 600;
}

.new-ui .ui-form input[type="text"]:-ms-input-placeholder,
.new-ui .ui-form input[type="search"]:-ms-input-placeholder,
.new-ui .ui-form input[type="password"]:-ms-input-placeholder,
.new-ui .ui-form input[type="email"]:-ms-input-placeholder,
.new-ui .ui-form input[type="phone"]:-ms-input-placeholder,
.new-ui .ui-form input[type="address"]:-ms-input-placeholder,
.new-ui .ui-form input[type="search"]:-ms-input-placeholder,
.new-ui .ui-form input[type="date"]:-ms-input-placeholder,
.new-ui .ui-form input[type="month"]:-ms-input-placeholder,
.new-ui .ui-form input[type="number"]:-ms-input-placeholder,
.new-ui .ui-form input[type="url"]:-ms-input-placeholder,
.new-ui .ui-form input[type="tel"]:-ms-input-placeholder,
.new-ui .ui-form input[type="range"]:-ms-input-placeholder,
.new-ui .ui-form input[type="datetime"]:-ms-input-placeholder,
.new-ui .ui-form input[type="week"]:-ms-input-placeholder,
.new-ui .ui-form textarea:-ms-input-placeholder {
  color: #a7b8d6;
  text-transform: none;
  font-style: italic;
  font-weight: 600;
}

.new-ui .ui-form input[type="text"]:-webkit-autofill,
.new-ui .ui-form input[type="search"]:-webkit-autofill,
.new-ui .ui-form input[type="password"]:-webkit-autofill,
.new-ui .ui-form input[type="email"]:-webkit-autofill,
.new-ui .ui-form input[type="phone"]:-webkit-autofill,
.new-ui .ui-form input[type="address"]:-webkit-autofill,
.new-ui .ui-form input[type="search"]:-webkit-autofill,
.new-ui .ui-form input[type="date"]:-webkit-autofill,
.new-ui .ui-form input[type="month"]:-webkit-autofill,
.new-ui .ui-form input[type="number"]:-webkit-autofill,
.new-ui .ui-form input[type="url"]:-webkit-autofill,
.new-ui .ui-form input[type="tel"]:-webkit-autofill,
.new-ui .ui-form input[type="range"]:-webkit-autofill,
.new-ui .ui-form input[type="datetime"]:-webkit-autofill,
.new-ui .ui-form input[type="week"]:-webkit-autofill,
.new-ui .ui-form textarea:-webkit-autofill {
  -webkit-box-shadow: inset 0 0 0px 1000px white;
}

.new-ui .ui-form input[type="text"].inline,
.new-ui .ui-form input[type="search"].inline,
.new-ui .ui-form input[type="password"].inline,
.new-ui .ui-form input[type="email"].inline,
.new-ui .ui-form input[type="phone"].inline,
.new-ui .ui-form input[type="address"].inline,
.new-ui .ui-form input[type="search"].inline,
.new-ui .ui-form input[type="date"].inline,
.new-ui .ui-form input[type="month"].inline,
.new-ui .ui-form input[type="number"].inline,
.new-ui .ui-form input[type="url"].inline,
.new-ui .ui-form input[type="tel"].inline,
.new-ui .ui-form input[type="range"].inline,
.new-ui .ui-form input[type="datetime"].inline,
.new-ui .ui-form input[type="week"].inline,
.new-ui .ui-form textarea.inline {
  display: inline;
  width: auto;
}

.new-ui .ui-form input[type="text"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="search"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="password"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="email"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="phone"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="address"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="search"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="date"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="month"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="number"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="url"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="tel"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="range"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="datetime"] + input:not([type="submit"]):not(button),
.new-ui .ui-form input[type="week"] + input:not([type="submit"]):not(button) {
  margin-top: 1rem;
}

.new-ui .ui-form input[type="search"] {
  -webkit-appearance: none;
}

.new-ui .ui-form input[type="search"]:hover, .new-ui .ui-form input[type="search"]:focus {
  background: white;
  transition: background 0.15s ease-out;
}

.new-ui .ui-form textarea {
  display: block;
  width: 100%;
  height: auto;
  min-height: 10rem;
  padding: 0.75rem 0;
  box-sizing: border-box;
  resize: none;
}

.new-ui .ui-form textarea.large {
  min-height: 20rem;
}

.new-ui .ui-form label {
  display: block;
  margin: 0 0 0.5em 1px;
  color: #758ba9;
  font-weight: 600;
  font-size: 1rem;
}

.new-ui .ui-form label[for*="radio"], .new-ui .ui-form label[for*="checkbox"] {
  display: inline-block;
}

input + .new-ui .ui-form label {
  margin-top: 1rem;
}

.new-ui .ui-form label.inline {
  display: inline-block;
  width: auto;
}

.new-ui .ui-form .ui-button {
  display: inline-block;
  width: auto;
  height: auto;
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  text-align: center;
  cursor: pointer;
  box-shadow: none;
  outline: none;
  border-radius: 0.25rem;
  transition: all 0.15s ease-out;
  display: inline-block;
  height: 2.25rem;
  margin: 0;
  padding: 0 1.875em;
  text-indent: 0;
  line-height: 2.25rem;
  border: none;
  border-radius: 0.25rem;
  color: #fff;
  cursor: pointer;
  -webkit-appearance: none;
}

.new-ui .ui-form .ui-button:hover, .new-ui .ui-form .ui-button:focus {
  box-shadow: none;
  border-radius: 0.25rem;
}

.new-ui .ui-form .ui-button.button-alternate {
  background-color: #464956;
}

.new-ui .ui-form .ui-button.ui-action-icon {
  padding: 0;
}

.new-ui .ui-form .ui-button.button-cta {
  font-size: 2rem;
  font-weight: 300;
}

.new-ui .ui-form .ui-button.button-cta[class*="icon-"] {
  padding: 0 1.875em !important;
}

.new-ui .ui-form fieldset legend {
  color: #5d9bfc;
  font-weight: normal;
  font-size: 1.25rem;
}

.new-ui .ui-form fieldset h2,
.new-ui .ui-form fieldset h3,
.new-ui .ui-form fieldset h4,
.new-ui .ui-form fieldset legend {
  margin-bottom: 0.75em;
}

.new-ui .ui-form fieldset input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]) {
  width: 100%;
}

.new-ui .ui-form fieldset input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]).inline {
  width: auto;
}

.new-ui .ui-form fieldset select {
  width: 100%;
}

.new-ui .ui-form fieldset .fieldgroup + .new-ui .ui-form fieldset .fieldgroup {
  margin-bottom: 1rem;
}

.new-ui .ui-form fieldset.section {
  margin-bottom: 2.5rem;
}

.new-ui .ui-form fieldset.section.no-label {
  margin-bottom: 0;
}

.new-ui .ui-form fieldset.note {
  padding: 1.5rem 0;
}

.new-ui .ui-form fieldset.note *,
.new-ui .ui-form fieldset.note .type-note {
  font-size: 0.925rem;
  line-height: 1.35em;
}

.new-ui .ui-form fieldset.inline {
  height: 1.75rem;
  margin-right: 0.75rem;
  margin-top: 0;
}

.new-ui .ui-form fieldset.inline * {
  display: inline-block;
  vertical-align: middle;
}

.new-ui .ui-form fieldset.inline label {
  line-height: 1.75rem;
  margin: 0 0.5rem 0 0;
}

@media (max-width: 480px) {
  .new-ui .ui-form fieldset.section {
    margin-bottom: 1.5rem;
  }
}

.new-ui .ui-form fieldset + fieldset,
.new-ui .ui-form .ui-form-element + .ui-form-element {
  margin-top: 1.125em;
}

.new-ui .ui-form .form-section fieldset + fieldset {
  margin-top: 0;
}

.new-ui .ui-form input[type="submit"] {
  border: none;
}

.new-ui .ui-form input[type="hidden"] + label {
  margin-top: 0;
}

.new-ui .ui-form .ui-form-group {
  display: block;
  margin-top: 1.5rem;
}

.new-ui .ui-form .ui-btn-row > .ui-form-element {
  display: inline-block;
  vertical-align: top;
  margin: 0 !important;
  float: none !important;
  margin-right: 0.5rem !important;
}

.new-ui .ui-form .ui-btn-row > .ui-form-element:last-child {
  margin-right: 0rem !important;
}

.new-ui .ui-form .ui-form-select {
  display: inline-block;
  width: auto;
  height: 1.75rem;
  position: relative;
}

.new-ui .ui-form .ui-form-select label,
.new-ui .ui-form .ui-form-select select {
  vertical-align: middle;
}

.new-ui .ui-form .ui-form-select label {
  line-height: 1.75rem;
  margin: 0 0.5rem 0 0;
}

.new-ui .ui-form .ui-form-select select {
  width: auto;
  height: 1.75rem;
  background: none;
  background-color: transparent;
  box-shadow: none;
  border: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0.25rem;
  border: 1px solid #c1cfe6;
  padding: 0.25rem 1.25rem 0.25rem 0.5rem;
  font-size: 0.875rem;
  color: #5d759a;
  background: -moz-linear-gradient(top, #f4f8ff 0%, #f5f9ff 100%);
  background: -webkit-linear-gradient(top, #f4f8ff 0%, #f5f9ff 100%);
  background: linear-gradient(to bottom, #f5f9ff 0%, #f4f8ff 100%);
}

.new-ui .ui-form .ui-form-select select:hover, .new-ui .ui-form .ui-form-select select:focus {
  transition: all 0.15s ease-out;
  box-shadow: 0 0 0 3px rgba(193, 207, 230, 0.125);
}

.new-ui .ui-form .ui-form-select select:focus {
  border: 1px solid #c1cfe6;
  transition: all 0.15s ease-out;
  outline: none;
}

.new-ui .ui-form .ui-form-select:after {
  font-family: "glyphs";
  display: block;
  content: "";
  width: 1rem;
  height: 1rem;
  line-height: 1.25rem;
  font-size: 0.9rem;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.25);
  color: #5d9bfc;
  text-align: center;
  position: absolute;
  top: 50%;
  right: 0.35rem;
  margin-top: -0.5rem;
  z-index: 2;
}

.new-ui .ui-form.style-app input[type="text"],
.new-ui .ui-form.style-app input[type="search"],
.new-ui .ui-form.style-app input[type="password"],
.new-ui .ui-form.style-app input[type="email"],
.new-ui .ui-form.style-app input[type="phone"],
.new-ui .ui-form.style-app input[type="address"],
.new-ui .ui-form.style-app input[type="search"],
.new-ui .ui-form.style-app input[type="date"],
.new-ui .ui-form.style-app input[type="month"],
.new-ui .ui-form.style-app input[type="number"],
.new-ui .ui-form.style-app input[type="url"],
.new-ui .ui-form.style-app input[type="tel"],
.new-ui .ui-form.style-app input[type="range"],
.new-ui .ui-form.style-app input[type="datetime"],
.new-ui .ui-form.style-app input[type="week"],
.new-ui .ui-form.style-app textarea {
  background: #f4f8ff;
  color: #304565;
}

.new-ui .ui-form.style-app input[type="text"],
.new-ui .ui-form.style-app input[type="search"],
.new-ui .ui-form.style-app input[type="password"],
.new-ui .ui-form.style-app input[type="email"],
.new-ui .ui-form.style-app input[type="phone"],
.new-ui .ui-form.style-app input[type="address"],
.new-ui .ui-form.style-app input[type="search"],
.new-ui .ui-form.style-app input[type="date"],
.new-ui .ui-form.style-app input[type="month"],
.new-ui .ui-form.style-app input[type="number"],
.new-ui .ui-form.style-app input[type="url"],
.new-ui .ui-form.style-app input[type="tel"],
.new-ui .ui-form.style-app input[type="range"],
.new-ui .ui-form.style-app input[type="datetime"],
.new-ui .ui-form.style-app input[type="week"] {
  height: 1.75rem;
  padding: 0.25rem;
  font-size: 0.875rem;
}

.new-ui .ui-form.style-app input[type="date"],
.new-ui .ui-form.style-app input[type="datetime"] {
  max-width: 5.625rem;
}

.new-ui .ui-form.style-app label {
  color: #304565;
  font-size: 0.85rem;
}

.new-ui .ui-form.minified input[type="text"],
.new-ui .ui-form.minified input[type="search"],
.new-ui .ui-form.minified input[type="password"],
.new-ui .ui-form.minified input[type="email"],
.new-ui .ui-form.minified input[type="phone"],
.new-ui .ui-form.minified input[type="address"],
.new-ui .ui-form.minified input[type="search"],
.new-ui .ui-form.minified input[type="date"],
.new-ui .ui-form.minified input[type="month"],
.new-ui .ui-form.minified input[type="number"],
.new-ui .ui-form.minified input[type="url"],
.new-ui .ui-form.minified input[type="tel"],
.new-ui .ui-form.minified input[type="range"],
.new-ui .ui-form.minified input[type="datetime"],
.new-ui .ui-form.minified input[type="week"] {
  height: 1.75rem;
  padding: 0.25rem;
  font-size: 0.875rem;
}

.new-ui .alert.message {
  display: block;
  width: 100%;
  height: auto;
  padding: 0.5rem 0;
}

.new-ui .alert[class*="danger"] > input,
.new-ui .alert[class*="danger"] > textarea, .new-ui .alert[class*="error"] > input,
.new-ui .alert[class*="error"] > textarea {
  border-bottom: 2px solid #e26464;
}

.new-ui .alert[class*="danger"] .message, .new-ui .alert[class*="error"] .message {
  color: #e26464;
}

.new-ui .alert[class*="success"] > input,
.new-ui .alert[class*="success"] > textarea {
  border-bottom: 2px solid #50a475;
}

.new-ui .alert[class*="success"] .message {
  color: #50a475;
}

.new-ui .alert[class*="warning"] > input,
.new-ui .alert[class*="warning"] > textarea {
  border-bottom: 2px solid #f0d886;
}

.new-ui .alert[class*="warning"] .message {
  color: #f0d886;
}

.new-ui .form-validation {
  display: block;
  height: auto;
  line-height: 1.65em;
  box-sizing: border-box;
}

.new-ui .form-validation[class*="error"] {
  color: #f2f3f6;
}

.new-ui .form-validation[class*="warning"] {
  color: #eac959;
}

.new-ui .form-validation[class*="success"] {
  color: #50a475;
}

.new-ui .ui-input-mix > *,
.new-ui .ui-input-mix .ui-date,
.new-ui .ui-input-mix .ui-button,
.new-ui .ui-input-mix .ui-label,
.new-ui .ui-input-mix .ui-input-text,
.new-ui .ui-input-mix .ui-dropdown,
.new-ui .ui-input-mix .ui-input-mix-item {
  display: inline-block;
  vertical-align: top;
  height: 2.25rem;
  line-height: 2.25rem;
  margin-left: -5px;
}

.new-ui .ui-input-mix > * input,
.new-ui .ui-input-mix .ui-date input,
.new-ui .ui-input-mix .ui-button input,
.new-ui .ui-input-mix .ui-label input,
.new-ui .ui-input-mix .ui-input-text input,
.new-ui .ui-input-mix .ui-dropdown input,
.new-ui .ui-input-mix .ui-input-mix-item input {
  height: 2.25rem;
}

.new-ui .ui-input-mix > *:first-child,
.new-ui .ui-input-mix .ui-date:first-child,
.new-ui .ui-input-mix .ui-button:first-child,
.new-ui .ui-input-mix .ui-label:first-child,
.new-ui .ui-input-mix .ui-input-text:first-child,
.new-ui .ui-input-mix .ui-dropdown:first-child,
.new-ui .ui-input-mix .ui-input-mix-item:first-child {
  margin-left: 0;
}

.new-ui .ui-input-mix .ui-button {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  background: #f2f3f6;
  background: -moz-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: -webkit-linear-gradient(top, #f5f9ff 0%, #FFF 100%);
  background: linear-gradient(to bottom, #FFF 0%, #f5f9ff 100%);
  padding-left: 0.5em;
  padding-right: 0.5em;
  color: #758ba9;
}

.new-ui .ui-input-mix .ui-button.no-radius-left {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.new-ui .ui-input-mix .ui-button.no-radius-right {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.new-ui .ui-input-mix .ui-button:before {
  color: #6da7ff;
}

.new-ui .ui-input-mix .ui-label {
  padding-left: 0.5em;
  padding-right: 0.5em;
  font-weight: 600;
}

.new-ui .ui-input-mix .ui-input-text {
  height: 2.25rem;
}

.new-ui .ui-input-mix .ui-input-text input {
  display: inline-block;
  width: auto;
  height: 2.25rem;
}

.new-ui .ui-input-mix .ui-input-text.string {
  width: auto;
  min-width: 8rem;
}

.new-ui .ui-input-mix .ui-input-text.string input {
  min-width: 12rem;
}

.new-ui .ui-input-mix .ui-input-text.data {
  width: auto;
  min-width: 3.5rem;
}

.new-ui .ui-input-mix .ui-input-text.data input {
  width: 3.5rem;
  min-width: 3.5rem;
}

.new-ui .ui-input-mix .ui-dropdown {
  height: 2.25rem;
}

.new-ui .ui-input-mix .ui-dropdown .dropdown-toggle {
  height: 2.25rem;
}

.new-ui .ui-input-dynamic {
  position: relative;
}

.new-ui .ui-input-dynamic input[type="text"] {
  padding-right: 6rem;
}

.new-ui .ui-input-dynamic .ui-button {
  height: 100%;
  background: #5d9bfc;
  color: #FFF;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border: 1px solid #3378e3;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  margin: 0 !important;
  font-weight: 600;
}

.new-ui .ui-input-dynamic .ui-button:hover {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  background: #6da7ff;
}

.new-ui .list-blocks > div,
.new-ui .list-blocks > li,
.new-ui .list-blocks .list-item {
  clear: both;
  text-align: left;
  position: relative;
  border-bottom: 1px solid #c1cfe6;
}

.new-ui .list-blocks > div:after,
.new-ui .list-blocks > li:after,
.new-ui .list-blocks .list-item:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .list-blocks > div .stat-count,
.new-ui .list-blocks > li .stat-count,
.new-ui .list-blocks .list-item .stat-count {
  display: block;
  float: right;
  font-weight: 600;
  color: black;
}

.new-ui .list-blocks > div:last-child:not(.list-item),
.new-ui .list-blocks > li:last-child:not(.list-item),
.new-ui .list-blocks .list-item:last-child:not(.list-item) {
  margin-bottom: 0;
  border-bottom: 0;
}

.new-ui .list-blocks > div h1,
.new-ui .list-blocks > div h2,
.new-ui .list-blocks > div h3,
.new-ui .list-blocks > div h4,
.new-ui .list-blocks > div p,
.new-ui .list-blocks > li h1,
.new-ui .list-blocks > li h2,
.new-ui .list-blocks > li h3,
.new-ui .list-blocks > li h4,
.new-ui .list-blocks > li p,
.new-ui .list-blocks .list-item h1,
.new-ui .list-blocks .list-item h2,
.new-ui .list-blocks .list-item h3,
.new-ui .list-blocks .list-item h4,
.new-ui .list-blocks .list-item p {
  display: block;
  text-align: left;
}

.new-ui .list-blocks > div .panel-close,
.new-ui .list-blocks > li .panel-close,
.new-ui .list-blocks .list-item .panel-close {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  margin-top: -0.5rem;
}

.new-ui .list-blocks > div.is-active, .new-ui .list-blocks > div.active,
.new-ui .list-blocks > li.is-active,
.new-ui .list-blocks > li.active,
.new-ui .list-blocks .list-item.is-active,
.new-ui .list-blocks .list-item.active {
  background: #5d9bfc;
  color: #FFF;
  transition: all 0.2s ease-out;
}

.new-ui .list-blocks > div.is-active *,
.new-ui .list-blocks > div.is-active .stat-count, .new-ui .list-blocks > div.active *,
.new-ui .list-blocks > div.active .stat-count,
.new-ui .list-blocks > li.is-active *,
.new-ui .list-blocks > li.is-active .stat-count,
.new-ui .list-blocks > li.active *,
.new-ui .list-blocks > li.active .stat-count,
.new-ui .list-blocks .list-item.is-active *,
.new-ui .list-blocks .list-item.is-active .stat-count,
.new-ui .list-blocks .list-item.active *,
.new-ui .list-blocks .list-item.active .stat-count {
  color: #FFF;
  transition: all 0.2s ease-out;
}

.new-ui .list-blocks > div,
.new-ui .list-blocks > li {
  padding: 0.5rem 0;
}

.new-ui .list-blocks.small > div,
.new-ui .list-blocks.small > li {
  padding: 0.65rem 0;
  font-weight: normal;
}

.new-ui .list-blocks.large > div,
.new-ui .list-blocks.large > li {
  padding: 1rem 0.75rem;
  font-size: 1rem;
}

.new-ui .list-blocks.checkbox > div,
.new-ui .list-blocks.checkbox > li {
  padding: 0.75rem 0;
  padding-left: 2.25rem;
}

.new-ui .list-blocks.checkbox > div *,
.new-ui .list-blocks.checkbox > li * {
  vertical-align: middle;
}

.new-ui .list-blocks.checkbox > div .ui-checkbox,
.new-ui .list-blocks.checkbox > li .ui-checkbox {
  width: 2.25rem;
  height: 2.25rem;
  position: absolute;
  top: 50%;
  left: 0;
  margin-top: -0.75rem;
}

.new-ui .list-blocks.checkbox > div .menu-drop,
.new-ui .list-blocks.checkbox > li .menu-drop {
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -12px;
}

.new-ui .list-blocks.checkbox > div .tools,
.new-ui .list-blocks.checkbox > li .tools {
  height: 1rem;
  line-height: 1rem;
}

.new-ui .list-blocks.checkbox > div .tools > li,
.new-ui .list-blocks.checkbox > div .tools > .ui-action,
.new-ui .list-blocks.checkbox > li .tools > li,
.new-ui .list-blocks.checkbox > li .tools > .ui-action {
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
}

.new-ui .list-blocks.checkbox > div .tools > li:before,
.new-ui .list-blocks.checkbox > div .tools > .ui-action:before,
.new-ui .list-blocks.checkbox > li .tools > li:before,
.new-ui .list-blocks.checkbox > li .tools > .ui-action:before {
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  font-size: 1.275rem;
}

.new-ui .list-steps {
  counter-reset: item;
}

.new-ui .list-steps li {
  font-size: 1.25rem;
  color: #5d9bfc;
  padding-left: 2.75rem;
  margin-bottom: 2rem;
  position: relative;
  counter-increment: item;
}

.new-ui .list-steps li:before {
  width: 2.15rem;
  height: 2.15rem;
  border-radius: 100%;
  box-shadow: inset 0 0 0 2px #5d9bfc;
  text-align: center;
  line-height: 1.875rem;
  font-size: 1.45rem;
  top: -6px;
  left: -5px;
  content: counter(item);
}

.new-ui .list-steps li.checked:before {
  color: rgba(255, 255, 255, 0);
}

.new-ui .list-steps li.checked:after {
  font-family: "glyphs";
  content: "";
  width: 2.15rem;
  height: 2.15rem;
  color: #5d9bfc;
  line-height: 1.875rem;
  font-size: 2.975rem;
  top: -4px;
  left: -0.75rem;
  z-index: 2;
}

@media (max-width: 1024px) {
  .new-ui .list-steps li {
    font-size: 1rem;
    padding-left: 1.75rem;
    margin-bottom: 1rem;
  }
  .new-ui .list-steps li:before {
    width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
    font-size: 1rem;
    box-shadow: inset 0 0 0 1px #5d9bfc;
    left: -6px;
    top: 0;
  }
  .new-ui .list-steps li.checked:before {
    box-shadow: inset 0 0 0 3px #5d9bfc;
  }
  .new-ui .list-steps li.checked:after {
    width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
    font-size: 2rem;
    top: 0px;
    left: -10px;
  }
}

.new-ui .list-links li {
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
  text-align: left;
}

.new-ui .list-links li:before {
  font-family: "glyphs";
  width: 0.5rem;
  height: 1.25rem;
  content: "";
  color: #5d9bfc;
  position: absolute;
  top: 0;
  left: -3px;
  text-align: left;
}

.new-ui .list-social li {
  margin-right: 0.25rem;
  width: 2rem;
  height: 2rem;
  position: relative;
  cursor: pointer;
}

.new-ui .list-social li a {
  display: block;
  width: 2rem;
  height: 2rem;
  font-size: 0;
  color: transparent;
}

.new-ui .list-social li a:before {
  display: block;
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
  font-size: 0.875rem;
  color: white;
  text-indent: 2px;
}

.new-ui .list-social li:hover a:before {
  color: #5d9bfc;
  transition: all 0.2s ease-out;
}

.new-ui .list-notifications > li,
.new-ui .list-todo > li {
  margin-bottom: 0.65rem;
  padding-left: 1rem;
  font-size: 0.875rem;
  position: relative;
}

.new-ui .list-notifications > li[class*="ui-marker-"]:after,
.new-ui .list-todo > li[class*="ui-marker-"]:after {
  position: absolute;
  top: 0.5rem;
  left: 0;
}

.new-ui .list-todo > li {
  padding-left: 1.75rem;
}

.new-ui .list-todo > li .title {
  cursor: pointer;
  font-size: 0.9rem;
  text-transform: none;
}

.new-ui .list-todo > li .title:hover {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui .list-todo > li .message {
  font-style: italic;
  color: #a7b8d6;
}

.new-ui .list-todo > li .ui-count,
.new-ui .list-todo > li .ui-marker-alert,
.new-ui .list-todo > li .action {
  position: absolute;
  top: 0.25rem;
  left: 0;
  cursor: pointer;
  background-color: #c1cfe6;
}

.new-ui .list-todo > li .ui-count:before,
.new-ui .list-todo > li .ui-marker-alert:before,
.new-ui .list-todo > li .action:before {
  display: inline-block;
  color: #FFF;
  font-size: 0.9rem;
  line-height: 1.15rem;
  text-indent: 2.5px;
}

.new-ui .list-todo > li .ui-info {
  position: absolute;
  top: 0.25rem;
  left: 0;
  cursor: default;
  background: none;
}

.new-ui .list-todo > li .ui-info:before {
  display: inline-block;
  color: #5d9bfc;
  font-size: 0.9rem;
  line-height: 1.15rem;
  text-indent: 2px;
  font-family: "glyphs";
  content: "";
}

.new-ui .list-todo > li.automation-is-active {
  padding: 0.75rem;
  margin-bottom: 1.5rem;
  background-color: #50a475;
  color: #FFF;
  border-radius: 0.25rem;
  box-shadow: inset 0 0 0 1px #368157;
}

.new-ui .list-todo > li.automation-is-active .title,
.new-ui .list-todo > li.automation-is-active .message {
  color: #FFF;
}

.new-ui .list-todo > li.automation-is-active .ui-count,
.new-ui .list-todo > li.automation-is-active .ui-marker-alert,
.new-ui .list-todo > li.automation-is-active .action {
  display: none;
  background-color: #FFF;
}

.new-ui .list-todo > li.automation-is-active .ui-count:before,
.new-ui .list-todo > li.automation-is-active .ui-marker-alert:before,
.new-ui .list-todo > li.automation-is-active .action:before {
  color: #50a475;
}

.new-ui .list-todo > li.automation-is-active:hover .title,
.new-ui .list-todo > li.automation-is-active:hover .message {
  color: #FFF;
}

.new-ui .list-todo > li:hover {
  background-color: #f4f8ff;
  box-shadow: 0 0 0 6px #f4f8ff;
  padding-left: 2.25rem;
  transition: all 0.15s ease-out;
}

.new-ui .list-todo > li:hover .ui-count {
  background-color: #5d9bfc;
  transition: all 0.15s ease-out;
  left: 0.5rem;
}

.new-ui .list-tools {
  display: inline-block;
  width: auto;
  height: auto;
}

.new-ui .list-tools li,
.new-ui .list-tools .ui-tool {
  display: inline-block;
  margin-right: 0.5rem;
  cursor: pointer;
}

.new-ui .list-tools li:last-child,
.new-ui .list-tools .ui-tool:last-child {
  margin-right: 0;
}

.new-ui .list-tools li:before,
.new-ui .list-tools .ui-tool:before {
  color: #c1cfe6;
}

.new-ui .list-tools li:hover:before,
.new-ui .list-tools .ui-tool:hover:before {
  color: #5d9bfc;
  transition: color 0.15s ease-out;
}

.new-ui .list-tools.small li,
.new-ui .list-tools.small .ui-tool {
  font-size: 1.25rem;
}

.new-ui .list-tools.medium li,
.new-ui .list-tools.medium .ui-tool {
  font-size: 1.5rem;
}

.new-ui .list-tools.large li,
.new-ui .list-tools.large .ui-tool {
  font-size: 2rem;
}

.new-ui .list-tools.xlarge li,
.new-ui .list-tools.xlarge .ui-tool {
  font-size: 3rem;
}

.new-ui .block-list > li {
  background: #FFF;
  border: 1px solid #c1cfe6;
  border-radius: 0.25rem;
  display: block;
  padding: 0.65rem 1.25rem;
  margin-bottom: 0.5rem;
  box-shadow: inset 0 0 0 1px rgba(128, 178, 255, 0.75);
  position: relative;
}

.new-ui .block-list > li p {
  margin: 0;
}

.new-ui .block-list > li .date {
  font-size: 0.825rem;
  font-weight: 600;
  color: #bababa;
}

.new-ui .block-list > li .name {
  color: #505050;
}

.new-ui .block-list > li[class*="icon-"]:before {
  width: 1.5rem;
  height: 1.5rem;
  margin-top: -0.75rem;
  font-size: 1.25rem;
  line-height: 1.5rem;
  color: #80b2ff;
  text-align: center;
  position: absolute;
  right: 0.5rem;
  top: 50%;
}

.new-ui .block-list > li *[class*="ui-tag"] {
  position: absolute;
  top: 0.75rem;
  right: 0.5rem;
}

.new-ui .block-list > li *[class*="ui-tag"].tag-role {
  position: static;
}

.new-ui .block-list > li .tag-lists,
.new-ui .block-list > li .list-tags {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  margin-top: -0.75rem;
}

.new-ui .block-list > li.role {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.new-ui .block-list > li.add-block {
  height: 3.125rem;
  border: 2px dashed #80b2ff;
  box-shadow: none;
  text-align: center;
}

.new-ui .block-list > li.add-block:before {
  display: inline-block;
  width: 2rem;
  font-family: "glyphs";
  content: "";
  height: 1em;
  font-size: 2.5rem;
  line-height: 3.125rem;
  color: #80b2ff;
  top: 0;
  right: 0;
  margin-top: -10px;
}

.new-ui .block-list > li.add-block:hover {
  background: rgba(168, 201, 252, 0.85);
  transition: all 0.1s ease-out;
}

.new-ui .block-list > li:last-of-type {
  margin-bottom: 0;
}

.new-ui #main-header,
.new-ui #main-content {
  display: block;
}

.new-ui #main-header {
  clear: both;
  width: 100%;
  height: 4rem;
  padding: 0 2.5rem;
  background: #1c2c3e;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
}

.new-ui #main-header:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui #main-content {
  width: 100%;
  margin: 0 auto;
  height: auto;
  position: relative;
  padding-top: 4rem;
}

.new-ui #main-content.sidebar-is-active {
  padding-right: 20rem;
}

h1 + .new-ui *,
h2 + .new-ui * {
  margin-top: 1.5rem;
}

h3 + .new-ui *,
h4 + .new-ui *,
h5 + .new-ui * {
  margin-top: 1.25rem;
}

.new-ui ul,
.new-ui ol {
  list-style: none;
  text-indent: 0;
}

.new-ui ul.list,
.new-ui ol.list {
  margin-top: 1em;
}

.new-ui ul.list li,
.new-ui ol.list li {
  position: relative;
  margin-bottom: 6px;
  padding-left: 14px;
}

.new-ui ul.list li:before,
.new-ui ol.list li:before {
  position: absolute;
  top: 0;
  left: 0;
  display: inline;
  margin: 0;
  padding: 0;
  width: auto;
  color: #bababa;
}

.new-ui ul.horizontal li,
.new-ui ol.horizontal li {
  display: inline-block;
}

.new-ui ul.list li {
  display: block;
}

.new-ui ul.list li:before {
  content: "•";
  color: #5d9bfc;
}

.new-ui ul.list.checkmark li {
  padding-left: 2rem;
  margin-bottom: 1.5rem;
}

.new-ui ul.list.checkmark li:before {
  font-family: "glyphs";
  content: "";
}

.new-ui ul.list.checkmark.green li:before {
  color: greenyellow;
  text-shadow: 2px 2px 1px rgba(0, 128, 0, 0.35);
}

.new-ui ol.list {
  counter-reset: item;
}

.new-ui ol.list > li:before {
  content: counter(item) ".";
  counter-increment: item;
  color: #5d9bfc;
}

.new-ui figure,
.new-ui .asset-frame {
  border-radius: 0.25rem;
  overflow: hidden;
}

.new-ui figure img,
.new-ui .asset-frame img {
  width: 100%;
  height: auto;
}

.new-ui figure.video,
.new-ui .asset-frame.video {
  position: relative;
}

.new-ui figure.video:before,
.new-ui .asset-frame.video:before {
  display: block;
  content: "";
  width: 100%;
  padding-top: 56.25%;
}

.new-ui figure.video > img,
.new-ui figure.video > .media,
.new-ui .asset-frame.video > img,
.new-ui .asset-frame.video > .media {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.new-ui figure.old-video,
.new-ui .asset-frame.old-video {
  position: relative;
}

.new-ui figure.old-video:before,
.new-ui .asset-frame.old-video:before {
  display: block;
  content: "";
  width: 100%;
  padding-top: 75%;
}

.new-ui figure.old-video > img,
.new-ui figure.old-video > .media,
.new-ui .asset-frame.old-video > img,
.new-ui .asset-frame.old-video > .media {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.new-ui figure.square,
.new-ui .asset-frame.square {
  position: relative;
}

.new-ui figure.square:before,
.new-ui .asset-frame.square:before {
  display: block;
  content: "";
  width: 100%;
  padding-top: 100%;
}

.new-ui figure.square > img,
.new-ui figure.square > .media,
.new-ui .asset-frame.square > img,
.new-ui .asset-frame.square > .media {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.new-ui .asset-frame {
  border-radius: 0.25rem;
}

.new-ui .place-right {
  float: right;
}

.new-ui .place-left {
  float: left;
}

.new-ui .page-content,
.new-ui .page-header {
  width: 100%;
  margin: 0 auto;
}

.new-ui .page-header {
  height: auto;
  position: relative;
  padding: 4rem;
  border: none;
}

.new-ui .page-header > h1 {
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  color: #304565;
  font-size: 1.85rem;
  line-height: 1.1em;
  font-weight: 600;
}

.new-ui .page-content {
  position: relative;
}

.new-ui .page-content.fullwidth {
  max-width: 80vw;
}

.new-ui .page-content > header {
  padding-top: 3rem;
  padding-bottom: 4rem;
}

.new-ui .page-content > article h3 {
  font-size: 1.35rem;
  font-weight: 400;
  color: #5d9bfc;
}

.new-ui .page-content > article h3.title-elegant {
  font-size: 2rem;
  font-weight: 600;
}

.new-ui .page-content > article p + h3 {
  margin-top: 1.5rem;
}

.new-ui .page-content .contact-form {
  width: 60%;
  margin: 2rem auto;
}

.new-ui .page-content .login-box {
  width: 60%;
  margin: 0 auto 4rem auto;
  border-radius: 0.25rem;
  padding: 2rem;
  background: #a8c9fc;
}

@media (max-width: 800px) {
  .new-ui .page-content .login-box,
  .new-ui .page-content .contact-form {
    width: 80%;
  }
}

@media (max-width: 600px) {
  .new-ui .page-content .login-box,
  .new-ui .page-content .contact-form {
    width: 90%;
  }
}

@media (max-width: 500px) {
  .new-ui .page-content .login-box,
  .new-ui .page-content .contact-form {
    width: 100%;
  }
}

.new-ui .page-sidebar {
  width: 0;
  height: 100vh;
  padding: 0;
  position: fixed;
  top: 0;
  right: 0;
  border-left: 1px solid #d8e1f0;
  background: rgba(255, 255, 255, 0.925);
  z-index: 1000;
}

.new-ui .page-sidebar .sidebar-header {
  position: relative;
  display: none;
  padding: 2.5rem 0 1rem 0;
  margin-bottom: 1.5rem;
}

.new-ui .page-sidebar .sidebar-header h2,
.new-ui .page-sidebar .sidebar-header h3 {
  font-size: 1.25rem;
}

.new-ui .page-sidebar .sidebar-header .sidebar-close {
  position: absolute;
  top: -0.5rem;
  left: -1.5rem;
}

.new-ui .page-sidebar .sidebar-header .sidebar-close:before {
  font-size: 1rem;
}

.new-ui .page-sidebar .sidebar-tag {
  display: block;
  width: 3.5rem;
  height: 3.5rem;
  border-top-left-radius: 0.1875rem;
  border-bottom-left-radius: 0.1875rem;
  border: 1px solid #d8e1f0;
  background-color: #eff5ff;
  position: absolute;
  top: 50%;
  left: -3.5rem;
  z-index: 100;
  cursor: pointer;
}

.new-ui .page-sidebar .sidebar-tag.notification:before {
  font-family: "glyphs";
  content: "";
  display: block;
  width: 3.5rem;
  height: 3.5rem;
  font-size: 2rem;
  line-height: 3.5rem;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  color: #5d9bfc;
}

.new-ui .page-sidebar .sidebar-tag.notification .ui-count {
  position: absolute;
  top: -8px;
  left: -8px;
  z-index: 20;
  background-color: #e26464;
}

.new-ui .page-sidebar .sidebar-content {
  display: none;
}

.new-ui .page-sidebar .ui-box.notification.klicky {
  margin-top: 3rem;
}

.new-ui .page-sidebar.is-active {
  width: 20rem;
  padding: 8rem 1.75rem 2.5rem 1.75rem;
}

.new-ui .page-sidebar.is-active .sidebar-header,
.new-ui .page-sidebar.is-active .sidebar-content {
  display: block;
}

.new-ui .page-sidebar.is-active .scrollable .list-todo {
  margin-bottom: 5rem;
}

.new-ui .page-section {
  width: 100%;
  max-width: 90rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 4rem;
  padding-right: 4rem;
}

.new-ui .page-section > h1,
.new-ui .page-section > h2,
.new-ui .page-section > header {
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  font-size: 1.475rem;
  line-height: 1.25em;
  font-weight: 400;
  color: #2f313a;
}

.new-ui .page-section > header {
  padding-bottom: 0.5rem;
}

.new-ui .page-section > header h1 {
  font-size: 1.5rem;
}

.new-ui .page-section > header h2 {
  font-size: 1.15rem;
}

.new-ui .page-section article > header {
  margin-bottom: 2.5rem;
}

.new-ui .page-section article + article {
  margin-top: 1.5rem;
}

.new-ui .page-section .page-section-content p {
  font-size: 1.125rem;
  line-height: 1.65em;
}

.new-ui .page-section .page-section-content.grid article h3,
.new-ui .page-section .page-section-content .grid article h3 {
  font-size: 1.2rem;
}

.new-ui .page-section .page-section-content.grid article p,
.new-ui .page-section .page-section-content .grid article p {
  margin-top: 0.5rem;
  text-align: justify;
}

.new-ui .page-section .page-section-content article > header {
  margin-bottom: 2.5rem;
}

.new-ui .page-section .section-content > header h2 {
  font-size: 1.25rem;
  text-shadow: 2px 2px 0 #FFF;
  color: #2f313a;
}

.new-ui .page-section .section-content + .section-content {
  margin-top: 2rem;
}

.new-ui .page-section.full-image, .new-ui .page-section.full-width, .new-ui .page-section.fullwidth {
  width: 100%;
  max-width: 100%;
  position: relative;
  padding-top: 8rem;
  padding-bottom: 8rem;
  padding-left: calc(((100% - 90rem)/2) + 4rem);
  padding-right: calc(((100% - 90rem)/2) + 4rem);
}

.new-ui .page-section.full-image > article, .new-ui .page-section.full-width > article, .new-ui .page-section.fullwidth > article {
  text-align: center;
}

.new-ui .page-section.full-image > blockquote, .new-ui .page-section.full-width > blockquote, .new-ui .page-section.fullwidth > blockquote {
  font-family: "Montserrat", "Lucida Sans Unicode", "Lucida Grande", sans-serif;
  padding-top: 2rem;
  position: relative;
}

.new-ui .page-section.full-image > blockquote:before, .new-ui .page-section.full-width > blockquote:before, .new-ui .page-section.fullwidth > blockquote:before {
  display: block;
  content: "";
  font-family: "glyphs";
  font-size: 2.75rem;
  width: 3rem;
  height: 3rem;
  color: #5d9bfc;
  position: absolute;
  top: -1rem;
  left: -2rem;
}

.new-ui .page-section.full-image > blockquote h1,
.new-ui .page-section.full-image > blockquote h2, .new-ui .page-section.full-width > blockquote h1,
.new-ui .page-section.full-width > blockquote h2, .new-ui .page-section.fullwidth > blockquote h1,
.new-ui .page-section.fullwidth > blockquote h2 {
  color: #5d9bfc;
  font-size: 1.5rem;
  font-weight: 400;
}

.new-ui .page-section.full-image > blockquote p, .new-ui .page-section.full-width > blockquote p, .new-ui .page-section.fullwidth > blockquote p {
  color: #FFF;
  font-size: 1.25rem;
  line-height: 1.35em;
}

.new-ui .page-section.full-image > blockquote footer cite, .new-ui .page-section.full-width > blockquote footer cite, .new-ui .page-section.fullwidth > blockquote footer cite {
  color: #5d9bfc;
  font-size: 0.9rem;
  line-height: 1.125em;
}

.new-ui .page-section.full-image > .background-image, .new-ui .page-section.full-width > .background-image, .new-ui .page-section.fullwidth > .background-image {
  display: block;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.new-ui .page-section.content-block > article {
  margin-left: auto;
  margin-right: auto;
}

.new-ui .page-section.single-column {
  padding-left: 4rem;
  padding-right: 4rem;
  padding-bottom: 5rem;
  text-align: left;
}

.new-ui .page-section.single-column > header {
  text-align: left;
  padding-top: 3rem;
}

.new-ui .page-section.single-column > article h3 + p {
  margin-top: 0.75rem;
}

.new-ui .page-section.single-column article + article {
  margin-top: 3rem;
}

.new-ui .page-section.tab-content {
  display: none;
}

.new-ui .page-section.tab-content.active, .new-ui .page-section.tab-content.is-active {
  display: block;
}

.new-ui .page-section:not(.tab-content) + .new-ui .page-section:not(.tab-content) {
  margin-top: 5rem;
}

.new-ui .page-section.full-image + .new-ui .page-section.full-image {
  margin-top: 0;
}

.new-ui .section-header {
  margin-bottom: 2.5rem;
}

.new-ui .section-header > h1 {
  font-size: 1.75rem;
  line-height: 1.25em;
  color: #5d9bfc;
}

.new-ui .section-header > h1 .search-term {
  font-weight: 500;
}

.new-ui .jsfiddle-embed {
  border-radius: 0.25rem;
  border: 1px solid #c1cfe6;
}

.new-ui .cursor-default {
  cursor: default !important;
}

.new-ui .landing {
  background-color: #659df4;
}

.new-ui .landing .list {
  text-align: left;
}

.new-ui .landing .list li {
  color: #FFF;
}

.new-ui .landing .list li:before {
  color: #FFF;
}

.new-ui .landing .video-play {
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
  transform: scale(1);
  display: block;
  width: 9rem;
  height: 6.25rem;
  font-size: 0;
  transition: transform 0.2s ease-out;
  background-image: url(https://assets.klicktipp.com/content_includes/affiliate/youtube-play.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
}

.new-ui .landing .video-play:hover {
  -moz-transform: scale(1.25);
  -o-transform: scale(1.25);
  -ms-transform: scale(1.25);
  -webkit-transform: scale(1.25);
  transform: scale(1.25);
  transition: transform 0.15s ease-in;
}

.new-ui .landing .page-header {
  text-align: center;
  padding-top: 2.5rem;
  padding-bottom: 0;
}

.new-ui .landing .page-header .page-title {
  display: block;
  width: auto;
  margin: 0.5em auto 0.35em auto;
  font-size: 3rem;
  line-height: 1.3em;
  color: #1c2c3e;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head {
  clear: both;
  padding-bottom: 4rem;
  padding-left: 0;
  padding-right: 0;
  width: auto;
  max-width: 70vw;
  margin-left: auto;
  margin-right: auto;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head .content {
  float: left;
  width: 60%;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head .page-title {
  max-width: auto;
  margin-left: 0;
  font-size: 5rem;
  line-height: 0.925em;
  text-transform: uppercase;
  text-align: left;
  font-weight: 300;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head .page-title span {
  display: block;
  font-weight: 900;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head h2 {
  color: #FFF;
  font-size: 2.75rem;
  line-height: 0.975em;
  text-align: left;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head p {
  width: 80%;
  color: #FFF;
  font-size: 1.125rem;
  line-height: 1.35em;
  text-align: left;
  margin-top: 2.25rem;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head figure {
  float: right;
  width: 40%;
  text-align: center;
}

.new-ui .landing .page-header.landing-marketing-cockpit-head figure img {
  width: 90%;
  height: auto;
}

.new-ui .landing .page-content h1 {
  color: #1c2c3e;
  font-size: 2.75rem;
  line-height: 1.4em;
}

.new-ui .landing .page-content h2 {
  color: #FFF;
  font-size: 2.25rem;
  line-height: 1.3em;
  text-align: center;
}

.new-ui .landing .page-content h3 {
  color: #1c2c3e;
  font-size: 1.65rem;
  line-height: 1.3em;
}

.new-ui .landing .page-content .main-video {
  position: relative;
}

.new-ui .landing .page-content .main-video figure {
  text-align: center;
  height: auto;
}

.new-ui .landing .page-content .main-video figure img {
  width: 75%;
  height: auto;
}

.new-ui .landing .page-content .main-video .video-play {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -7rem 0 0 -4.5rem;
}

.new-ui .landing .page-content .button-cta {
  margin: 1.15em auto 0.85em auto;
  padding-right: 2.5em !important;
}

.new-ui .landing .page-content .button-cta:before {
  display: block;
  font-family: "glyphs";
  content: "";
  width: 1em;
  height: 1em;
  font-size: 1em;
  line-height: 0.8em;
  position: absolute;
  top: 50%;
  right: 2rem;
  margin-top: -0.5em;
}

.new-ui .landing .page-content .button-cta.medium:before {
  line-height: 1em;
}

.new-ui .landing .page-content .cta-note {
  text-align: center;
  font-size: 1rem;
  font-style: italic;
  margin-top: -1rem;
}

.new-ui .landing .page-section {
  clear: both;
  padding: 5rem 3rem;
  position: relative;
}

.new-ui .landing .page-section:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.new-ui .landing .page-section > header {
  width: auto;
  max-width: 70vw;
  padding-bottom: 3rem;
  margin: 0 auto 3rem auto;
  position: relative;
  text-align: center;
}

.new-ui .landing .page-section > header:before {
  display: block;
  width: 12rem;
  height: 4px;
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  background: rgba(0, 0, 0, 0.25);
  margin-left: -6rem;
}

.new-ui .landing .page-section > header h1 {
  display: block;
  width: 85%;
  margin: 0 auto 1.25rem auto;
  font-size: 2.85rem;
  line-height: 1.275em;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: -1px;
}

.new-ui .landing .page-section > header h2 {
  display: block;
  width: 85%;
  margin: 0 auto;
  color: #1c2c3e;
  font-size: 2.5rem;
  line-height: 1.35em;
  font-weight: 500;
  letter-spacing: -1px;
  text-shadow: none;
}

.new-ui .landing .page-section > header h3 {
  display: block;
  width: 85%;
  margin: 0 auto;
  color: #FFF;
}

.new-ui .landing .page-section.pointer-down:before {
  -moz-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  display: block;
  content: "";
  width: 2.5rem;
  height: 2.5rem;
  background: inherit;
  position: absolute;
  bottom: -1.25rem;
  left: 50%;
  margin-left: -1.25rem;
  z-index: 5;
}

.new-ui .landing .page-section + .new-ui .landing .page-section {
  margin-top: 0;
}

.new-ui .landing .page-section p,
.new-ui .landing .page-section ul,
.new-ui .landing .page-section ol {
  color: #FFF;
  font-size: 1.325rem;
  line-height: 1.4em;
  text-align: left;
}

.new-ui .landing .page-section p a,
.new-ui .landing .page-section ul a,
.new-ui .landing .page-section ol a {
  color: #fff;
  text-decoration: underline;
  font-weight: 600;
}

.new-ui .landing .page-section p a:hover,
.new-ui .landing .page-section ul a:hover,
.new-ui .landing .page-section ol a:hover {
  text-decoration: none;
  transition: all 0.2s ease-out;
}

.new-ui .landing .page-section p strong,
.new-ui .landing .page-section ul strong,
.new-ui .landing .page-section ol strong {
  font-weight: 800;
}

.new-ui .landing .page-section p em,
.new-ui .landing .page-section ul em,
.new-ui .landing .page-section ol em {
  font-style: italic;
}

.new-ui .landing .page-section p + p {
  margin-top: 1rem;
}

.new-ui .landing .page-section .video {
  position: relative;
}

.new-ui .landing .page-section .video .video-play {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -3.125rem 0 0 -4.5rem;
  cursor: pointer;
}

.new-ui .landing .page-section .video-thumbnail {
  width: 55%;
  height: auto;
  padding: 0;
  margin: 0 auto 2.85rem auto;
}

.new-ui .landing .page-section .video-thumbnail img {
  border: 16px solid #FFF;
  border-radius: 0.25rem;
  box-shadow: 0 8px 0 -4px rgba(0, 0, 0, 0.125);
}

.new-ui .landing .page-section .info {
  width: 80%;
  margin: 0 auto;
}

.new-ui .landing .page-section .info h2 {
  text-align: left;
}

.new-ui .landing .page-section .info h3 {
  text-align: left;
  font-weight: 500;
  margin-bottom: 1.5rem;
}

.new-ui .landing .page-section .info p,
.new-ui .landing .page-section .info ul,
.new-ui .landing .page-section .info ol {
  color: #FFF;
  text-align: left;
}

.new-ui .landing .page-section .info p + p {
  margin-top: 1rem;
}

.new-ui .landing .page-section .columnize.justify p {
  text-align: justify;
}

.new-ui .landing .page-section.bg-white > header h1,
.new-ui .landing .page-section.bg-white > header h2, .new-ui .landing .page-section.bg-bg-main-medium > header h1,
.new-ui .landing .page-section.bg-bg-main-medium > header h2 {
  color: #3378e3;
}

.new-ui .landing .page-section.bg-white > header h3, .new-ui .landing .page-section.bg-bg-main-medium > header h3 {
  color: #5d759a;
}

.new-ui .landing .page-section.bg-white > header:before, .new-ui .landing .page-section.bg-bg-main-medium > header:before {
  background: #a8c9fc;
}

.new-ui .landing .page-section.bg-white .info p,
.new-ui .landing .page-section.bg-white .info ul,
.new-ui .landing .page-section.bg-white .info ol, .new-ui .landing .page-section.bg-bg-main-medium .info p,
.new-ui .landing .page-section.bg-bg-main-medium .info ul,
.new-ui .landing .page-section.bg-bg-main-medium .info ol {
  color: #5d759a;
  text-align: left;
}

.new-ui .landing .page-section.bg-white .video-thumbnail img {
  border: 16px solid #d9e6fa;
  box-shadow: 0 8px 0 -4px rgba(93, 117, 154, 0.75);
}

.new-ui .landing .hsteps {
  padding-bottom: 6rem;
}

.new-ui .landing .hsteps *[class*="grid-"] {
  padding: 1rem 2rem;
  position: relative;
}

.new-ui .landing .hsteps *[class*="grid-"]:before {
  display: block;
  content: "";
  width: 25%;
  height: 4rem;
  background-image: url(../../images/curve-arrow.png);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: contain;
  position: absolute;
  bottom: -1rem;
  right: -12.5%;
  opacity: 0;
}

.new-ui .landing .hsteps *[class*="grid-"]:nth-child(1):before {
  opacity: 1;
  transition: opacity 0.2s ease-out;
  transition-delay: 1s;
}

.new-ui .landing .hsteps *[class*="grid-"]:nth-child(2):before {
  opacity: 1;
  transition: opacity 0.2s ease-out;
  transition-delay: 2s;
}

.new-ui .landing .hsteps *[class*="grid-"]:last-child:before {
  display: none;
}

.new-ui .landing .hsteps h2 {
  text-shadow: 2px 2px 0 rgba(23, 75, 173, 0.5);
  font-size: 3.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.new-ui .landing .hsteps p {
  font-size: 1.275rem;
  line-height: 1.325em;
}

.new-ui .landing .hsteps img {
  margin-top: 1.5rem;
  margin-bottom: 2rem;
}

.new-ui .landing .list.checkmark {
  margin-top: 0;
}

.new-ui .landing .special .video {
  width: 50%;
  height: auto;
  text-align: center;
  margin: 0 auto;
}

.new-ui .landing .special .info {
  width: 75%;
  height: auto;
  margin: 4rem auto 4rem auto;
}

.new-ui .landing .promo-box {
  width: 70%;
  height: auto;
  margin: 2.5rem auto;
  padding: 3rem 16rem 3rem 3rem;
  background: #5d9bfc;
  border-radius: 0.25rem;
  position: relative;
}

.new-ui .landing .promo-box p {
  display: block;
  height: auto;
  text-align: left;
}

.new-ui .landing .promo-box .type-intro {
  font-weight: 400;
}

.new-ui .landing .promo-box figure {
  width: 10rem;
  height: 10rem;
  overflow: hidden;
  border-radius: 100%;
  position: absolute;
  top: 50%;
  right: 3rem;
  margin-top: -5rem;
}

.new-ui .landing .promo-box figure img {
  width: 100%;
  height: auto;
}

.new-ui .landing .bookmark-menu {
  display: none;
  padding-right: 12rem;
}

.new-ui .landing .bookmark-menu .links {
  width: calc(100% - 8rem);
}

.new-ui .landing .bookmark-menu .links .next {
  box-shadow: -8px 0 10px 0 #1c2c3e;
}

.new-ui .landing .bookmark-menu .links .prev {
  box-shadow: 8px 0 10px 0 #1c2c3e;
}

.new-ui .landing .bookmark-menu .button-cta {
  position: absolute;
  top: 0.6rem;
  right: 1.5rem;
}

.new-ui .landing #main-footer {
  background: #000;
  padding: 1.5rem 0;
  text-align: center;
}

.new-ui .landing #main-footer span,
.new-ui .landing #main-footer a {
  color: #c1cfe6;
  font-size: 0.85rem;
}

.new-ui .landing #main-footer .footer-separator {
  margin: 0 0.5rem;
}

.new-ui .landing #main-footer a:hover {
  color: #FFF;
  transition: color 0.2s ease-out;
}

.new-ui .landing .centerblock {
  max-width: 50vw;
}

@media (max-width: 1440px) {
  .new-ui .landing .centerblock {
    max-width: 70vw;
  }
}

@media (max-width: 1280px) {
  .new-ui .landing .page-header.landing-marketing-cockpit-head {
    max-width: 85vw;
  }
  .new-ui .landing .page-section > header,
  .new-ui .landing .page-section .columnize,
  .new-ui .landing .page-section .content,
  .new-ui .landing .page-section .info {
    max-width: 85vw;
  }
}

@media (max-width: 1024px) {
  .new-ui .landing .centerblock {
    max-width: 100vw;
  }
  .new-ui .landing .page-header.landing-marketing-cockpit-head {
    max-width: 100vw;
  }
  .new-ui .landing .page-section {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
  .new-ui .landing .page-section > header,
  .new-ui .landing .page-section .columnize,
  .new-ui .landing .page-section .content,
  .new-ui .landing .page-section .info {
    max-width: 100vw;
  }
  .new-ui .landing .page-section .grid-6-12 {
    width: 100%;
  }
  .new-ui .landing .page-section .grid-6-12 .video-thumbnail {
    width: 85%;
    margin: 0 auto;
  }
  .new-ui .landing .page-section .grid-6-12 p {
    text-align: justify;
  }
  .new-ui .landing .page-section:nth-child(2) .grid-6-12:nth-child(2), .new-ui .landing .page-section:nth-child(4) .grid-6-12:nth-child(2) {
    float: left;
  }
  .new-ui .landing .bookmark-menu .links {
    width: 100%;
  }
}

@media (max-width: 960px) {
  .new-ui .landing .grid *[class*="grid-"] {
    display: block;
    width: 100%;
    margin-left: 0;
  }
  .new-ui .landing *[class*="grid-"] {
    display: block;
    width: 100%;
    margin-left: 0;
  }
  .new-ui .landing .info,
  .new-ui .landing .video-thumbnail {
    width: 90%;
  }
}

@media (max-width: 768px) {
  .new-ui .landing .page-header .page-title {
    max-width: 95%;
    font-size: 2.5rem;
  }
  .new-ui .landing .page-content .main-video figure img {
    width: 100%;
  }
  .new-ui .landing .page-section {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .new-ui .landing .page-section header h1,
  .new-ui .landing .page-section header h2,
  .new-ui .landing .page-section header h3 {
    width: 100%;
  }
  .new-ui .landing .page-section:first-child h2 {
    font-size: 1.65rem;
  }
  .new-ui .landing .promo-box {
    width: 90%;
    margin: 5rem auto 1.5rem auto;
    padding: 8rem 3rem 3rem 3rem;
  }
  .new-ui .landing .promo-box p {
    display: block;
    height: auto;
    text-align: left;
  }
  .new-ui .landing .promo-box figure {
    width: 10rem;
    height: 10rem;
    top: -3rem;
    right: 50%;
    margin-top: 0;
    margin-right: -5rem;
  }
}

@media (max-width: 660px) {
  .new-ui .landing .hsteps h2 {
    font-size: 2.5rem;
    hyphens: manual;
    -webkit-hyphens: manual;
  }
}

@media (max-width: 600px) {
  .new-ui .landing .button-cta {
    padding-right: 3em !important;
  }
  .new-ui .landing .button-cta:before {
    right: 1.5rem !important;
  }
  .new-ui .landing .bookmark-menu .links {
    display: none;
  }
  .new-ui .landing .bookmark-menu .button-cta {
    right: 50%;
    margin-right: -4.75rem;
    padding-right: 1.45rem !important;
  }
}

@media (max-width: 400px) {
  .new-ui .landing .page-header {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .new-ui .landing .page-header .page-title {
    width: 100%;
    font-size: 2rem;
    hyphens: manual;
    -webkit-hyphens: manual;
  }
  .new-ui .landing .hsteps h2:first-child {
    font-size: 2rem;
  }
  .new-ui .landing .hsteps img {
    width: 100%;
    height: auto;
  }
  .new-ui .landing .page-section {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .new-ui .landing .page-section header h2 {
    font-size: 1.85rem;
    hyphens: manual;
    -webkit-hyphens: manual;
    margin-bottom: 1.25rem;
  }
  .new-ui .landing .page-section header h3 {
    font-size: 1.25rem;
    hyphens: manual;
    -webkit-hyphens: manual;
  }
  .new-ui .landing .page-section p {
    font-size: 1.2rem;
  }
  .new-ui .landing .promo-box {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .new-ui .landing .video-play {
    width: 5.625rem;
    height: 4.375rem;
  }
  .new-ui .landing .page-content .main-video .video-play {
    margin-left: -2.5rem;
    margin-top: -2.5rem;
  }
  .new-ui .landing .video .video-play {
    margin-left: -2.5rem;
    margin-top: -2.5rem;
  }
  .new-ui .landing .button-cta.xlarge {
    font-size: 1.45rem;
  }
  .new-ui .landing .button-cta.medium {
    font-size: 1.35rem;
  }
}

.new-ui .styleguide #main-content {
  width: 100%;
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.new-ui .styleguide #main-content .ui-box:not(.card) + .ui-box:not(.card) {
  margin-top: 3rem;
}

.new-ui .styleguide .page-header,
.new-ui .styleguide .page-section {
  width: 100%;
  max-width: 100rem;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 1660px) {
  .new-ui .styleguide .page-header,
  .new-ui .styleguide .page-section {
    max-width: 90rem;
  }
}

@media (max-width: 1500px) {
  .new-ui .styleguide .page-header,
  .new-ui .styleguide .page-section {
    max-width: 75rem;
  }
}

@media (max-width: 1260px) {
  .new-ui .styleguide .page-header,
  .new-ui .styleguide .page-section {
    width: 90%;
    max-width: 90%;
  }
}

.new-ui .styleguide .page-header h1 {
  color: #686cbe;
}

.new-ui .styleguide .page-section {
  height: auto;
  min-height: 100%;
  padding-bottom: 4rem;
}

.new-ui .styleguide .page-section h2,
.new-ui .styleguide .page-section h3,
.new-ui .styleguide .page-section h4 {
  color: #686cbe;
  font-weight: 600;
  margin-bottom: 2rem;
  text-transform: capitalize;
}

.new-ui .styleguide .page-section h4 {
  color: #758ba9;
  font-size: 1em;
  margin-bottom: 0.5rem;
}

.new-ui .styleguide .page-section strong {
  color: #686cbe;
}

.new-ui .styleguide .page-section-content p {
  line-height: 1.325em;
}

.new-ui .styleguide .code-snippet {
  display: inline-block;
  width: auto;
  padding: 0.35rem;
  font-family: monospace;
  font-size: 0.75em;
  font-weight: 600;
  color: #686cbe;
  background-color: #f4f8ff;
  border: 1px solid #eff5ff;
}

.new-ui .styleguide p + .code-snippet {
  margin: 0.75rem 0 0.325rem 0;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"] {
  height: 3.5rem;
  line-height: 3.5rem;
  color: #FFF;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(0) {
  background-color: #b6b9e6;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(1) {
  background-color: #a7aae1;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(2) {
  background-color: #989cdc;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(3) {
  background-color: #888dd6;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(4) {
  background-color: #797fd1;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(5) {
  background-color: #6a70cc;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(6) {
  background-color: #5b62c7;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(7) {
  background-color: #4c53c2;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(8) {
  background-color: #3f47b9;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(9) {
  background-color: #3a41aa;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(10) {
  background-color: #353b9b;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(11) {
  background-color: #30368c;
}

.new-ui .styleguide .grid-sample .grid *[class*="grid-"]:nth-child(12) {
  background-color: #2b307d;
}

.new-ui .styleguide .grid-sample .grid + .grid {
  margin-top: 1.25rem;
}

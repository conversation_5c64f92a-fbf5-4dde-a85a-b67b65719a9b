<?php
/**
 * Configuration
 *
 * some explanations about the bootstrap in klicktipp:
 *   DRUPAL_BOOTSTRAP_CONFIGURATION
 *     - loads settings.php, which
 *     - loads baseconfig.inc.php (this file = installation dependent constants)
 *   DRUPAL_BOOTSTRAP_PAGE_CACHE
 *     - calls hook klicktipp_boot, which
 *     - loads includes from sites/all/modules/klicktipp/core/* (constants and utilities)
 *   DRUPAL_BOOTSTRAP_DATABASE
 *     - connects to database (after this file is loaded)
 *   DRUPAL_BOOTSTRAP_FULL
 *     - calls hook klicktipp_init
 **/

$conf['samesite_cookie_value'] = 'None';

// Sets the constant for the given var_name from the environment variable with the same name
// If a default_value is given use that value, otherwise
// If the environment variable is not set, then die() to stop the code
if(!function_exists('define_from_env')){ //TODO: remove when cliCronBounceQueueTestCase fixed, see DEV-2081
  function define_from_env($const_name, $var_name, $default_value = null): void {
    if (defined($const_name)) {
      return;
    }

    $value = $default_value;
    if (isset($var_name) && array_key_exists($var_name, $_SERVER)) {
      $value = $_SERVER[$var_name];
    }

    if (!array_key_exists($var_name, $_SERVER)) {
      $_SERVER[$var_name] = $value;
    }

    define($const_name, $value);
  }
}

// Set and define all Constants inside the try..catch in order to detect configuration errors quickly
define_from_env('KLICKTIPP_ENVIRONMENT', 'KLICKTIPP_ENVIRONMENT');

/*
* For each environment we have different KLICKTIPP_DOMAIN which need to be taken care off
* thus different configuration is needed. This is also due the "domain migration".
*/
switch (KLICKTIPP_ENVIRONMENT) {

  case 'prod':
    if(substr($_SERVER['HTTP_HOST'], -14) == 'klick-tipp.com') {
      define('KLICKTIPP_DOMAIN', 'klick-tipp.com');
      define('APP_URL', 'https://www.'.KLICKTIPP_DOMAIN.'/'); // Must end with '/'
    } else  {
      // worker (runqueue.sh) default case, because HTTP_HOST is with php-cli always empty
      define_from_env('KLICKTIPP_DOMAIN', 'KLICKTIPP_DOMAIN', 'klicktipp.com');
      define('APP_URL', 'https://app.'.KLICKTIPP_DOMAIN.'/'); // Must end with '/'
    }
    break;

  case 'staging':
    if ($_SERVER['HTTP_HOST'] == 'sim.zauberlist.com') {
      define('KLICKTIPP_DOMAIN', 'sim.zauberlist.com');
      define('APP_URL', 'https://'.KLICKTIPP_DOMAIN.'/'); // Must end with '/'
    } elseif ($_SERVER['HTTP_HOST'] == 'staging.zauberlist.com') {
      define('KLICKTIPP_DOMAIN', 'staging.zauberlist.com');
      define('APP_URL', 'https://'.KLICKTIPP_DOMAIN.'/'); // Must end with '/'
    } elseif ($_SERVER['HTTP_HOST'] == 'klick-tipp-staging.com') {
      define('KLICKTIPP_DOMAIN', 'klick-tipp-staging.com');
      define('APP_URL', 'https://www.'.KLICKTIPP_DOMAIN.'/'); // Must end with '/'
    } else {
      // worker (runqueue.sh) default case, because HTTP_HOST is with php-cli always empty
      define_from_env('KLICKTIPP_DOMAIN', 'KLICKTIPP_DOMAIN', 'klicktipp-staging.com');
      define('APP_URL', 'https://app.' . KLICKTIPP_DOMAIN . '/'); // Must end with '/'
    }
    break;

  case 'ci':
  case 'dev':
  case 'devops':
  case 'integration':
  case 'local':
  case 'test':
    // The LOCAL, DEV and CI environments are basically just the same, therefore they share the same config
    if (empty($_SERVER['HTTP_HOST'])) {
      // worker (runqueue.sh) default case, because HTTP_HOST is with php-cli always empty
      define_from_env('KLICKTIPP_DOMAIN', 'KLICKTIPP_DOMAIN', 'ktlocal.com');
      define('APP_URL', 'https://app.' . KLICKTIPP_DOMAIN . '/'); // Must end with '/'
    } else {
      define('KLICKTIPP_DOMAIN', $_SERVER['HTTP_HOST']);
      define('APP_URL', 'https://' . KLICKTIPP_DOMAIN . '/'); // Must end with '/'
    }
    break;

  case 'preprod':
    // worker (runqueue.sh) default case, because HTTP_HOST is with php-cli always empty
    define_from_env('KLICKTIPP_DOMAIN', 'KLICKTIPP_DOMAIN', 'klicktipp-preprod.com');
    define('APP_URL', 'https://app.'.KLICKTIPP_DOMAIN.'/'); // Must end with '/'
    break;

  default:
    // As these envs do not exist (yet), throw a die() and stop processing.
    die("Environment Configuration Error: KLICKTIPP_ENVIRONMENT '" . KLICKTIPP_ENVIRONMENT . "' does not exist!\n");
}

global $cookie_domain;
$cookie_domain = "." . KLICKTIPP_DOMAIN;

# domain header doesn't accept explicit port in domain header assignment
# change:
# .www.ktlocal.com:20394 -> .www.ktlocal.com
# This is a special case for e2e test stack: we open the stack on :5002 instead of : 443
# set-cookie directive fails, when domain is .www.ktlocal.com:5002
# In the case of :443 browsers or others automatically remove the port from the cookie domain.
switch (KLICKTIPP_ENVIRONMENT) {
  case 'ci':
  case 'dev':
  case 'devops':
  case 'integration':
  case 'local':
  case 'test':
	$cookie_domain = strtok($cookie_domain, ':');
	break;
}


define_from_env('KLICKTIPP_SALT', 'KLICKTIPP_SALT', KLICKTIPP_DOMAIN);

define_from_env('KLICKTIPP_ASSETS_DOMAIN', 'KLICKTIPP_ASSETS_DOMAIN', 'assets.' . KLICKTIPP_DOMAIN );
define_from_env('KLICKTIPP_MAILCDN_DOMAIN', 'KLICKTIPP_MAILCDN_DOMAIN', 'mail.cdndata.' . KLICKTIPP_DOMAIN );
define_from_env('KLICKTIPP_CDN_SCRIPTS_PATH', 'KLICKTIPP_CDN_SCRIPTS_PATH', 'scripts.cdn.klicktipp.cloud');


// docroot
define_from_env('DOMAIN_ROOT', 'KLICKTIPP_DOMAIN_ROOT'); // No '/' at the end
define_from_env('APP_PATH', 'KLICKTIPP_APP_PATH', DOMAIN_ROOT . '/htdocs'); // No '/' at the end
define_from_env('DATA_PATH', 'KLICKTIPP_DATA_PATH', DOMAIN_ROOT); // No '/' at the end

define_from_env('PUBLIC_API_URL', 'KLICKTIPP_PUBLIC_API_URL', APP_URL.'api'.'/');

// use DRUPAL_ROOT if called from web server, APP_PATH otherwise
// DRUPAL_ROOT is used to load the drupal subsystems, so it needs to be defined ahead (in the calling script)
// just define it here in case we dont use the drupal bootstrap
define_from_env('DRUPAL_ROOT', 'KLICKTIPP_DRUPAL_ROOT', APP_PATH); // drupal defines that as getcwd()

define_from_env('KLICKTIPP_PATH', 'KLICKTIPP_PATH', 'sites/all/modules/klicktipp');

// send queue location
define_from_env('SEND_METHOD_SAVETODISK_DIR', 'KLICKTIPP_SEND_METHOD_SAVETODISK_DIR', '/var/spool/q/');
define_from_env('SEND_METHOD_SAVETODISK_TMP', 'KLICKTIPP_SEND_METHOD_SAVETODISK_TMP', SEND_METHOD_SAVETODISK_DIR.'tmp/');
define_from_env('SEND_METHOD_SAVETODISK_TAR', 'KLICKTIPP_SEND_METHOD_SAVETODISK_TAR', SEND_METHOD_SAVETODISK_TMP);
define('SEND_METHOD_PREGENERATION_DIR', SEND_METHOD_SAVETODISK_DIR . 'pregeneration/');
define('SEND_METHOD_PREGENERATION_CONTROL_DIR', SEND_METHOD_PREGENERATION_DIR . '_control/');

// bounce queue location
define_from_env('BOUNCEQ_BOUNCE_RECEIVED_DIR', 'KLICKTIPP_BOUNCEQ_BOUNCE_RECEIVED_DIR', '/var/spool/bounce/received/');
define_from_env('BOUNCEQ_BOUNCE_PROCESSED_DIR', 'KLICKTIPP_BOUNCEQ_BOUNCE_PROCESSED_DIR', DATA_PATH . '/data/bounces/');

// request forwarders location
define_from_env('REQUEST_FORWARDERS_DIR', 'KLICKTIPP_REQUEST_FORWARDERS_DIR', DATA_PATH.'/pipes/');
define_from_env('REQUEST_FORWARDERS_TMP_DIR', 'KLICKTIPP_REQUEST_FORWARDERS_TMP_DIR', REQUEST_FORWARDERS_DIR.'tmp/');
define_from_env('REQUEST_FORWARDERS_FILE', 'KLICKTIPP_REQUEST_FORWARDERS_FILE', KLICKTIPP_DOMAIN.'-requests-aliases');

// pmta config dir
define_from_env('VIRTUAL_MTA_CONFIG_DIR', 'KLICKTIPP_VIRTUAL_MTA_CONFIG_DIR', DATA_PATH.'/vmtas/');
define_from_env('VIRTUAL_MTA_CONFIG_TMP_DIR', 'KLICKTIPP_VIRTUAL_MTA_CONFIG_TMP_DIR', VIRTUAL_MTA_CONFIG_DIR.'tmp/' );
define_from_env('DKIM_CONFIG_DIR', 'KLICKTIPP_DKIM_CONFIG_DIR', DATA_PATH.'/dkim/');
define_from_env('DKIM_CONFIG_TMP_DIR', 'KLICKTIPP_DKIM_CONFIG_TMP_DIR', DKIM_CONFIG_DIR.'tmp/');

// Language Settings
define_from_env('KLICKTIPP_DEFAULT_COUNTRY', 'KLICKTIPP_DEFAULT_COUNTRY');
define_from_env('KLICKTIPP_DEFAULT_LANGUAGE', 'KLICKTIPP_DEFAULT_LANGUAGE');
define_from_env('KLICKTIPP_DEFAULT_TIMEZONE', 'KLICKTIPP_DEFAULT_TIMEZONE');

//S3 settings
define_from_env('KLICKTIPP_S3_SETTINGS_KEY', 'KLICKTIPP_S3_SETTINGS_KEY');
define_from_env('KLICKTIPP_S3_SETTINGS_SECRET', 'KLICKTIPP_S3_SETTINGS_SECRET');

//S3 settings for translations
define_from_env('KLICKTIPP_TRANSLATION_S3_KEY', 'KLICKTIPP_TRANSLATION_S3_KEY');
define_from_env('KLICKTIPP_TRANSLATION_S3_SECRET', 'KLICKTIPP_TRANSLATION_S3_SECRET');

//LaunchDarkly SDK Key
define_from_env('KLICKTIPP_LAUNCH_DARKLY_SDK_KEY', 'KLICKTIPP_LAUNCH_DARKLY_SDK_KEY');

// s3 bucket
define_from_env('S3_BUCKET', 'KLICKTIPP_S3_BUCKET');
define_from_env('S3_ENDPOINT', 'KLICKTIPP_S3_ENDPOINT');

define_from_env('X_MAILER', 'KLICKTIPP_X_MAILER');

// aws hosts, e.g. for beanstalkd service
define_from_env('AWS_HOSTNAME', 'KLICKTIPP_AWS_HOSTNAME');
define_from_env('KLICKTIPP_BEANSTALKD_HOSTNAME', 'KLICKTIPP_BEANSTALKD_HOSTNAME');
define_from_env('KLICKTIPP_BEANSTALKD_PORT', 'KLICKTIPP_BEANSTALKD_PORT', 11300);

// database credentials used in settings.php
define_from_env('KLICKTIPP_SETTINGS_DBNAME', 'KLICKTIPP_SETTINGS_DBNAME');
define_from_env('KLICKTIPP_SETTINGS_DBUSER', 'KLICKTIPP_SETTINGS_DBUSER');
define_from_env('KLICKTIPP_SETTINGS_DBPASS', 'KLICKTIPP_SETTINGS_DBPASS');
// proxysql middleware
define_from_env('KLICKTIPP_SETTINGS_DBHOST', 'KLICKTIPP_SETTINGS_DBHOST');
define_from_env('KLICKTIPP_SETTINGS_DBPORT', 'KLICKTIPP_SETTINGS_DBPORT');

define_from_env('KLICKTIPP_SETTINGS_DBCHARSET', 'KLICKTIPP_SETTINGS_DBCHARSET', 'utf8');
define_from_env('KLICKTIPP_SETTINGS_DBCOLLATION', 'KLICKTIPP_SETTINGS_DBCOLLATION', 'utf8_unicode_ci');
/* for mb4 support
define_from_env('KLICKTIPP_SETTINGS_DBCHARSET', 'KLICKTIPP_SETTINGS_DBCHARSET', 'utf8mb4');
define_from_env('KLICKTIPP_SETTINGS_DBCOLLATION', 'KLICKTIPP_SETTINGS_DBCOLLATION', 'utf8mb4_general_ci');
*/

// database credentials used in amember/config.inc.php
define_from_env('AMEMBER_SETTINGS_DBNAME', 'AMEMBER_SETTINGS_DBNAME', KLICKTIPP_SETTINGS_DBNAME);
define_from_env('AMEMBER_SETTINGS_DBUSER', 'AMEMBER_SETTINGS_DBUSER', KLICKTIPP_SETTINGS_DBUSER);
define_from_env('AMEMBER_SETTINGS_DBPASS', 'AMEMBER_SETTINGS_DBPASS', KLICKTIPP_SETTINGS_DBPASS);

// Bee Email Editor
define_from_env('KLICKTIPP_BEE_PLUGIN_CLIENT_ID', 'KLICKTIPP_BEE_PLUGIN_CLIENT_ID', '');
define_from_env('KLICKTIPP_BEE_PLUGIN_CLIENT_SECRET', 'KLICKTIPP_BEE_PLUGIN_CLIENT_SECRET', '');
define_from_env('KLICKTIPP_BEE_PLUGIN_LANDING_PAGE_CLIENT_ID', 'KLICKTIPP_BEE_PLUGIN_LANDING_PAGE_CLIENT_ID', '');
define_from_env('KLICKTIPP_BEE_PLUGIN_LANDING_PAGE_CLIENT_SECRET', 'KLICKTIPP_BEE_PLUGIN_LANDING_PAGE_CLIENT_SECRET', '');
define_from_env('KLICKTIPP_BEE_PLUGIN_FILE_MANAGER_CLIENT_ID', 'KLICKTIPP_BEE_PLUGIN_FILE_MANAGER_CLIENT_ID', '');
define_from_env('KLICKTIPP_BEE_PLUGIN_FILE_MANAGER_CLIENT_SECRET', 'KLICKTIPP_BEE_PLUGIN_FILE_MANAGER_CLIENT_SECRET', '');
define_from_env('KLICKTIPP_BEE_PLUGIN_APIKEY', 'KLICKTIPP_BEE_PLUGIN_APIKEY', '');
define_from_env('KLICKTIPP_BEE_S3_BUCKET', 'KLICKTIPP_BEE_S3_BUCKET', '');
define_from_env('KLICKTIPP_BEE_S3_ENDPOINT', 'KLICKTIPP_BEE_S3_ENDPOINT', '');
define_from_env('KLICKTIPP_BEE_S3_KEY', 'KLICKTIPP_BEE_S3_KEY', '');
define_from_env('KLICKTIPP_BEE_S3_SECRET', 'KLICKTIPP_BEE_S3_SECRET', '');

// subscriber duplicate event export. subfolder under /data/users/<USER_ID>
define_from_env('KLICKTIPP_DUPLICATES_DOWNLOAD_DIR', 'KLICKTIPP_DUPLICATES_DOWNLOAD_DIR', 'duplicates-export');

// Debuging and Profiling
define_from_env('KLICKTIPP_PHP_XHPROF_SUBDOMAIN', 'KLICKTIPP_PHP_XHPROF_SUBDOMAIN', 'xhprof');
define_from_env('KLICKTIPP_PHP_XHPROF_URL', 'KLICKTIPP_PHP_XHPROF_URL', 'https://' . KLICKTIPP_PHP_XHPROF_SUBDOMAIN . KLICKTIPP_DOMAIN . '/index.php');

// Listbuilding
/*
 * STAGING should be set to: "staging.zauberlist.com"
 * PRODUCTION should be set to: "klick-tipp.com"
 */
define_from_env('KLICKTIPP_LISTBUILDING_EMAIL_REQUEST_DOMAIN', 'KLICKTIPP_LISTBUILDING_EMAIL_REQUEST_DOMAIN', KLICKTIPP_DOMAIN);
/*
 * STAGING should be set to: "<EMAIL>-requests"
 * PRODUCTION should be set to: "<EMAIL>-requests"
 */
define_from_env('KLICKTIPP_LISTBUILDING_EMAIL_REQUEST_ALIAS', 'KLICKTIPP_LISTBUILDING_EMAIL_REQUEST_ALIAS', "mail@alias-" . KLICKTIPP_DOMAIN . "-requests");

/*
 * use "const" different from other constants in file because opposite to "define" it supports arrays
 */
const KLICKTIPP_EMAIL_DOMAINS = [KLICKTIPP_DOMAIN, KLICKTIPP_LISTBUILDING_EMAIL_REQUEST_DOMAIN];

define_from_env('KLICKTIPP_DNS_VALIDATION_DOMAIN', 'KLICKTIPP_DNS_VALIDATION_DOMAIN', KLICKTIPP_DOMAIN);

define_from_env('KLICKTIPP_LANDING_PAGE_ROOT_DOMAIN', 'KLICKTIPP_LANDING_PAGE_ROOT_DOMAIN', 'klicktipp.site');

define_from_env('KLICKTIPP_HALON_BRIDGE_API_ENDPOINT', 'KLICKTIPP_HALON_BRIDGE_API_ENDPOINT', '');
define_from_env('KLICKTIPP_HALON_X_API_KEY', 'KLICKTIPP_HALON_X_API_KEY');

### Redis ###
define_from_env('KLICKTIPP_REDIS_DSN', 'KLICKTIPP_REDIS_DSN');
define_from_env('KLICKTIPP_REDIS_RATE_LIMIT_DSN', 'KLICKTIPP_REDIS_RATE_LIMIT_DSN');
define_from_env('KLICKTIPP_REDIS_DOMAIN_WHITELIST_DSN', 'KLICKTIPP_REDIS_DOMAIN_WHITELIST_DSN');
define_from_env('KLICKTIPP_REDIS_OIDC_TOKEN_CACHE_DSN', 'KLICKTIPP_REDIS_OIDC_TOKEN_CACHE_DSN');

//
// CACHING (memcache or redis)
//
define_from_env('KLICKTIPP_CACHE_BACKEND', 'KLICKTIPP_CACHE_BACKEND');

switch (KLICKTIPP_CACHE_BACKEND) {

  case 'db':
    //Do not need to perform any changes
    break;

  case 'memcache':
    define_from_env('KLICKTIPP_MEMCACHE_SERVER_ADDRESS', 'KLICKTIPP_MEMCACHE_SERVER_ADDRESS');
    define_from_env('KLICKTIPP_MEMCACHE_SERVER_BIN', 'KLICKTIPP_MEMCACHE_SERVER_BIN');
    define_from_env('KLICKTIPP_MEMCACHE_KEY_PREFIX', 'KLICKTIPP_MEMCACHE_KEY_PREFIX', KLICKTIPP_SETTINGS_DBNAME);

    $conf['memcache_servers'] = array(
      KLICKTIPP_MEMCACHE_SERVER_ADDRESS => KLICKTIPP_MEMCACHE_SERVER_BIN,
    );

    $conf['cache_backends'][] = 'sites/all/modules/memcache/memcache.inc';
    $conf['cache_default_class'] = 'MemCacheDrupal';
    $conf['cache_class_cache_form'] = 'DrupalDatabaseCache';

    $conf['memcache_key_prefix'] = KLICKTIPP_MEMCACHE_KEY_PREFIX;

    // Max Item size: 5MB = 5 * 1024 * 1024
    // Requires also max item size on memcache to be increased "-I 5M"
    $conf['memcache_data_max_length'] = 5242880;

    // Use Memcache instead of Database for Locking
    // Disabled, after heaving heavy lag spikes
    // https://klicktipp.atlassian.net/browse/AD-1560
    // $conf['lock_inc'] = 'sites/all/modules/memcache/memcache-lock.inc';

    break;

  case 'redis':
    define_from_env('KLICKTIPP_REDIS_HOSTNAME', 'KLICKTIPP_REDIS_HOSTNAME');
    define_from_env('KLICKTIPP_REDIS_PORT', 'KLICKTIPP_REDIS_PORT', '6379');

    $conf['redis_client_interface'] = 'PhpRedis';
    $conf['redis_client_host']      =  KLICKTIPP_REDIS_HOSTNAME;
    $conf['redis_client_port']      =  KLICKTIPP_REDIS_PORT;

    $conf['cache_backends'][]  = 'sites/all/modules/redis/redis.autoload.inc';
    $conf['cache_default_class']    = 'Redis_CacheCompressed';
    $conf['cache_compression_size_threshold'] = 100;
    $conf['cache_compression_ratio'] = 1;
    $conf['cache_prefix'] = KLICKTIPP_SETTINGS_DBNAME;

    // You still can exclude the form cache the same way if you
    // experience problems:
    ////$conf['cache_class_cache_form'] = 'DrupalDatabaseCache';

    // !!! Locking must not be enabled, when using redis + envoy, because
    // required redis commands do not work through envoy
    // $conf['lock_inc'] = 'sites/all/modules/redis/redis.lock.inc';

    // This could be enabled later, but seems not to have an major impact
    // on performance.
    // $conf['path_inc'] = 'sites/all/modules/redis/redis.path.inc';

    // Set eviction mecanism to "never flush", this means that entries
    // are never being deleted by Drupal itself, but cached item validity
    // is checked at load time: this is the best setting for sharded
    // environments, or sites that have a huge amount of cached entries
    // in the same bin, in your case, please set this one it may help to
    // avoid flush performance problems
    $conf['redis_flush_mode'] = 3;

    // This is not mandatory at all, but using this settings will
    // force the Redis server to proceed to LRU eviction by
    // itself often enough to avoid long time stalling entries
    $conf['redis_perm_ttl'] = "2 weeks";

    break;

  default:
    die('Need to specify a Cache Backend! Use either "memcache" or "redis"');
    break;
}


define_from_env('KLICKTIPP_DELEGATE_SESSION_HANDLING_TO_FPM', 'KLICKTIPP_DELEGATE_SESSION_HANDLING_TO_FPM');

if (KLICKTIPP_DELEGATE_SESSION_HANDLING_TO_FPM == 'true') {

      // Enables session Proxy module which passes session handling to php process.
      // Session backend is then configured by
      // session.save_handler and
      // session.save_path in php.ini
      $conf['session_inc'] = 'sites/all/modules/session_proxy/session.inc';
      $conf['session_storage_force_default'] = TRUE;

}

$conf['path_alias_admin_blacklist'] = FALSE;


// activate tarfile creation for pmta injection
//$conf['klicktipp_tar_pickup'] = 1;

// beanstalkd, see: /sites/all/modules/beanstalkd/README.txt
$conf['queue_default_class'] = 'BeanstalkdQueue';

// queue priorities from low to high (smaller values, higher priority)
$queue_priorities = [
  // lowest: do whenever there is nothing else to do
  'stop_inactive_users_queue' => 2000,
  'delete_pending_subscribers_queue' => 2000,
  'delete_taggings' => 2000,
  'delete_newsletter_queue' => 2000,
  'email_similarity' => 2000,
  'npscore_queue' => 2000,
  'senderscore_queue' => 2000,
  'softbouncecheck' => 2000,
  'subscriber_limit_queue' => 2000,
  'whitelabeldomaincheck' => 2000,
  'landing_page_domain_check' => 2000,
  'tag_passive_subscribers' => 2000,
  'zy0de_queue' => 2000,
  'export_subscriber_duplicates' => 2000,
  'addon_status_sync' => 2000,
  'bouncecheck' => 1999,
  // subscriber deletions create a lot of locks in db, so better with low prio
  'delete_subscribers' => 1600,
  'delete_subscriptions' => 1600,
  // chicklets should have regular updates, but don not need high prio
  'chicklet_update' => 1500,
  // default: all new and/or not listed queues
  // this is below 1024 and will show up in "current-jobs-urgent"
  'default' => 1000,
  // low: should be done in reasonable time
  'subscriber_lowprio_queue' => 950,
  'fullcontact_subscribers' => 903,
  'export_subscribers' => 901,
  'tag_subscribers' => 900,
  // medium: our main jobs
  'subscriber_queue' => 850,
  'outbound_event' => 820,
  'send_queue_to_one' => 800,
  'send_nexmo_sms' => 800,
  'send_twilio_sms' => 800,
  'transactional_queue' => 800,
  'newsletter_queue' => 800,
  'newsletter_pregeneration_queue' => 810,
  'newsletter_pregenerated_queue' => 810,
  'autoresponder_queue' => 800,
  'send_queue' => 800,
  // high: large jobs with high priority
  'winner_queue' => 710,
  'create_queue' => 700,
  // interactive: asap
  'move_subscribers' => 675,
  'import_subscribers_import' => 651,
  'import_subscribers_validate' => 651,
  'import_subscribers' => 651,
  'request_forwarders_queue' => 600,
];
foreach ($queue_priorities as $queuename => $prio) {
  $q = ($queuename == 'default') ? "beanstalk_default_queue" : "beanstalk_queue_$queuename";
  $conf[$q] = array(
    // Name of the host where beanstalkd is installed.
    'host' => KLICKTIPP_BEANSTALKD_HOSTNAME,
    // Port which beanstalkd is listening to.
    'port' => KLICKTIPP_BEANSTALKD_PORT,
    // Used in runqueue.sh to know if it should run the job in another process.
    'fork' => FALSE,
    // How long you should wait when reserving a job.
    'reserve_timeout' => 0,
    // ttr = Seconds a job can be reserved (run), before it is requeued by beanstalk.
    // THIS MUST NEVER HAPPEN. SET THIS VALUE VERY HIGH.
    'ttr' => 600,
    // Seconds to delay a job.
    'release_delay' => 0,
    // When forking the job runner, wait n time for more items on this queue.
    'forked_extra_timeout' => FALSE,
    // When forking the job runner, process n items in addition on this queue.
    'forked_extra_items' => FALSE,
    // Sets the priority of the job.
    'priority' => $prio,
    // Set the default delay for a queue.
    //@note: may be overridden by #delay parameter in job data
    'delay' => 0,
  );
}

### Subscriber Import
# directory name in ./data/users were import files will be stored
# e.g. ./data/users/{import}/<slice>/<uid>/<importID>/files.json|csv
# Note: in drupal (prod) ./data/users is set as the private:// directory
define_from_env('KLICKTIPP_SUBSCRIBER_IMPORT_DATA_USERS_DIR', 'KLICKTIPP_SUBSCRIBER_IMPORT_DATA_USERS_DIR', 'import');
# files in the above directory should be deleted when they are older than
#   the specified days + 5 days
# the 5 days is just a buffer to not delete to early
# the import itself will be invalid for update after the specified days
define_from_env('KLICKTIPP_SUBSCRIBER_IMPORT_FILES_VALID_UNTIL_DAYS', 'KLICKTIPP_SUBSCRIBER_IMPORT_FILES_VALID_UNTIL_DAYS', 90);

### ChatGPT API Credentials
define_from_env('KLICKTIPP_CHATGPT_ORGANISATION', 'KLICKTIPP_CHATGPT_ORGANISATION');
define_from_env('KLICKTIPP_CHATGPT_APIKEY', 'KLICKTIPP_CHATGPT_APIKEY');

### Landing Page CNAME (the cname record value, that a custom domain has to point to)
define_from_env('KLICKTIPP_LANDING_PAGE_CNAME_VALUE', 'KLICKTIPP_LANDING_PAGE_CNAME_VALUE');

### KROT Captcha Rest Key
define_from_env('KLICKTIPP_CAPTCHA_EU_REST_KEY', 'KLICKTIPP_CAPTCHA_EU_REST_KEY');

### RabbitMQ ###
define_from_env('KLICKTIPP_RABBITMQ_HOST', 'KLICKTIPP_RABBITMQ_HOST');
define_from_env('KLICKTIPP_RABBITMQ_PORT', 'KLICKTIPP_RABBITMQ_PORT');
define_from_env('KLICKTIPP_RABBITMQ_USER', 'KLICKTIPP_RABBITMQ_USER');
define_from_env('KLICKTIPP_RABBITMQ_PASS', 'KLICKTIPP_RABBITMQ_PASS');
define_from_env('KLICKTIPP_RABBITMQ_VHOST', 'KLICKTIPP_RABBITMQ_VHOST');

### NodeRed ###
define('KLICKTIPP_NODERED_BASE_URL', 'https://node-red-lvn0t-u24934.vm.elestio.app');
define_from_env('KLICKTIPP_NODERED_BASIC_AUTH_USER', 'KLICKTIPP_NODERED_BASIC_AUTH_USER');
define_from_env('KLICKTIPP_NODERED_BASIC_AUTH_PASSWORD', 'KLICKTIPP_NODERED_BASIC_AUTH_PASSWORD');
define_from_env('KLICKTIPP_NODERED_API_KEY', 'KLICKTIPP_NODERED_API_KEY');
define_from_env('KLICKTIPP_NODERED_API_SECRET', 'KLICKTIPP_NODERED_API_SECRET');
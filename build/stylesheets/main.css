/* Klick-Tipp Icons */
* {
  outline: none !important; }

body {
  position: relative;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif; }

.container.container-form {
  padding-top: 20px; }

#top-menu {
  margin: 0; }

#top-menu.navbar-no-electron #account-info-wrapper,
#top-menu.navbar-no-electron .top-menu-search {
  display: none; }

#top-menu.navbar-no-electron .navbar-collapse {
  text-align: center; }

#top-menu.navbar-no-electron ul.navbar-nav {
  display: inline-block;
  float: none; }

#top-menu.navbar-no-electron .navbar-logo-toggle .navbar-brand {
  padding: 10px 20px 0 0; }

#top-menu.navbar-no-electron .navbar-nav .dropdown-menu {
  text-align: left; }

#view {
  position: relative;
  margin: 0;
  padding: 50px 0 0 0; }

#sidebar {
  position: fixed;
  top: 50px;
  right: 0px;
  width: 500px;
  height: 2500px;
  margin: 0;
  padding: 15px;
  background: #eeeeee none no-repeat scroll 0 0;
  border-left: 10px solid #999999;
  -webkit-box-shadow: -10px 0px 20px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: -10px 0px 20px 0px rgba(0, 0, 0, 0.2);
  box-shadow: -10px 0px 20px 0px rgba(0, 0, 0, 0.2);
  z-index: 100; }

#sidebar.sidebar-open {
  margin-right: 0; }

#sidebar.sidebar-closed {
  margin-right: -490px; }

#sidebar-trigger {
  position: absolute;
  left: 0;
  top: 50%;
  width: 50px;
  height: 100px;
  margin: -50px 0 0 -50px;
  background-color: #999999;
  border: 0;
  border-radius: 50px 0 0 50px;
  line-height: 100px;
  vertical-align: middle;
  text-align: center;
  color: #ffffff;
  font-size: 20px;
  cursor: pointer;
  -webkit-box-shadow: -10px 0px 20px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: -10px 0px 20px 0px rgba(0, 0, 0, 0.2);
  box-shadow: -10px 0px 20px 0px rgba(0, 0, 0, 0.2); }

#sidebar-trigger.sidebar-open {
  background: #999999 url(icons/sidebar_out.png) no-repeat scroll center center; }

#sidebar-trigger.sidebar-closed {
  background: #999999 url(icons/sidebar_in.png) no-repeat scroll center center; }

.sidebar-trigger-bubble {
  position: absolute;
  display: block;
  width: auto;
  background-color: #d9534f;
  height: 23px;
  padding: 0 5px 0 5px;
  line-height: 20px;
  top: 35px;
  right: 13px;
  vertical-align: center;
  text-align: center;
  font-size: 15px;
  border: 2px solid #d43f3a;
  border-radius: 12px; }

#sidebar-trigger.sidebar-open .sidebar-trigger-bubble {
  display: none; }

#main {
  /* jsplumb container needs relative positioning */
  position: relative;
  background: #ffffff repeat scroll 0 0;
  text-align: center;
  vertical-align: top;
  white-space: nowrap;
  padding: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

#main {
  width: 100%;
  overflow: auto; }

#builder {
  position: relative;
  display: block;
  padding: 50px; }

/* do not display the <br /> */
pf-action, pf-decision, pf-start, pf-create, campaign-builder-branch {
  font-size: 0; }

/* TODO: needed? */
[class^="icon-"],
[class*=" icon-"] {
  background-image: url("icons_sprite.png"); }

.btn-submit,
.btn-submit.active {
  background-image: url("icons_sprite.png"); }

.kt-group .input-group.input-group-select .input-group-btn:last-child > .btn {
  -webkit-border-radius: 4px !important;
  -moz-border-radius: 4px !important;
  border-radius: 4px !important; }

.kt-group .input-group.input-group-select .input-group-btn {
  padding: 0 0 0 5px; }

input.input-short {
  width: 100px; }

input.input-medium {
  width: 180px; }

input.input-mini {
  width: 40px;
  padding-left: 0px;
  padding-right: 0px; }

.modal {
  overflow: auto;
  overflow-y: scroll; }

.modal-body {
  padding: 15px; }

.modal-icon {
  background-image: url("icons_sprite.png"); }

.modal-confirm .modal-header {
  background-color: #D9534F;
  border-radius: 4px 4px 0 0;
  color: #ffffff; }

.modal-footer .modal-footer-message {
  line-height: 31px;
  vertical-align: middle; }

.modal-btn-right {
  float: right; }

select.form-control.form-select.form-select-auto {
  display: inline-block;
  width: auto;
  max-width: none; }

.inline-block > * {
  display: inline-block;
  vertical-align: top; }

.progress-popup {
  position: absolute;
  display: block;
  width: 600px;
  height: 70px;
  left: 50%;
  top: 150px;
  margin: 0 0 0 -300px;
  padding: 10px;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2); }

.progress-popup .progress-message {
  font-size: 14px;
  line-height: 16px;
  font-weight: bold;
  color: #333333;
  padding: 0;
  margin: 0 0 5px 0;
  text-align: left; }

/* dev helper classes for hover */
.highligh-hover-text {
  color: #dddddd; }

.highligh-hover-text:hover {
  color: #555555; }

/* Campaign builder */
/* --- state start --- */
.state-action,
.state-decision,
.state-start {
  position: relative;
  display: inline-block;
  max-width: 400px;
  margin: 20px 0px;
  padding: 0;
  background: #d2322d none repeat scroll 0 0;
  border: none;
  border-radius: 10px; }

.state-icon {
  position: absolute;
  display: block;
  width: 32px;
  height: 32px;
  left: 10px;
  top: 50%;
  margin: -16px 0 0 0;
  padding: 0;
  background: transparent scroll no-repeat 0 0; }

.state-name {
  position: relative;
  display: block;
  margin: 0 0 0 37px;
  color: #ffffff;
  text-align: left;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  font-size: 17px;
  font-weight: normal;
  line-height: 25px;
  vertical-align: middle;
  white-space: normal; }

/* state icons */
.modal-state-create .modal-state-icon,
.modal-header.modal-move-copy .modal-state-icon {
  background-image: url(icons/create_icon.png); }

.action-type-email .state-icon,
.splittest-variant .action-type-email .state-icon,
.modal-state-email .modal-state-icon {
  background-image: url(icons/icon_state_email.png); }

.action-type-sms .state-icon,
.splittest-variant .action-type-sms .state-icon,
.modal-state-sms .modal-state-icon {
  background-image: url(icons/icon_state_sms.png); }

.action-type-notify-by-email .state-icon,
.modal-state-notify-by-email .modal-state-icon {
  background-image: url(icons/icon_state_notify_by_email.png); }

.action-type-notify-by-sms .state-icon,
.modal-state-notify-by-sms .modal-state-icon {
  background-image: url(icons/icon_state_notify_by_sms.png); }

.action-type-outbound .state-icon,
.splittest-variant .action-type-outbound .state-icon,
.modal-state-outbound .modal-state-icon {
  background-image: url(icons/icon_state_outbound.png); }

.action-type-tagging .state-icon,
.splittest-variant .action-type-tagging .state-icon,
.modal-state-tagging .modal-state-icon {
  background-image: url(icons/icon_state_tagging.png); }

.action-type-setfield .state-icon,
.modal-state-setfield .modal-state-icon {
  background-image: url(icons/icon_state_setfield.png); }

.action-type-untagging .state-icon,
.modal-state-untagging .modal-state-icon {
  background-image: url(icons/icon_state_untagging.png); }

.action-type-unsubscribe .state-icon,
.modal-state-unsubscribe .modal-state-icon {
  background-image: url(icons/icon_state_unsubscribe.png); }

.action-type-goto .state-icon,
.modal-state-goto .modal-state-icon {
  background-image: url(icons/icon_state_goto.png); }

.action-type-wait {
  background-color: #ffffff;
  border: 1px solid #6c7a89; }

.action-type-wait .state-icon,
.modal-state-wait .modal-state-icon {
  background-image: url(icons/icon_state_wait.png); }

.action-type-start-automation .state-icon,
.splittest-variant .action-type-start-automation .state-icon,
.modal-state-start-automation .modal-state-icon {
  background-image: url(icons/icon_state_startautomation.png); }

.action-type-stop-automation .state-icon,
.modal-state-stop-automation .modal-state-icon {
  background-image: url(icons/icon_state_stopautomation.png); }

.state-decision .state-icon,
.modal-state-decision .modal-state-icon {
  background-image: url(icons/icon_state_decision.png); }

.state-start .state-icon,
.modal-state-start .modal-state-icon {
  background-image: url(icons/icon_state_start.png); }

.action-type-exit .state-icon,
.modal-state-exit .modal-state-icon {
  background-image: url(icons/icon_state_exit.png); }

.action-type-splittest .state-icon,
.modal-state-splittest .modal-state-icon {
  background-image: url(icons/icon_state_splittest.png); }

.action-type-empty .state-icon {
  background-image: url(icons/icon_state_empty.png); }

.action-type-facebook-audience-add .state-icon,
.splittest-variant .action-type-facebook-audience-add .state-icon,
.modal-state-facebook-audience-add .modal-state-icon {
  background-image: url(icons/icon_state_fbaudience.png); }

/* state colors */
.state-color-create {
  background-color: #999999; }

.group-color-workflow,
.state-color-decision,
.state-color-goto,
.state-color-splittest {
  background-color: #8337b3; }

.group-color-outgoing,
.state-color-email,
.state-color-sms,
.state-color-outbound,
.state-color-facebook-audience-add,
.state-color-notify-by-email,
.state-color-notify-by-sms,
.state-color-empty {
  background-color: #146cb3; }

.group-color-add,
.state-color-tagging,
.state-color-setfield {
  background-color: #759a05; }

.group-color-remove,
.state-color-untagging,
.state-color-unsubscribe,
.state-color-stop-automation,
.state-color-exit {
  background-color: #ca4904; }

.group-color-delay,
.state-color-wait {
  background-color: #ffffff;
  border: 1px solid #6c7a89; }

.group-color-automation,
.state-color-start,
.state-color-start-automation {
  background-color: #ec7b0d; }

.state-color-wait .state-name {
  color: #6c7a89; }

/* --- state adder --- */
.state-adder,
.state-goto {
  position: relative;
  display: inline-block;
  margin: 0;
  padding: 0;
  cursor: pointer;
  border-radius: 50%;
  background-color: #ffffff; }

#builder.uneditable .state-adder,
#builder.uneditable .state-goto {
  visibility: hidden;
  height: 1px; }

.state-adder-fill {
  fill: #cccccc; }

.state-goto-fill {
  fill: #8337b3; }

.state-adder:hover .state-adder-fill {
  fill: #999999; }

.state-adder-fill:active {
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.125) inset; }

.state-goto-unconnected {
  position: absolute;
  display: inline-block;
  left: 50%;
  margin-left: -100px;
  top: 100%;
  width: 200px;
  padding: 10px;
  border-radius: 6px;
  border: 0;
  color: #ffffff;
  font-size: 14px;
  line-height: 18px;
  vertical-align: middle;
  /*
  animation-name: goto-connect;
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  */ }

.state-goto-unconnected-tip {
  position: absolute;
  display: block;
  width: 10px;
  height: 10px;
  left: 50%;
  top: 0;
  margin: -5px 0 0 -5px;
  -ms-transform: rotate(45deg);
  /* IE 9 */
  -webkit-transform: rotate(45deg);
  /* Safari */
  transform: rotate(45deg); }

/* --- subscriber count --- */
.badge-subscriber-count {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  margin: 0 0 0 10px;
  padding: 2px 5px 2px 26px;
  color: #6c7a89;
  line-height: 17px;
  font-size: 15px;
  vertical-align: text-top;
  border: none;
  border-radius: 12px;
  background: #ffffff url(icons/icon_subscriber_count.png) no-repeat scroll 5px 2px; }

.action-type-wait .badge-subscriber-count {
  border: 1px solid #6c7a89; }

/* --- state delete --- */
.delete-state {
  position: absolute;
  display: none;
  top: 0;
  right: 0;
  width: 26px;
  height: 26px;
  margin: -13px -13px 0 0;
  border: 2px solid #ffffff;
  border-radius: 50%;
  background: #d9534f url(icons_sprite.png) no-repeat scroll -295px -516px;
  cursor: pointer; }

.delete-state:hover {
  background-color: #d2322d; }

.state-action:hover .delete-state,
.state-decision:hover .delete-state {
  display: block; }

#builder.dragula-dragging .state-action:hover .delete-state,
#builder.dragula-dragging .state-decision:hover .delete-state {
  display: none; }

/* --- branch --- */
.branch {
  position: relative;
  display: inline-block;
  text-align: center;
  vertical-align: top;
  margin: 0 25px;
  min-width: 420px; }

.branch-child {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 100%; }

.decision-label {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 44px;
  margin: 20px 0;
  padding: 0;
  background: #ffffff none repeat scroll 0 0;
  border: 1px solid #cccccc;
  border-radius: 50%;
  color: #333333;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  vertical-align: middle;
  cursor: default;
  z-index: 500; }

.decision-label-yes {
  border: 2px solid #60cb98;
  color: #60cb98; }

.decision-label-no {
  border: 2px solid #eb7b7a;
  color: #eb7b7a; }

/* --- z-indizes --- */
.jsplumb-connector {
  z-index: 1; }

.branch {
  z-index: 2; }

.jsplumb-endpoint {
  z-index: 3; }

/* --- Modal --- */
.modal-dialog-role {
  outline: none; }

.modal-lg {
  margin: 5% auto;
  outline: none;
  width: 765px; }

.modal-info .modal-header {
  background-color: #5bc0de;
  border-radius: 4px 4px 0 0;
  color: #ffffff; }

/* --- Panels --- */
#zoom-panel {
  position: fixed;
  display: inline-block;
  left: 20px;
  top: 70px;
  margin: 0;
  padding: 5px;
  border: 1px solid #cccccc;
  border-radius: 6px;
  background-color: #ffffff;
  font-size: 0; }

#automation-panel {
  position: fixed;
  display: inline-block;
  right: 40px;
  top: 70px;
  margin: 0;
  padding: 5px;
  border: 1px solid #cccccc;
  border-radius: 6px;
  background-color: #ffffff;
  font-size: 0; }

.sidebar-open #automation-panel {
  right: 540px; }

.panel-button {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 30px;
  line-height: 30px;
  padding: 0;
  margin: 0;
  border-right: 1px solid #cccccc;
  background: transparent none no-repeat scroll center center;
  vertical-align: middle;
  cursor: pointer; }

.panel-button.zoom-out {
  background-image: url(icons/zoom_out.png); }

.panel-button.zoom-in {
  background-image: url(icons/zoom_in.png); }

.panel-button.zoom-original {
  background-image: url(icons/zoom_original.png); }

.panel-button.zoom-fit {
  background-image: url(icons/zoom_to_fit.png);
  border-right: 0; }

.panel-button.automation-settings {
  background-image: url(icons/automation_settings.png);
  border-right: 0; }

.panel-label {
  position: relative;
  display: inline-block;
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  margin: 0;
  border-right: 1px solid #cccccc;
  background: transparent none no-repeat scroll center center;
  font-size: 18px;
  line-height: 30px;
  vertical-align: middle;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  color: #cccccc;
  text-align: center;
  cursor: default; }

/* Modal form */
.modal-state-settings .modal-header,
.modal-move-copy .modal-header {
  border-radius: 4px 4px 0 0;
  padding: 15px 20px; }

.modal-state-settings .modal-state-title,
.modal-move-copy .modal-state-title {
  position: relative;
  display: block;
  padding: 0;
  margin: 0;
  line-height: 32px;
  font-size: 26px;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  color: #ffffff; }

.modal-state-icon {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 32px;
  padding: 0;
  margin: 0 15px 0 0;
  background-color: transparent;
  background-repeat: no-repeat;
  vertical-align: text-top; }

.settings-create-state {
  position: relative;
  display: block;
  font-size: 0px; }

.settings-create-state .settings-create-state-row {
  position: relative;
  display: block;
  padding: 10px; }

.modal-state-settings .modal-header.state-color-wait {
  background-color: #6c7a89; }

.modal-state-settings .modal-state-wait .modal-state-icon {
  background-image: url(icons/icon_state_wait_white.png); }

.create-icon-wrapper {
  position: relative;
  display: inline-block;
  width: 140px;
  padding: 5px 20px;
  text-align: center;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  font-size: 12px;
  line-height: 16px;
  color: #333333;
  vertical-align: top;
  cursor: pointer;
  white-space: normal;
  border: 0;
  border-radius: 6px;
  margin: 0 18px; }

.create-icon-wrapper:hover {
  background-color: #eeeeee; }

[class^="create-state-icon-"],
[class*=" create-state-icon-"] {
  position: relative;
  display: block;
  width: 100px;
  height: 100px;
  margin: 0 auto 5px auto;
  border-radius: 6px;
  background-repeat: no-repeat;
  background-position: center center; }

.create-state-icon-decision {
  background-image: url(icons/create_icon_decision.png); }

.create-state-icon-goto {
  background-image: url(icons/create_icon_goto.png); }

.create-state-icon-email {
  background-image: url(icons/create_icon_email.png); }

.create-state-icon-sms {
  background-image: url(icons/create_icon_sms.png); }

.create-state-icon-notify-by-email {
  background-image: url(icons/create_icon_notify_by_email.png); }

.create-state-icon-notify-by-sms {
  background-image: url(icons/create_icon_notify_by_sms.png); }

.create-state-icon-outbound {
  background-image: url(icons/create_icon_outbound.png); }

.create-state-icon-tagging {
  background-image: url(icons/create_icon_tagging.png); }

.create-state-icon-untagging {
  background-image: url(icons/create_icon_untagging.png); }

.create-state-icon-setfield {
  background-image: url(icons/create_icon_setfield.png); }

.create-state-icon-unsubscribe {
  background-image: url(icons/create_icon_unsubscribe.png); }

.create-state-icon-startautomation {
  background-image: url(icons/create_icon_startautomation.png); }

.create-state-icon-stopautomation {
  background-image: url(icons/create_icon_stopautomation.png); }

.create-state-icon-wait {
  background-image: url(icons/create_icon_wait.png); }

.create-state-icon-exit {
  background-image: url(icons/create_icon_exit.png); }

.create-state-icon-splittest {
  background-image: url(icons/create_icon_splittest.png); }

.create-state-icon-facebook-audience {
  background-image: url(icons/create_icon_fbaudience.png); }

.create-icon-wrapper .not-allowed {
  cursor: not-allowed;
  background-color: #eeeeee; }

/* --- statistics --- */
.state-content {
  position: relative;
  cursor: pointer;
  padding: 15px; }

.state-stats {
  position: relative;
  display: block;
  margin: 5px 10px;
  padding: 5px 0 0 0;
  border-top: 1px solid rgba(255, 255, 255, 0.5);
  line-height: 20px;
  vertical-align: middle; }

.state-stats-email {
  min-width: 300px; }

.state-stats-sms {
  min-width: 200px; }

.state-stats a,
.state-stats a:active,
.state-stats a:visited,
.state-stats a:focus,
.state-stats span {
  position: relative;
  display: inline-block;
  font-size: 16px;
  text-align: center;
  color: #ffffff;
  cursor: pointer;
  text-decoration: none; }

.state-stats span {
  padding-right: 10px;
  font-weight: lighter; }

.state-stats a:hover {
  text-decoration: none; }

.state-stats-email-sent {
  background: transparent url(icons/icon_stats_sent.png) scroll no-repeat 0 2px;
  padding: 0 10px 0 25px; }

.state-stats-email-opened {
  background: transparent url(icons/icon_stats_opened.png) scroll no-repeat 0 2px;
  padding: 0 10px 0 21px; }

.state-stats-email-clicked {
  background: transparent url(icons/icon_stats_clicked.png) scroll no-repeat 0 1px;
  padding: 0 0 0 21px; }

.state-stats-splittest-started {
  background: transparent url(icons/icon_stats_started.png) scroll no-repeat 0 2px;
  padding: 0 10px 0 21px; }

.state-stats-splittest-finsihed {
  background: transparent url(icons/icon_stats_finished.png) scroll no-repeat 0 1px;
  padding: 0 0 0 21px; }

.state-stats-splittest-sent {
  background: transparent url(icons/icon_stats_splittest_sent.png) scroll no-repeat 0 2px;
  padding: 0 10px 0 21px; }

.state-stats-splittest-count {
  background: transparent url(icons/icon_stats_splittest_count.png) scroll no-repeat 0 2px;
  padding: 0 0 0 21px; }

/* --- decision --- */
.decision-segment {
  position: relative;
  display: block;
  padding: 10px;
  border: 1px solid #999999;
  border-radius: 6px;
  background: #f7f7f7 none repeat scroll 0 0; }

.decision-segment-op {
  position: relative;
  display: block;
  width: 200px;
  white-space: nowrap;
  font-size: 0;
  margin: 0 auto 5px auto;
  padding: 0;
  vertical-align: top;
  text-align: center; }

.decision-segment-op .op-and,
.decision-segment-op .op-or {
  position: relative;
  display: inline-block;
  padding: 0;
  width: 50px;
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  color: #333333;
  text-align: center;
  border: 1px solid #999999;
  background: #f7f7f7 none repeat scroll 0 0;
  cursor: pointer; }

.decision-segment-op .op-and {
  border-radius: 6px 0 0 6px; }

.decision-segment-op .op-or {
  border-left: 0;
  border-radius: 0 6px 6px 0; }

.decision-segment-op .op-and:hover,
.decision-segment-op .op-or:hover {
  background-color: #eeeeee; }

.decision-segment-op .op-and.active,
.decision-segment-op .op-or.active {
  background-color: #60cb98;
  color: #ffffff; }

.decision-segment-op .op-and.disabled,
.decision-segment-op .op-or.disabled {
  cursor: default; }

.decision-segment-container {
  position: relative;
  display: block;
  text-align: center;
  margin: 0;
  padding: 0; }

.decision-segment-connector {
  position: relative;
  display: block;
  width: 1px;
  margin: 0 auto;
  padding: 0;
  height: 20px;
  border-left: 1px dashed #999999; }

.decision-segment,
.decision-segment .condition-field-value {
  white-space: nowrap; }

.decision-segment select {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  font-size: 14px;
  padding-left: 0;
  padding-right: 0; }

.decision-segment select.condition-group,
.decision-segment select.condition-field {
  position: relative;
  display: inline;
  width: auto; }

.decision-segment select.condition-field {
  position: relative;
  display: inline;
  width: auto; }

.decision-segment select.condition-value {
  position: relative;
  display: inline;
  width: auto; }

.decision-condition-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  padding: 0 30px 0 0; }

.decision-condition-wrapper .delete-button {
  position: absolute;
  display: block;
  top: 0px;
  right: 0px;
  margin: -5px -6px 0 0;
  width: 23px;
  height: 23px;
  color: #999999;
  font-size: 22px;
  line-height: 19px;
  vertical-align: top;
  text-align: center;
  cursor: pointer; }

.decision-condition-wrapper .delete-button:hover {
  color: #333333; }

.segement-condition {
  position: relative;
  display: block;
  margin: 5px 0;
  padding: 5px;
  background: #ffffff none no-repeat scroll 0 0;
  border: 1px solid #dddddd;
  border-radius: 6px; }

/* --- form elements --- */
.form-group-inline {
  vertical-align: top; }

.form-group-inline select,
.form-group-inline {
  display: inline;
  width: auto;
  vertical-align: top;
  margin-bottom: 15px; }

form select.form-control.condition-op {
  max-width: 100%; }

.form-group-inline .btn,
.kt-group .input-group-select .btn {
  height: 34px; }

.btn-icon-add-black,
.btn-icon-add-black.active,
.btn-icon-add-black:active {
  background-position: -292px -1973px; }

.input-group-select-inline {
  display: inline-block;
  vertical-align: top; }

.kt-group .input-group-select-inline select,
.kt-group .input-group.input-group-select select {
  width: auto !important;
  max-width: 500px;
  display: inline; }

.kt-group .input-group.input-group-select .input-group-btn {
  width: 40%;
  display: inline; }

.form-group-inline input {
  display: inline;
  vertical-align: top; }

.form-group-inline label {
  display: block; }

.input-short-number {
  width: 50px;
  text-align: center; }

.input-group.input-number {
  width: 200px; }

/* --- Messages --- */
#global-messages {
  position: relative; }

#global-messages .alert {
  position: relative;
  margin-bottom: 10px; }

#global-messages .close {
  margin-left: 10px;
  margin-top: -2px; }

#global-messages .remove {
  display: none; }

/* --- Todo list --- */
#sidebar #todolist {
  position: relative;
  display: block;
  margin: 0;
  padding: 0 0 15px 0;
  overflow-y: scroll; }

#todolist .todo-item,
#todolist .todo-item:active,
#todolist .todo-item:visited,
#todolist .todo-item:focus {
  display: block;
  margin-bottom: 10px;
  cursor: pointer;
  background: #ffffff url(icons/edit.png) no-repeat scroll 410px center;
  border-color: #999999;
  color: #333333;
  text-decoration: none; }

#todolist .todo-item:hover {
  text-decoration: none;
  color: #333333;
  background-color: #f9f9f9; }

.sidebar-separator {
  font-weight: bold;
  font-size: 14px;
  line-height: 20px;
  color: #999999;
  vertical-align: middle;
  margin: 0 0 10px 0; }

.sidebar-separator .bubble {
  position: relative;
  display: inline-block;
  font-weight: bold;
  font-size: 14px;
  line-height: 20px;
  min-width: 40px;
  border: none;
  border-radius: 10px;
  background-color: #999999;
  color: #eeeeee;
  float: right;
  text-align: center; }

.sidebar-separator.no-messages {
  color: #cccccc; }

.sidebar-separator.no-messages .bubble {
  background-color: #cccccc; }

#sidebar hr {
  border-color: #dddddd;
  margin: 0 0 10px 0; }

#todolist .todo-name {
  font-weight: bold;
  padding-right: 70px; }

#todolist .todo-message {
  padding-right: 70px; }

/* --- delete confirm ---*/
.modal-header.modal-confirm {
  background-color: #d9534f; }

/* --- move/copy --- */
.modal-header.modal-move-copy {
  background-color: #5cb85c; }

.modal-move-copy .form-type-checkbox span {
  display: inline-block;
  vertical-align: text-top;
  cursor: pointer; }

.modal-move-copy .form-type-checkbox span.disabled {
  color: #999999;
  cursor: not-allowed; }

.modal-move-copy .form-radio {
  cursor: pointer; }

/* Drag&Drop */
body.no-select *,
.modal-header,
label {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.gu-mirror {
  position: fixed;
  margin: 0;
  padding: 0;
  z-index: 5000;
  cursor: move; }

.gu-transit {
  display: none; }

#builder.dragula-dragging,
#builder.dragula-dragging *,
#builder.dragula-dragging *:active {
  cursor: grabbing;
  cursor: grabbing;
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing; }

.dragula-dropzone.dragula-goto-connect pf-goto,
.dragula-dropzone.dragula-move-copy.dragula-hide-dropin pf-action,
.dragula-dropzone.dragula-move-copy.dragula-hide-dropin pf-decision {
  display: none; }

#builder.dragula-dragging .state-action,
#builder.dragula-dragging .state-start,
#builder.dragula-dragging .state-decision,
#builder.dragula-dragging .state-goto,
#builder.dragula-dragging .state-adder {
  opacity: 0.5; }

#builder.dragula-move-copy .state-adder {
  opacity: 1; }

#builder.dragula-goto-connect .state-action.is-dropzone,
#builder.dragula-goto-connect .state-decision.is-dropzone {
  opacity: 1; }

#builder.dragula-goto-connect .dropable .state-action.is-dropzone,
#builder.dragula-goto-connect .dropable .state-decision.is-dropzone {
  -webkit-box-shadow: 0px 0px 20px 0px #8337b3;
  -moz-box-shadow: 0px 0px 20px 0px #8337b3;
  box-shadow: 0px 0px 20px 0px #8337b3;
  cursor: hand;
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab; }

#builder.dragula-goto-connect .dropable .state-action.is-dropzone *,
#builder.dragula-goto-connect .dropable .state-decision.is-dropzone * {
  cursor: hand;
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab; }

#builder.dragula-move-copy .state-adder.is-dropzone {
  display: inline-block;
  border-radius: 50%;
  -webkit-box-shadow: 0px 0px 20px 0px #5cb85c;
  -moz-box-shadow: 0px 0px 20px 0px #5cb85c;
  box-shadow: 0px 0px 20px 0px #5cb85c; }

#builder.dragula-move-copy .state-adder.is-dropzone svg path {
  fill: #5cb85c; }

#builder.dragula-move-copy .dragula-container.dropable .state-adder.is-dropzone {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none; }

.state-insert {
  position: absolute;
  display: none;
  top: 0px;
  left: 50%;
  margin: 0 0 0 -60px;
  height: 30px;
  width: 120px;
  text-align: center;
  padding: 0;
  background-color: #ffffff;
  border: 1px dashed #5cb85c;
  border-radius: 5px;
  color: #5cb85c;
  font-size: 13px;
  font-weight: normal;
  line-height: 30px;
  vertical-align: middle;
  -webkit-box-shadow: 0px 0px 20px 0px #5cb85c;
  -moz-box-shadow: 0px 0px 20px 0px #5cb85c;
  box-shadow: 0px 0px 20px 0px #5cb85c; }

.dragula-container.dropable .state-adder.is-dropzone .state-insert {
  display: block; }

#builder.dragula-dragging.dragula-move-copy .dragula-container.dropable .state-insert {
  cursor: hand;
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab; }

#main.touch-scrolling * {
  cursor: move; }

/*  Splittest */
.state-splittest-variants {
  position: relative;
  display: block;
  padding: 10px;
  margin: 5px;
  background-color: #ffffff;
  border: none;
  border-radius: 6px;
  color: #333333;
  font-size: 14px;
  cursor: pointer; }

.state-splittest-variants .state-action {
  width: 100%;
  margin: 5px 0; }

.state-splittest-variants .state-action.splittest-winner {
  border: 4px solid gold; }

.state-splittest-variants .state-action.splittest-loser {
  background-color: #dddddd; }

/* ####### Datepicker ######### */
.utils-datepicker {
  position: relative;
  display: inline-block; }

.utils-datepicker.role-time {
  width: 140px; }

.utils-datepicker .input-group input {
  width: 100px;
  text-align: center; }

.utils-datepicker .input-group input.input-mini {
  width: 40px;
  padding-left: 0px;
  padding-right: 0px; }

.utils-datepicker .input-group input.form-control.form-text.ungroup {
  margin-left: 10px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px; }

.utils-datepicker input.error {
  border-color: #ee5555; }

.utils-datepicker .input-group select {
  position: relative;
  display: inline-block;
  -webkit-appearance: none;
  background-color: transparent;
  border: none;
  cursor: pointer;
  text-align: center;
  padding: 3px 0;
  color: #333333; }

.utils-datepicker.role-date .input-group input {
  border-radius: 4px; }

.utils-datepicker .input-group .datepicker-at {
  border-left: 0; }

.utils-datepicker .input-group .datepicker-units {
  border-left: 0;
  border-right: 0; }

.utils-datepicker .input-group .datepicker-units.ungroup {
  border-right: 1px solid #cccccc;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px; }

.utils-datepicker .input-group .datepicker-hours {
  background-color: #ffffff;
  border-left: 0;
  border-right: 0; }

.utils-datepicker.role-time .input-group .datepicker-hours {
  border-left: 1px solid #cccccc; }

.utils-datepicker .input-group .datepicker-minutes {
  background-color: #ffffff;
  border-left: 0;
  border-radius: 0; }

.utils-datepicker .input-group .datepicker-oclock {
  border-left: 0;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px; }

.utils-datepicker .input-group .datepicker-spacer {
  width: 100%;
  background-color: transparent;
  border: none; }

.utils-datepicker .utils-datepicker-container {
  position: absolute;
  left: 0px;
  display: none;
  width: 300px;
  margin: 0 0 0 100px;
  padding: 4px;
  border-radius: 4px;
  background-color: #fefefe;
  border: 1px solid #aaaaaa;
  color: #333333;
  z-index: 1000;
  -webkit-user-select: none; }

.utils-datepicker .utils-datepicker-container.align-top {
  top: 0px; }

.utils-datepicker .utils-datepicker-container.align-bottom {
  bottom: 0px; }

.utils-datepicker .utils-datepicker-container.opened {
  display: inline-block; }

.utils-datepicker .utils-datepicker-container .utils-datepicker-header {
  position: relative;
  display: block;
  width: 290px;
  height: 28px;
  margin: 0 0 4px 0;
  background-color: #f0f0f0;
  border: 0 none;
  padding: 4px 0;
  border-radius: 4px; }

.utils-datepicker .utils-datepicker-container .utils-datepicker-header i.arrow,
.utils-datepicker .utils-datepicker-container .utils-datepicker-header i.arrow.arrow-left {
  position: relative;
  display: inline-block;
  width: 14px;
  height: 14px;
  margin: 4px;
  padding: 0;
  background: transparent url("icons_sprite.png") no-repeat scroll -20px -160px;
  cursor: pointer;
  vertical-align: bottom;
  opacity: 0.75; }

.utils-datepicker .utils-datepicker-container .utils-datepicker-header i.arrow:hover {
  opacity: 1; }

.utils-datepicker .utils-datepicker-container .utils-datepicker-header i.arrow.arrow-right {
  background-position: 0px -160px; }

.utils-datepicker .utils-datepicker-container .utils-datepicker-header .select-month-year {
  position: relative;
  display: inline-block;
  width: 238px;
  height: 20px;
  vertical-align: bottom;
  text-align: center; }

.utils-datepicker .utils-datepicker-container .utils-datepicker-header .select-month-year select,
.utils-datepicker .utils-datepicker-container .utils-datepicker-header .select-month-year select:focus {
  position: relative;
  display: inline-block;
  -webkit-appearance: none;
  background-color: transparent;
  border: none;
  cursor: pointer;
  letter-spacing: 2px;
  padding-left: 2px; }

.utils-datepicker .utils-datepicker-container .utils-datepicker-header .select-month-year select:hover {
  background-color: #999999;
  color: #ffffff; }

.utils-datepicker .utils-datepicker-container .days {
  position: relative;
  display: block;
  width: 290px;
  background-color: transparent;
  margin: 0;
  padding: 0px 5px;
  font-size: 0;
  white-space: normal; }

.utils-datepicker .utils-datepicker-container .days span {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 36px;
  margin: 2px;
  padding: 0;
  line-height: 36px;
  font-size: 14px;
  vertical-align: middle;
  text-align: center;
  border: none;
  color: #333333;
  cursor: pointer; }

.utils-datepicker .utils-datepicker-container .days span.weekday {
  font-weight: bold; }

.utils-datepicker .utils-datepicker-container .days span.select-day {
  font-weight: normal;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05); }

.utils-datepicker .utils-datepicker-container .days span.select-day.is-active {
  background: #bfbfbf repeat scroll 0 0;
  border-radius: 4px;
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  box-shadow: none; }

.utils-datepicker .utils-datepicker-container .days span.select-day.today {
  background: transparent linear-gradient(to bottom, #fceec1 0%, #eedc94 100%) repeat scroll 0 0;
  border-radius: 4px;
  color: #333333;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  box-shadow: none; }

.utils-datepicker .utils-datepicker-container .days span.select-day:hover {
  background: transparent linear-gradient(to bottom, #049cdb 0%, #0064cd 100%) repeat scroll 0 0;
  border-radius: 4px;
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  box-shadow: none; }

.utils-datepicker .utils-datepicker-container .days span.disabled,
.utils-datepicker .utils-datepicker-container .days span.disabled:hover {
  background: transparent;
  border-radius: 0;
  color: transparent;
  text-shadow: none;
  box-shadow: none;
  cursor: default; }

.utils-datepicker.role-weekdays .weekday,
.utils-datepicker.role-months .month,
.utils-datepicker.role-days .day {
  position: relative;
  display: inline-block;
  cursor: pointer;
  border: 1px solid #ac2925;
  border-right: 0;
  padding: 6px 10px;
  float: left;
  line-height: 1.42857;
  background-color: #d2322d;
  color: #ffffff; }

.utils-datepicker.role-weekdays .weekday.weekday-monday,
.utils-datepicker.role-months .month.month-january,
.utils-datepicker.role-days .day.day-1,
.utils-datepicker.role-days .day.day-17 {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px; }

.utils-datepicker.role-weekdays .weekday.weekday-sunday,
.utils-datepicker.role-months .month.month-december,
.utils-datepicker.role-days .day.day-16,
.utils-datepicker.role-days .day.day-31 {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-right: 1px solid; }

.utils-datepicker.role-weekdays .weekday.weekday-selected,
.utils-datepicker.role-months .month.month-selected,
.utils-datepicker.role-days .day.day-selected {
  background-color: #5cb85c;
  border-color: #398439; }

.utils-datepicker.role-days .day {
  min-width: 40px;
  text-align: center; }

.utils-datepicker.role-days .row-days {
  position: relative;
  display: block;
  height: 34px;
  margin-bottom: 5px; }

/*
Entity Selector
*/
.select-entity {
  position: relative;
  display: block;
  width: 100%;
  max-width: 732px;
  margin-bottom: 20px; }

.select-entity .select-entity-table {
  position: relative;
  display: table;
  border-collapse: separate; }

.select-entity .select-entity-cell {
  position: relative;
  display: table-cell;
  height: 34px;
  vertical-align: middle; }

.select-entity .select-entity-cell.select-type {
  width: 1%;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-right: 0;
  -webkit-border-radius: 4px 0 0 4px;
  padding: 0 0 0 10px; }

.select-entity .select-entity-cell.select-type select {
  -webkit-appearance: none;
  background-image: url(icons/select_arrows.png);
  background-position: right center;
  background-repeat: no-repeat;
  background-color: #ffffff;
  width: auto;
  min-width: 100px;
  height: 34px;
  border: 0;
  cursor: pointer;
  padding: 0 15px 0 0; }

.select-entity .select-entity-cell.select-entity {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  -webkit-border-radius: 4px 0 0 4px;
  background-image: url(icons/select_arrows.png);
  background-position: right center;
  background-repeat: no-repeat;
  padding-right: 10px;
  cursor: pointer; }

.select-entity.no-create .select-entity-cell.select-entity {
  -webkit-border-radius: 4px;
  width: 650px; }

.select-entity .select-entity-cell.select-entity .selected-name {
  position: relative;
  display: inline-block;
  width: 100%;
  max-width: 100%;
  height: 34px;
  line-height: 34px;
  padding: 0px 10px;
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }

.select-entity .select-entity-cell.select-create-name {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  -webkit-border-radius: 4px 0 0 4px; }

.select-entity.create-with-type .select-entity-cell.select-create-name {
  -webkit-border-radius: 0; }

.select-entity .select-entity-cell.select-create-name input {
  border: 0;
  height: 34px;
  width: 100%; }

.select-entity .select-entity-cell.select-button {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-left: 0;
  -webkit-border-radius: 0 4px 4px 0;
  width: 1%;
  padding: 0px 10px;
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  color: #333333;
  cursor: pointer; }

.select-entity.with-cancel .select-entity-cell.select-button {
  -webkit-border-radius: 0; }

.select-entity .select-entity-cell.select-cancel {
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-left: 0;
  -webkit-border-radius: 0 4px 4px 0;
  width: 1%;
  padding: 0 10px;
  cursor: pointer; }

.select-entity .select-entity-cell.select-cancel span {
  position: relative;
  display: inline-block;
  line-height: 25px;
  font-size: 25px;
  font-weight: bold;
  text-align: center;
  color: #333333;
  -webkit-user-select: none;
  padding: 0px 0 4px 0;
  cursor: pointer; }

.select-entity .select-entity-cell.as-button:hover {
  background-color: #dddddd; }

.select-entity .select-entity-cell.as-button:active {
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.125) inset; }

.select-entity.open .select-entity-cell.select-entity:hover {
  background-color: #ffffff; }

.select-entity .outside-label {
  position: absolute;
  display: block;
  left: 0;
  bottom: 0;
  margin: 0 0 -16px 0;
  padding: 0;
  line-height: 12px;
  font-size: 12px;
  color: #aaaaaa; }

.select-entity input.create-progress-bar {
  background: transparent no-repeat;
  background-size: 0% 100%;
  background-image: linear-gradient(#337ab7, #5bc0de); }

.select-entity .error {
  color: #d9534f; }

.select-entity .select-entity-dropdown {
  position: absolute;
  display: block;
  left: 0;
  top: 68px;
  width: 100%;
  height: auto;
  max-height: 400px;
  margin: 0;
  padding: 2px 5px 5px 5px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 0 0 6px 6px;
  z-index: 10;
  overflow-x: hidden;
  overflow-y: auto; }

.select-entity .btn-select-entity {
  z-index: 10; }

.select-entity .select-entity-dropdown .select-entity-dropdown-option {
  position: relative;
  display: block;
  padding: 2px; }

.select-entity .select-entity-dropdown .select-entity-dropdown-option .select-entity-dropdown-option-content {
  position: relative;
  display: block;
  width: 100%;
  border-bottom: 1px solid #dddddd;
  padding: 5px;
  cursor: pointer; }

.select-entity .select-entity-dropdown .select-entity-dropdown-option:last-child {
  border-bottom: 0;
  padding-bottom: 0; }

.select-entity .select-entity-dropdown .select-entity-dropdown-option-name {
  position: relative;
  display: block;
  width: 100%;
  line-height: 22px;
  font-size: 16px;
  color: #333333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }

.select-entity .select-entity-dropdown .select-entity-dropdown-option-info {
  position: relative;
  display: block;
  width: 100%;
  line-height: 16px;
  font-size: 12px;
  color: #aaaaaa; }

.select-entity .select-entity-dropdown .select-entity-dropdown-option-info .info-separator {
  padding: 10px 0; }

.select-entity .select-entity-dropdown .select-entity-dropdown-option-hover {
  background-color: #dddddd;
  border-radius: 4px; }

.select-entity .select-entity-searchbox {
  position: absolute;
  display: block;
  left: 0;
  top: 35px;
  width: 100%;
  height: 1px;
  margin: 0;
  padding: 3px 5px 0 25px;
  background: #cccccc url(icons/search.png) no-repeat scroll 5px center;
  opacity: 0.01;
  overflow: hidden;
  z-index: 1; }

.select-entity.open .select-entity-searchbox {
  opacity: 1;
  height: 33px; }

.select-entity .select-entity-searchbox input {
  width: 100%;
  height: 26px;
  padding: 0 10px;
  line-height: 20px;
  font-size: 14px;
  vertical-align: middle;
  color: #333333;
  border: 1px solid #999999;
  border-radius: 4px;
  margin: 2px 0 0 0; }

.select-entity .input-group {
  margin-top: 20px;
  width: 100%; }

.select-entity .input-group select {
  -webkit-appearance: none;
  -webkit-border-top-right-radius: 0;
  -webkit-border-bottom-left-radius: 0;
  background-image: url(icons/select_arrows.png);
  background-position: right center;
  background-repeat: no-repeat; }

.select-entity .input-group .btn-create-cancel {
  border-left: 0;
  cursor: pointer; }

.select-entity .input-group .btn-create-cancel span {
  display: inline-block;
  font-size: 25px;
  line-height: 16px;
  font-weight: bold;
  padding: 0 0 4px 0;
  cursor: pointer; }

.popover__wrapper {
  position: relative;
  margin-top: 1.5rem;
  display: inline-block; }

.popover__content {
  opacity: 0;
  left: 50px;
  position: absolute;
  transform: translate(0, 10px);
  background-color: #FFFFFF;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
  width: 250px;
  z-index: 10;
  opacity: 1;
  visibility: visible;
  transform: translate(0, -20px);
  transition: all 0.5s cubic-bezier(0.75, -0.02, 0.2, 0.97); }

.popover__content:before {
  position: absolute;
  z-index: -1;
  content: '';
  right: calc(50% - 10px);
  top: -8px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 25px 10px 0;
  border-color: transparent #FFFFFF transparent transparent;
  transition-duration: 0.3s;
  transition-property: transform; }

.popover__message {
  text-align: center; }

.popover__content__heading {
  width: 100%;
  background-color: #1d9d75;
  margin-top: 0px; }

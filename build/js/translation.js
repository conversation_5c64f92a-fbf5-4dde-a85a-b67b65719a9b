window["ktTranslations"] = window["ktTranslations"] || {};
window["ktTranslations"]["originals"] = ["entity::create::Create new Listbuilding", "entity::create::Create new Marketing Tool", "entity::create::Create new Product", "entity::create::Create new Integration", "entity::create::Create new SMS Listbuilding", "entity::create::Create new Subscription Form", "entity::create::Create new Facebook Subscription", "entity::create::Create new Notification", "entity::create::Create new Custom Field", "entity::create::Create new Newsletter / Autoresponder", "general::icon::glyph-decimal-dot", "general::error::This field is required!", "statistics::button::Add Tag", "general::button::Cancel", "general::option::Please select a Tag.", "statistics::tooltip::Remove Tag", "statistics::tab::title::Loading data", "statistics::tab::title::Statistics", "statistics::message::Statistic @@name@@ successfully created.", "statistics::tab::title::Settings", "statistics::message::The tag @@name@@ has already been added.", "general::button::Today", "general::button::Earliest date", "statistics::label::Tag", "statistics::label::Total", "statistics::message::Statistic @@name@@ successfully removed.", "statistics::message::An error occurred while deleting the statistic @@name@@.", "statistics::message::A Statistic with that name already exists.", "statistics::label::Date", "statistics::modal::title::Add Tag", "statistics::form::title::Range from", "statistics::form::title::Range to", "statistics::form::title::Chart type", "statistics::option::Progress", "statistics::option::Continuous", "statistics::option::Total", "statistics::form::title::Granularity", "statistics::option::Hourly", "statistics::option::Daily", "statistics::option::Monthly", "statistics::option::Annually", "general::form::element::title::Name", "general::form::placeholder::Insert a name...", "statistics::button::Delete statistic", "statistics::error::Please enter a name for your Statistic.", "general::confirm::Are you sure to delete @@name@@", "statistics::form::placeholder::Insert a name...", "prototype::tab::Overview", "prototype::tab::Settings", "prototype::tab::E-Mail", "calendar::tab::View", "calendar::tab::Settings", "metalabel::tab::Settings", "metasearch::title::Meta Search", "metasearch::tab::Result", "translation::title::Translations", "translation::tab::translate::All", "translation::tab::translate::Not translated", "general::label::All", "general::label::Not translated", "general::error::404", "general::error::Unable to find the automation", "general::button::Show me my automations", "general::error::Attention!", "general::error::Oops! That action is not possible!", "general::error::This automation is active and cannot be edited.", "automation::statistics::title::Automation statistics", "flowchart::message::No sms selected", "flowchart::message::No email selected", "sms::message::SMS successfully updated", "email::message::Email successfully updated", "email::button::Send gmail preview", "email::preview::The Gmail preview has been sent to @@email@@. Retrieving the inbox...", "email::preview::An error occurred. The Gmail inbox could not be retrieved. @@error@@", "email::button::Resend gmail preview", "email::preview::This email arrived in the following inboxes.", "email::preview::The Gmail preview will be sent to @@email@@", "email::preview::@@count@@ Gmail previews have been sent.", "general::status::Refreshing...", "email::preview::Calculate all missing Spamscores.", "email::preview::All Spamscores are up to date.", "email::preview::@@count@@ Gmail previews will be sent to @@email@@", "email::preview::@@count@@ Gmail previews have been sent to @@email@@. Retrieving the inboxes...", "email::preview::All email inboxes are up to date.", "automation::statistic::label::Goal", "automation::statistic::label::Contacts", "automation::statistic::label::Conversion", "automation::statistic::label::Conversion Rate", "automation::statistic::label::Revenue", "automation::statistic::label::Savings", "automation::statistic::label::Average", "automation::statistic::label::Total", "automation::statistic::label::Contacts that reached a goal or finished the campaign.", "automation::statistic::label::Average value of a contact that starts the campaign.", "automation::statistics::active", "automation::statistics::started", "automation::statistics::finished", "automation::statistics::converted", "automation::statistics::onopenend", "automation::statistics::failed", "statistics::form::button::Download as PDF", "flowchart::command::todo::Edit automation email @@name@@", "flowchart::command::todo::Edit automation sms @@name@@", "flowchart::command::todo::Edit notification email @@name@@", "flowchart::command::todo::Edit notification sms @@name@@", "flowchart::command::todo::Edit outbound @@name@@", "flowchart::command::todo::Connect Facebook Audience @@name@@", "flowchart::message::todo::condition is empty", "flowchart::message::todo::goal condition is empty", "flowchart::message::todo::goal has no following action", "flowchart::message::todo::empty segment in condition", "flowchart::message::todo::invalid tag in condition", "flowchart::message::todo::invalid tag duration in condition", "flowchart::message::todo::invalid field in condition", "flowchart::message::todo::invalid op in condition", "flowchart::message::todo::invalid value in condition", "flowchart::message::todo::no content for send action", "flowchart::message::todo::no email in send action", "flowchart::message::todo::no sms in send action", "flowchart::message::todo::no notify email send action", "flowchart::message::todo::no notify sms send action", "flowchart::message::todo::email has no subject", "flowchart::message::todo::email has no content", "flowchart::message::todo::email not published", "flowchart::message::todo::SMS has no content", "flowchart::message::todo::notification has no valid receiver", "flowchart::message::todo::invalid outbound in outbound action", "flowchart::message::todo::invalid activation url in outbound action", "flowchart::message::todo::invalid tag in action", "flowchart::message::todo::invalid field in action", "flowchart::message::todo::no target in goto action", "flowchart::message::todo::no automation in automation action", "flowchart::message::todo::invalid splittest", "flowchart::message::todo::invalid splittest duration", "flowchart::message::todo::splittest variant missing", "flowchart::message::todo::invalid splittest entity", "flowchart::message::todo::invalid splittest variant weight", "flowchart::message::todo::invalid splittest variant tag", "flowchart::message::todo::invalid splittest weights", "flowchart::message::todo::invalid action", "flowchart::message::todo::invalid node", "flowchart::message::todo::exit has next state", "flowchart::message::todo::automation cant start itself", "flowchart::message::todo::automation cant stop itself", "flowchart::message::todo::invalid time delay in wait", "flowchart::message::todo::invalid field type", "flowchart::message::todo::invalid delay type", "flowchart::message::todo::invalid weekday constraint", "flowchart::message::todo::invalid field value", "flowchart::message::todo::field value out of range", "flowchart::message::todo::invalid copy field", "flowchart::message::todo::start not found", "flowchart::message::todo::possible endless loop", "flowchart::message::todo::unreachable nodes", "flowchart::message::todo::no action found", "flowchart::message::todo::invalid field operator", "flowchart::message::todo::invalid facebook audience", "flowchart::message::todo::facebook audience not connected", "flowchart::message::todo::invalid tag or field in action", "flowchart::message::todo::full contact api key missing", "flowchart::message::todo::no calendar selected", "entity::delete::Delete confirm", "general::option::January", "general::option::February", "general::option::March", "general::option::April", "general::option::May", "general::option::June", "general::option::July", "general::option::August", "general::option::September", "general::option::October", "general::option::November", "general::option::December", "general::option::weekday:short::Mon", "general::option::weekday:short::Tue", "general::option::weekday:short::Wed", "general::option::weekday:short::Thu", "general::option::weekday:short::Fri", "general::option::weekday:short::Sat", "general::option::weekday:short::Sun", "calendar::message::This calendar cannot be edited due to the following dependencies:", "calendar::message::Loading dependencies...", "calendar::modal::title::Add entry", "entity::link::edit::[@@name@@](@@link@@)", "metasearch::message::Please select at least 1 object.", "metasearch::message::Add labels to @@entityname@@.", "metasearch::message::Add labels to @@count@@ objects.", "metasearch::message::Remove labels from @@entityname@@.", "metasearch::message::Remove labels from @@count@@ objects.", "email::spamscore::Calculate SpamScore", "email::spamscore::Your SpamScore is too high!", "email::spamscore::Your SpamScore is high.", "email::spamscore::Your SpamScore is OK.", "quickhelp::form::element:title::Title", "quickhelp::form::placeholder::Enter a title...", "quickhelp::form::element:title::Content", "quickhelp::form::placeholder::Please enter a help text...", "quickhelp::error::An error occurred while deleting the quickhelp.", "general::button::Create", "general::error::An unexpected error occurred. A notification has been sent to our tech support.", "general::message::You have reached the limit of @@count@@ @@type@@. Please upgrade your account.", "entity::create::To create a new Custom Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new Inline Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new Widget, the create dialog will be opened in a new tab.", "entity::create::To create a new Raw Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new Request By E-Mail, the create dialog will be opened in a new tab.", "entity::create::To create a new API Key, the create dialog will be opened in a new tab.", "entity::create::To create a new SMS Listbuilding, the create dialog will be opened in a new tab.", "entity::create::To create a new SMS Listbuilding (Nexmo), the create dialog will be opened in a new tab.", "entity::create::To create a new SMS Listbuilding (Twilio), the create dialog will be opened in a new tab.", "entity::create::To create a new DigiStore24 Product, the create dialog will be opened in a new tab.", "entity::create::To create a new Affilicon Product, the create dialog will be opened in a new tab.", "entity::create::To create a new Clickbank Product, the create dialog will be opened in a new tab.", "entity::create::To create a new Paypal Product, the create dialog will be opened in a new tab.", "entity::create::To create a new Wufoo Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new Facebook Button, the create dialog will be opened in a new tab.", "entity::create::To create a new Combo Box, the create dialog will be opened in a new tab.", "entity::create::To create a new Facebook Fanpage, the create dialog will be opened in a new tab.", "entity::create::To create a new Leadpages Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new OptimizePress Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new Wistia Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new Thrive Subscription Form, the create dialog will be opened in a new tab.", "entity::create::To create a new Business Reader Pro, the create dialog will be opened in a new tab.", "entity::create::To create a new Business Card Reader Event, the create dialog will be opened in a new tab.", "entity::create::To create a new Email Newsletter, the create dialog will be opened in a new tab.", "entity::create::To create a new SMS Newsletter, the create dialog will be opened in a new tab.", "entity::create::To create a new Email Autoresponder, the create dialog will be opened in a new tab.", "entity::create::To create a new SMS Autoresponder, the create dialog will be opened in a new tab.", "entity::create::To create a new Smartlink, the create dialog will be opened in a new tab.", "entity::create::To create a new Tagging Pixel, the create dialog will be opened in a new tab.", "entity::create::After finishing the create process, return to this tab and click the refresh button.", "entity::create::We are working as fast as we can to integrate all create dialogs into the cockpit.", "flowchart::condition::verb::received", "flowchart::condition::verb::started", "flowchart::condition::verb::finished", "flowchart::condition::verb::sent", "flowchart::condition::verb::opened", "flowchart::condition::verb::clicked", "flowchart::condition::verb::viewed", "flowchart::condition::verb::triggered", "flowchart::condition::verb::converted", "flowchart::condition::verb::Ready", "flowchart::condition::verb::Started", "flowchart::condition::verb::In Progress", "flowchart::condition::verb::Finished", "flowchart::condition::verb::subscribed", "flowchart::condition::verb::canceled", "flowchart::condition::verb::resumed", "flowchart::condition::verb::reached", "flowchart::condition::verb::registered", "general::option::Please select", "flowchart::smarttag::category::Newsletter / Autoresponder", "flowchart::smarttag::category::Conversion", "flowchart::smarttag::category::SMS Listbuilding", "flowchart::smarttag::category::Subscription Form", "flowchart::smarttag::category::Facebook Button", "flowchart::smarttag::category::Payment", "flowchart::smarttag::category::Refund", "flowchart::smarttag::category::Chargeback", "flowchart::smarttag::category::Rebill", "flowchart::smarttag::category::Digistore Rebill Canceled Last Day", "flowchart::smarttag::category::Payment Completed", "flowchart::smarttag::category::Payment Expired", "flowchart::smarttag::category::Digistore Affiliation", "flowchart::smarttag::category::Plugin Inbound Ready", "flowchart::smarttag::category::Plugin Inbound Started", "flowchart::smarttag::category::Plugin Inbound In Progress", "flowchart::smarttag::category::Plugin Inbound Finished", "general::button::Save", "general::button::Delete", "general::button::Edit", "general::button::Refresh", "general::button::Duplicate", "general::button::Send Email", "general::button::Send SMS", "general::button::Download", "general::option::weekday::short::Mon", "general::option::weekday::short::Tue", "general::option::weekday::short::Wed", "general::option::weekday::short::Thu", "general::option::weekday::short::Fri", "general::option::weekday::short::Sat", "general::option::weekday::short::Sun", "general::option::Please select at least 1 day.", "entity::option::Insert name and hit enter", "general::error::An unexpected error occured.", "entity::message::You do not have any objects created yet.", "entity::message::No objects available.", "entity::message::There are no matches for this filter.", "entity::option::Please select or create", "entity::data::ID: @@id@@ | Type: @@type@@ | Category: @@category@@", "entity::data::ID: @@id@@ | Type: @@type@@", "general::option::Please select at least 1 month.", "general::option::Please select at least 1 option.", "general::option::Monday", "general::option::Tuesday", "general::option::Wednesday", "general::option::Thursday", "general::option::Friday", "general::option::Saturday", "general::option::Sunday", "general::option::Please select at least 1 weekday.", "general::option::Minutes", "general::option::Hours", "general::option::Days", "general::option::Weeks", "general::option::Months", "general::option::Years", "flowchart::condition::operation::is empty", "flowchart::condition::operation::is not empty", "flowchart::condition::operation::is equal", "flowchart::condition::operation::is not equal", "flowchart::condition::operation::contains", "flowchart::condition::operation::does not contain", "flowchart::condition::operation::starts with", "flowchart::condition::operation::does not start with", "flowchart::condition::operation::ends with", "flowchart::condition::operation::does not end with", "flowchart::condition::operation::is less than", "flowchart::condition::operation::is less or equal than", "flowchart::condition::operation::is greater than", "flowchart::condition::operation::is greater or equal than", "flowchart::condition::operation::time:is before time", "flowchart::condition::operation::time:is after time", "flowchart::condition::operation::date:is equal", "flowchart::condition::operation::date:is not equal", "flowchart::condition::operation::date:is before today", "flowchart::condition::operation::date:is after today", "flowchart::condition::operation::date:is weekday", "flowchart::condition::operation::date:is month", "flowchart::condition::operation::date:is day of month", "flowchart::condition::operation::date:is holiday from calendars", "flowchart::condition::operation::datetime:is equal", "flowchart::condition::operation::datetime:is not equal", "flowchart::condition::operation::datetime:is before", "flowchart::condition::operation::datetime:is after", "flowchart::condition::operation::datetime:is weekday", "flowchart::condition::operation::datetime:is month", "flowchart::condition::operation::datetime:is day of month", "flowchart::condition::operation::datetime:is before time", "flowchart::condition::operation::datetime:is after time", "flowchart::form::element:title::Condition", "flowchart::form::element:title::Action", "flowchart::form::element:title::Interval", "flowchart::option::at any time", "flowchart::option::within last 24 hours", "flowchart::option::within last 3 days", "flowchart::option::within last 30 days", "flowchart::option::within last 90 days", "flowchart::option::within last year", "flowchart::condition::operation::is active email subscriber", "flowchart::condition::operation::is not active email subscriber", "flowchart::condition::operation::is active sms subscriber", "flowchart::condition::operation::is not active sms subscriber", "flowchart::condition::operation::today is equal", "flowchart::condition::operation::today is not equal", "flowchart::condition::operation::today is before", "flowchart::condition::operation::today is after", "flowchart::condition::operation::today is weekday", "flowchart::condition::operation::today is month", "flowchart::condition::operation::today is day of month", "flowchart::condition::operation::today is holiday from calendars", "flowchart::condition::operation::today is before time", "flowchart::condition::operation::today is after time", "flowchart::option::What do you want to check?", "flowchart::condition::type::Contact status", "flowchart::condition::type::Field", "flowchart::condition::type::Today", "general::button::AND", "general::button::OR", "flowchart::message::This Automation has no start condition and can therefore only be started by other automations.", "flowchart::form::element:title::Revenue", "flowchart::form::element:title::Cost Savings", "flowchart::form::element:title::Color", "flowchart::form::element:title::Conditions for the contact", "general::button::Close", "general::error::Please insert an interger.", "general::label::gender::male", "general::label::gender::female", "general::label::gender::not available", "general::form::element:title::Name", "flowchart::form::element:title::Custom field containing the first name", "flowchart::form::element:title::Set custom field based on the gender", "flowchart::form::element::title::Custom field to store gender", "flowchart::form::element::title::Value for gender male", "general::form::placeholder::Insert a value...", "flowchart::form::element::title::Value for gender female", "flowchart::form::element::title::Value for gender undefined", "flowchart::form::element:title::Tag contact based on the gender", "flowchart::form::element::title::Tag for gender male", "flowchart::form::element::title::Tag for gender female", "flowchart::form::element::title::Tag for gender undefined", "general::error::Duplicate selection!", "flowchart::form::element:title::Custom field for first name", "flowchart::form::element:title::Custom field for last name", "flowchart::form::element:title::Email", "flowchart::option::Wait until the contact can receive emails.", "flowchart::message::Loading Facebook Audiences...", "flowchart::message::You do not have any Facebook Audiences", "flowchart::form::element:title::Ignore Facebook Audience expired access", "flowchart::form::element:title::Ignore Fullcontact rate limit", "flowchart::form::element:title::Always overwrite with fullcontact data", "flowchart::command::Disconnect goto", "flowchart::form::element:title::Target", "email::option::Fixed Email address", "email::option::From dispatch profile", "flowchart::form::element:title::Email address", "flowchart::form::placeholder::Insert the email address to which to send the notification", "flowchart::form::element:title::SMS", "flowchart::form::element:title::Mobile number", "flowchart::form::placeholder::Insert the mobile number to which to send the notification", "flowchart::form::element:title::Outbound", "flowchart::option::Current time", "flowchart::option::Fixed time", "flowchart::option::Ceil hour", "flowchart::option::Floor hour", "flowchart::option::Round hour", "flowchart::option::SubscriberID", "flowchart::option::EmailAddress", "flowchart::option::SubscriptionStatus", "flowchart::option::BounceType", "flowchart::option::SubscriptionIP", "flowchart::option::SubscriptionReferrer", "flowchart::option::PhoneNumber", "flowchart::option::SMSSubscriptionStatus", "flowchart::option::SMSBounceType", "flowchart::option::SubscriptionSMS", "flowchart::option::OptInDate", "flowchart::option::SubscriptionDate", "flowchart::option::UnsubscriptionDate", "flowchart::option::SMSSubscriptionDate", "flowchart::option::SMSUnsubscriptionDate", "flowchart::message::No Contact fields can be copied to a time custom field.", "flowchart::option::set field value", "flowchart::option::copy from field", "flowchart::option::copy from subscriber", "flowchart::option::empty field", "flowchart::option::prepend string", "flowchart::option::append string", "flowchart::option::replace string", "flowchart::option::generate MD5 hash", "flowchart::option::truncate url query", "flowchart::option::truncate url path", "flowchart::option::add number", "flowchart::option::subtract number", "flowchart::option::random number", "flowchart::option::time now", "flowchart::option::floor full hour", "flowchart::option::ceil full hour", "flowchart::option::round full hour", "flowchart::option::datetime now", "flowchart::option::datetime increase", "flowchart::option::datetime decrease", "flowchart::option::now next month", "flowchart::option::now next year", "flowchart::option::field next month", "flowchart::option::field next year", "flowchart::option::ultimo", "flowchart::option::easter date", "flowchart::form::placeholder::Please enter a value...", "flowchart::form::placeholder::Please enter the string to prepend...", "flowchart::form::placeholder::Please enter the string to append...", "flowchart::form::placeholder::Please enter the string to replace...", "flowchart::form::placeholder::Please enter the string to replace with...", "general::option::Minimum", "general::option::Maximum", "flowchart::form::element:title::Custom field", "flowchart::form::element:title::Operation", "flowchart::form::element:title::Only on the following weekdays", "flowchart::form::element:title::Time", "flowchart::form::element:title::Wait until the contact can receive sms.", "flowchart::form::element:title::Weight", "flowchart::form::element:title::Splittest Target", "flowchart::command::Duplicate Splittest Variant", "flowchart::message::This Splittest is running/finished and can therefore not be edited.", "flowchart::form::element:title::Duration", "flowchart::form::element:title::Splittest Variants", "flowchart::form::element:title::Automation", "flowchart::form::element:title::Tag", "flowchart::option::From Custom Field", "flowchart::option::From Birthday Field", "flowchart::option::From Calendar", "general::option::per hour", "general::option::per day", "general::option::per week", "general::option::per month", "general::option::per year", "flowchart::option::Limiter", "flowchart::form::element:title::Delay", "flowchart::form::element:title::Exclude holidays from calendars", "flowchart::form::element:title::Calendars", "flowchart::form::element:title::Wait for certain hours", "flowchart::form::element:title::Wait for certain weekdays", "flowchart::form::element:title::Wait for certain days of the month", "flowchart::form::element:title::Wait for certain weeks", "flowchart::form::element:title::Wait for certain  months", "flowchart::form::element:title::Wait until specific time?", "entity::type::Automation", "entity::type::Rule", "entity::type::Automation Email", "entity::type::Automation SMS", "entity::type::Notification Email", "entity::type::Notification SMS", "entity::type::Newsletter/Autoresponder Email", "entity::type::Newsletter/Autoresponder SMS", "entity::type::Email Newsletter", "entity::type::SMS Newsletter", "entity::type::Email Autoresponder", "entity::type::Email Autoresponder (Field)", "entity::type::Email Autoresponder (Birthday)", "entity::type::SMS Autoresponder", "entity::type::SMS Autoresponder (Field)", "entity::type::SMS Autoresponder (Birthday)", "entity::type::Single", "entity::type::Paragraph", "entity::type::Email", "entity::type::Number", "entity::type::URL", "entity::type::Date", "entity::type::Time", "entity::type::Datetime", "entity::type::HTML", "entity::type::Decimal", "entity::type::Subscription form - Custom", "entity::type::Subscription form - Raw", "entity::type::Subscription form - Facebook Button", "entity::type::Subscription form - Combo-Box", "entity::type::Subscription form - Inline", "entity::type::Subscription form - Leadpages", "entity::type::Subscription form - Wistia Turnstile", "entity::type::Subscription form - OptimizePress", "entity::type::Subscription form - Thrive", "entity::type::Subscription form - Widget", "entity::type::Request", "entity::type::Fanpage", "entity::type::PayPal product", "entity::type::DigiStore24 product", "entity::type::AffiliCon product", "entity::type::Clickbank product", "entity::type::API key", "entity::type::Wufoo Subscription form", "entity::type::SMS Listbuilding", "entity::type::SMS Listbuilding Nexmo", "entity::type::SMS Listbuilding Twilio", "entity::type::Business card", "entity::type::Business Card Reader Event", "entity::type::Smart Tag", "entity::type::Manual Tag", "entity::type::Smart Link", "entity::smarttag::Newsletter/Autoresponser - Sent", "entity::smarttag::Newsletter/Autoresponser - Opened", "entity::smarttag::Newsletter/Autoresponser - Link clicked", "entity::smarttag::Newsletter/Autoresponser - Browser view", "entity::smarttag::Newsletter/Autoresponser - Conversion", "entity::smarttag::Kajabi Activation", "entity::smarttag::Kajabi Deactivation", "entity::smarttag::Outbound Activation", "entity::smarttag::Tagging Pixel", "entity::smarttag::Email - Sent", "entity::smarttag::Email - Opened", "entity::smarttag::Email - Link clicked", "entity::smarttag::Email - Browser view", "entity::smarttag::SMS - Sent", "entity::smarttag::SMS - Link clicked", "entity::smarttag::Automation started", "entity::smarttag::Automation finished", "entity::smarttag::Subscribed by API", "entity::smarttag::Subscribed by Business Card Reader", "entity::smarttag::Subscribed by Business Card Reader Event", "entity::smarttag::Subscribed by Email Request", "entity::smarttag::Subscribed by SMS Request", "entity::smarttag::Subscribed by Form", "entity::smarttag::Subscribed by Facebook", "entity::smarttag::Subscribed by Payment", "entity::smarttag::Payment Refunded", "entity::smarttag::Payment Chargedback", "entity::smarttag::Rebill Canceled", "entity::smarttag::Rebill Canceled Last Day", "entity::smarttag::Rebill Resumed", "entity::smarttag::Payment Completed", "entity::smarttag::Payment Expired", "entity::smarttag::Digistore Affiliation", "entity::smarttag::Plugin Inbound Ready", "entity::smarttag::Plugin Inbound Started", "entity::smarttag::Plugin Inbound In Progress", "entity::smarttag::Plugin Inbound Finished", "entity::smarttag::Added to Facebook Audience", "entity::type::Countdown", "entity::type::Kajabi", "entity::type::Outbound", "entity::type::Plugin", "entity::type::Tagging Pixel", "entity::type::Automation Template", "entity::type::Webinar", "entity::type::Zapier", "entity::type::Statistics", "entity::type::Exit Lightbox", "entity::type::Feedback Form", "entity::type::One Time Offer", "entity::type::Social Proof Counter", "entity::type::Website Splittest", "entity::type::Splittest", "entity::type::Calendar", "entity::type::Metalabel", "entity::type::Signature", "entity::type::Double OptIn Process", "entity::create::Create new Automation", "entity::create::Create new Rule", "entity::create::Create new Automation Email", "entity::create::Create new Automation SMS", "entity::create::Create new Notification Email", "entity::create::Create new Notification SMS", "entity::create::Create new Newsletter/Autoresponder Email", "entity::create::Create new Newsletter/Autoresponder SMS", "entity::create::Create new Email Newsletter", "entity::create::Create new SMS Newsletter", "entity::create::Create new Email Autoresponder", "entity::create::Create new SMS Autoresponder", "entity::create::Create new Custom Field (Single)", "entity::create::Create new Custom Field (Paragraph)", "entity::create::Create new Custom Field (Email)", "entity::create::Create new Custom Field (Number)", "entity::create::Create new Custom Field (URL)", "entity::create::Create new Custom Field (Date)", "entity::create::Create new Custom Field (Time)", "entity::create::Create new Custom Field (Datetime)", "entity::create::Create new Custom Field (HTML)", "entity::create::Create new Custom Field (Decimal)", "entity::create::Create new Subscription form - Custom", "entity::create::Create new Subscription form - Raw", "entity::create::Create new Subscription form - Facebook Button", "entity::create::Create new Subscription form - Combo-Box", "entity::create::Create new Subscription form - Inline", "entity::create::Create new Subscription form - Leadpages", "entity::create::Create new Subscription form - Wistia Turnstile", "entity::create::Create new Subscription form - OptimizePress", "entity::create::Create new Subscription form - Thrive", "entity::create::Create new Subscription form - Widget", "entity::create::Create new Request", "entity::create::Create new Fanpage", "entity::create::Create new PayPal product", "entity::create::Create new DigiStore24 product", "entity::create::Create new AffiliCon product", "entity::create::Create new Clickbank product", "entity::create::Create new API key", "entity::create::Create new Wufoo Subscription form", "entity::create::Create new SMS Listbuilding Nexmo", "entity::create::Create new SMS Listbuilding Twilio", "entity::create::Create new Business card", "entity::create::Create new Business Card Reader Event", "entity::create::Create new Smart Tag", "entity::create::Create new Manual Tag", "entity::create::Create new Smart Link", "entity::create::Create new Newsletter/Autoresponser", "entity::create::Create new Kajabi", "entity::create::Create new Outbound", "entity::create::Create new Tagging Pixel", "entity::create::Create new Email", "entity::create::Create new SMS", "entity::create::Create new API Key", "entity::create::Create new Business Card Reader", "entity::create::Create new Email Request", "entity::create::Create new SMS Request", "entity::create::Create new Facebook Button", "entity::create::Create new Facebook Audience", "entity::create::Create new Countdown", "entity::create::Create new Plugin", "entity::create::Create new Automation Template", "entity::create::Create new Webinar", "entity::create::Create new Zapier", "entity::create::Create new Statistics", "entity::create::Create new Exit Lightbox", "entity::create::Create new Feedback Form", "entity::create::Create new One Time Offer", "entity::create::Create new Social Proof Counter", "entity::create::Create new Website Splittest", "entity::create::Create new Splittest", "entity::create::Create new Calendar", "entity::create::Create new Metalabel", "entity::create::Create new Signature", "entity::create::Create new Double OptIn Process", "entity::create::You have not created any Automations yet.", "entity::create::You have not created any Rules yet.", "entity::create::You have not created any Automation Emails yet.", "entity::create::You have not created any Automation SMS yet.", "entity::create::You have not created any Notification Emails yet.", "entity::create::You have not created any Notification SMS yet.", "entity::create::You have not created any Newsletter/Autoresponder Emails yet.", "entity::create::You have not created any Newsletter/Autoresponder SMS yet.", "entity::create::You have not created any Email Newsletter yet.", "entity::create::You have not created any SMS Newsletter yet.", "entity::create::You have not created any Email Autoresponders yet.", "entity::create::You have not created any SMS Autoresponders yet.", "entity::create::You have not created any Custom Fields (Single) yet.", "entity::create::You have not created any Custom Fields (Paragraph) yet.", "entity::create::You have not created any Custom Fields (Email) yet.", "entity::create::You have not created any Custom Fields (Number) yet.", "entity::create::You have not created any Custom Fields (URL) yet.", "entity::create::You have not created any Custom Fields (Date) yet.", "entity::create::You have not created any Custom Fields (Time) yet.", "entity::create::You have not created any Custom Fields (Datetime) yet.", "entity::create::You have not created any Custom Fields (HTML) yet.", "entity::create::You have not created any Custom Fields (Decimal) yet.", "entity::create::You have not created any Subscription forms - Custom yet.", "entity::create::You have not created any Subscription forms - Raw yet.", "entity::create::You have not created any Subscription forms - Facebook Button yet.", "entity::create::You have not created any Subscription forms - Combo-Box yet.", "entity::create::You have not created any Subscription forms - Inline yet.", "entity::create::You have not created any Subscription forms - Leadpages yet.", "entity::create::You have not created any Subscription forms - Wistia Turnstile yet.", "entity::create::You have not created any Subscription forms - OptimizePress yet.", "entity::create::You have not created any Subscription forms - Thrive yet.", "entity::create::You have not created any Subscription forms - Widget yet.", "entity::create::You have not created any Requests yet.", "entity::create::You have not created any Fanpages yet.", "entity::create::You have not created any PayPal products yet.", "entity::create::You have not created any DigiStore24 products yet.", "entity::create::You have not created any AffiliCon products yet.", "entity::create::You have not created any Clickbank products yet.", "entity::create::You have not created any API keys yet.", "entity::create::You have not created any Wufoo Subscription forms yet.", "entity::create::You have not created any SMS Listbuildings yet.", "entity::create::You have not created any SMS Listbuildings Nexmo yet.", "entity::create::You have not created any SMS Listbuildings Twilio yet.", "entity::create::You have not created any Business cards yet.", "entity::create::You have not created any Business Card Reader Events yet.", "entity::create::You have not created any Smart Tags yet.", "entity::create::You have not created any Manual Tags yet.", "entity::create::You have not created any Smart Links yet.", "entity::create::You have not created any Newsletter/Autoresponsers yet.", "entity::create::You have not created any Kajabi outbounds yet.", "entity::create::You have not created any Outbounds yet.", "entity::create::You have not created any Tagging Pixels yet.", "entity::create::You have not created any Emails yet.", "entity::create::You have not created any SMS yet.", "entity::create::You have not created any API Keys yet.", "entity::create::You have not created any Business Card Readers yet.", "entity::create::You have not created any Email Requests yet.", "entity::create::You have not created any SMS Requests yet.", "entity::create::You have not created any Subscription Forms yet.", "entity::create::You have not created any Facebook Buttons yet.", "entity::create::You have not created any Products yet.", "entity::create::You have not created any Facebook Audiences yet.", "entity::create::You have not created any Countdowns yet.", "entity::create::You have not created any Kajabis yet.", "entity::create::You have not created any Automation Templates yet.", "entity::create::You have not created any Webinars yet.", "entity::create::You have not created any Zapier outbounds yet.", "entity::create::You have not created any Statistics yet.", "entity::create::You have not created any Exit Lightboxes yet.", "entity::create::You have not created any Feedback Forms yet.", "entity::create::You have not created any One Time Offers yet.", "entity::create::You have not created any Social Proof Counters yet.", "entity::create::You have not created any Website Splittests yet.", "entity::create::You have not created any Splittests yet.", "entity::create::You have not created any Calendars yet.", "entity::create::You have not created any Labels yet.", "entity::create::You have not created any Signatures yet.", "entity::create::You have not created any Double OptIn Processes yet.", "entity::select::Please select a Calendar from the left or create a new one.", "entity::select::Please select a MetaLabel from the left or create a new one.", "service::error::No response", "service::error::Service not found", "service::error::Access denied.", "service::error::Object not found.", "service::error::Server error.", "flowchart::condition::type::Tagging", "flowchart::condition::operation::Contact has manual tag", "flowchart::condition::operation::Contact has not manual tag", "flowchart::condition::operation::Contact has any manual tag", "flowchart::condition::operation::Contact has not any manual tag", "flowchart::condition::type::Automation", "flowchart::condition::operation::Contact has automation", "flowchart::condition::operation::Contact has not automation", "flowchart::condition::operation::Contact has any automation", "flowchart::condition::operation::Contact has not any automation", "flowchart::condition::type::Newsletter/Autoresponder", "flowchart::condition::operation::Contact has Newsletter/Autoresponder", "flowchart::condition::operation::Contact has not Newsletter/Autoresponder", "flowchart::condition::operation::Contact has any Newsletter/Autoresponder", "flowchart::condition::operation::Contact has not any Newsletter/Autoresponder", "flowchart::condition::type::Emails", "flowchart::condition::operation::Contact has email", "flowchart::condition::operation::Contact has not email", "flowchart::condition::operation::Contact has any email", "flowchart::condition::operation::Contact has not any email", "flowchart::condition::type::SMS", "flowchart::condition::operation::Contact has sms", "flowchart::condition::operation::Contact has not sms", "flowchart::condition::operation::Contact has any sms", "flowchart::condition::operation::Contact has not any sms", "flowchart::condition::type::SmartLink", "flowchart::condition::operation::Contact has SmartLink", "flowchart::condition::operation::Contact has not SmartLink", "flowchart::condition::operation::Contact has any SmartLink", "flowchart::condition::operation::Contact has not any SmartLink", "flowchart::condition::type::Tagging Pixel", "flowchart::condition::verb::activated", "flowchart::condition::operation::Contact has Tagging Pixel", "flowchart::condition::operation::Contact has not Tagging Pixel", "flowchart::condition::operation::Contact has any Tagging Pixel", "flowchart::condition::operation::Contact has not any Tagging Pixel", "flowchart::condition::type::Conversion Pixel", "flowchart::condition::operation::Contact has Conversion Pixel", "flowchart::condition::operation::Contact has not Conversion Pixel", "flowchart::condition::operation::Contact has any Conversion Pixel", "flowchart::condition::operation::Contact has not any Conversion Pixel", "flowchart::condition::type::Business Card Reader Event", "flowchart::condition::operation::Contact has Business Card Reader Event", "flowchart::condition::operation::Contact has not Business Card Reader Event", "flowchart::condition::operation::Contact has any Business Card Reader Event", "flowchart::condition::operation::Contact has not any Business Card Reader Event", "flowchart::condition::type::Outbound", "flowchart::condition::operation::Contact has Outbound", "flowchart::condition::operation::Contact has not Outbound", "flowchart::condition::operation::Contact has any Outbound", "flowchart::condition::operation::Contact has not any Outbound", "flowchart::condition::type::Subscription Form", "flowchart::condition::operation::Contact has Subscription Form", "flowchart::condition::operation::Contact has not Subscription Form", "flowchart::condition::operation::Contact has any Subscription Form", "flowchart::condition::operation::Contact has not any Subscription Form", "flowchart::condition::type::Facebook Button", "flowchart::condition::operation::Contact has Facebook Button", "flowchart::condition::operation::Contact has not Facebook Button", "flowchart::condition::operation::Contact has any Facebook Button", "flowchart::condition::operation::Contact has not any Facebook Button", "flowchart::condition::type::Request By Email", "flowchart::condition::operation::Contact has Request By Email", "flowchart::condition::operation::Contact has not Request By Email", "flowchart::condition::operation::Contact has any Request By Email", "flowchart::condition::operation::Contact has not any Request By Email", "flowchart::condition::type::Request By SMS", "flowchart::condition::operation::Contact has Request By SMS", "flowchart::condition::operation::Contact has not Request By SMS", "flowchart::condition::operation::Contact has any Request By SMS", "flowchart::condition::operation::Contact has not any Request By SMS", "flowchart::condition::type::API Key", "flowchart::condition::operation::Contact has API Key", "flowchart::condition::operation::Contact has not API Key", "flowchart::condition::operation::Contact has any API Key", "flowchart::condition::operation::Contact has not any API Key", "flowchart::condition::type::Business Card Reader", "flowchart::condition::operation::Contact has Business Card Reader", "flowchart::condition::operation::Contact has not Business Card Reader", "flowchart::condition::operation::Contact has any Business Card Reader", "flowchart::condition::operation::Contact has not any Business Card Reader", "flowchart::condition::type::Product", "flowchart::condition::verb::bought", "flowchart::condition::operation::Contact has Product", "flowchart::condition::operation::Contact has not Product", "flowchart::condition::operation::Contact has any Product", "flowchart::condition::operation::Contact has not any Product", "flowchart::condition::type::Refund", "flowchart::condition::operation::Contact has Refund", "flowchart::condition::operation::Contact has not Refund", "flowchart::condition::operation::Contact has any Refund", "flowchart::condition::operation::Contact has not any Refund", "flowchart::condition::type::Chargeback", "flowchart::condition::operation::Contact has Chargeback", "flowchart::condition::operation::Contact has not Chargeback", "flowchart::condition::operation::Contact has any Chargeback", "flowchart::condition::operation::Contact has not any Chargeback", "flowchart::condition::type::Rebill", "flowchart::condition::operation::Contact has Rebill", "flowchart::condition::operation::Contact has not Rebill", "flowchart::condition::operation::Contact has any Rebill", "flowchart::condition::operation::Contact has not any Rebill", "flowchart::condition::type::Rebill Status", "flowchart::condition::verb::were completed", "flowchart::condition::verb::have expired", "flowchart::condition::operation::The rebill payments for product", "flowchart::condition::operation::The rebill payments for any product", "flowchart::condition::type::Digistore Affiliate", "flowchart::condition::verb::affiliated", "flowchart::condition::operation::Contact has Digistore Affiliate", "flowchart::condition::operation::Contact has not Digistore Affiliate", "flowchart::condition::operation::Contact has any Digistore Affiliate", "flowchart::condition::operation::Contact has not any Digistore Affiliate", "flowchart::condition::type::Plugin Inbound", "flowchart::condition::operation::Contact has Plugin", "flowchart::condition::operation::Contact has not Plugin", "flowchart::condition::operation::Contact has any Plugin", "flowchart::condition::operation::Contact has not any Plugin", "flowchart::state::type::Start", "flowchart::state::type::Decision", "flowchart::state::type::Goal", "flowchart::state::type::Goto", "flowchart::state::type::Wait", "flowchart::state::type::Start Automation", "flowchart::state::type::Stop Automation", "flowchart::state::type::Splittest", "flowchart::state::type::Exit", "flowchart::state::type::Facebook Audience Add", "flowchart::state::type::Email", "flowchart::state::type::SMS", "flowchart::state::type::Notify by Email", "flowchart::state::type::Notify by SMS", "flowchart::state::type::Outbound", "flowchart::state::type::tagging", "flowchart::state::type::Untagging", "flowchart::state::type::Set Field", "flowchart::state::type::Detect Gender", "flowchart::state::type::Detect Name", "flowchart::state::type::FullContact", "flowchart::state::type::Unsubscribe", "entity::group::Communication", "entity::group::Automation Structure", "entity::group::Contact Updates", "entity::type::Splittest Variant", "flowchart::command::Move single action", "flowchart::command::Move single action (not possible for this action)", "flowchart::command::Move this action and all following actions", "flowchart::command::Move this action and all following actions (not possible for this action)", "flowchart::command::Copy single action", "flowchart::command::Copy this action and all following actions", "flowchart::command::Copy this action and all following actions (not possible for this action)", "flowchart::label::Active", "flowchart::label::Inactive", "flowchart::command::Reset zoom", "flowchart::command::Zoom in", "flowchart::command::Zoom out", "flowchart::command::Fit to screen", "flowchart::command::Expand Flowchart", "flowchart::command::Collapse Flowchart", "flowchart::command::Collapse Flowchart to YES", "flowchart::command::Collapse Flowchart to NO", "flowchart::modal::title::Add Action", "flowchart::modal::title::Delete action", "flowchart::modal::title::Edit @@action@@", "flowchart::command::(not possible for this action)", "flowchart::tooltip::@@count@@ waiting contacts", "general::confirm::Attention!", "general::confirm::Do you really want to delete?", "general::confirm::Note: This action can not be undone!", "general::error::Oops! Something Went Wrong!", "general::error::An unexpected error occurred, please refresh your browser.", "general::button::Homepage", "general::button::Reload", "calendar::option::Original Date", "calendar::option::Plus/Minus X days", "calendar::option::Previous business day", "calendar::option::Next business day", "calendar::option::Previous weekday", "calendar::option::Next weekday", "calendar::option::First day", "calendar::option::Last day", "calendar::option::First business day", "calendar::option::Last business day", "calendar::option::First weekday", "calendar::option::Last weekday", "calendar::option::Nth weekday", "calendar::option::Weekday", "general::form::element::title::Type", "calendar::option::Relative dates", "calendar::option::Fixed Date", "calendar::form::element:title::Base", "calendar::option::Holidays", "calendar::option::Quarters", "calendar::option::Months", "calendar::option::Weeks", "calendar::form::element:title::Holidays", "calendar::form::element:title::Quarter", "calendar::form::element:title::Months", "calendar::form::element:title::Weeks", "general::form::element::title::Operation", "general::form::element::title::Date From", "general::form::element::title::Date To", "general::form::element::title::Nth weekday", "calendar::option::Last", "calendar::form::element:title::Weekdays", "calendar::form::element:title::Business days", "calendar::form::element::title::Use National Holidays and exclude them from business days", "general::label::religion::Christian", "general::label::country::Germany", "general::label::country::Austria", "general::label::country::Switzerland", "general::button::Reset", "calendar::message::The calendar has been successfully updated.", "email::form::element:title::Send preview email", "general::form::placeholder::Insert an email address...", "email::option::From name", "email::option::No placeholder found", "signature::option::Default Signature", "email::form::element:title::Copy content of another email", "general::option::Make notes", "general::option::Notes", "general::form::placeholder::Insert notes...", "email::form::element:title::Sender name", "email::form::element::Custom Sender Name", "email::form::placeholder::Insert a sender name...", "email::form::element:title::From email address", "email::form::element:title::Reply to email address", "email::form::element:title::CC email address", "email::form::element:title::BCC email address", "email::form::element:title::Sender domain", "email::form::element:title::Subject", "email::form::placeholder::Insert a subject...", "email::form::element:title::Content type", "email::form::element:title::Parameter", "email::form::element:title::Plain text", "email::option::Deactivate HTML editor", "email::form::element:title::HTML", "email::form::element:title::Edit HTML content", "email::form::element:title::Signature", "email::option::Turn off link tracking", "email::option::Dispatch this email on our premium mailservers.", "email::option::Preview for GMail", "email::option::Use as template to send emails from the Contact form", "email::option::Use as template to send emails as invoice on payments", "general::form::element::title::Description", "general::form::placeholder::Insert a description...", "metasearch::form::element:title::Label", "metasearch::form::element:title::Show objects without any labels", "metasearch::form::element::title::Name contains", "general::label::Klick-Tipp Entities", "general::button::Search", "metalabels::form::element::title::Remove labels", "metalabels::form::element::title::Add existing labels", "general::button::Done", "email::form::element:title::Send preview sms", "general::form::placeholder::Insert a mobile number...", "sms::option::From name", "sms::option::You do not have any providers configured!", "sms::option::No placeholder found", "sms::form::element:title::Sender name", "sms::form::placeholder::Insert a sender name...", "sms::form::element:title::Sender domain", "sms::option::Turn off link tracking", "sms::form::element:title::Parameter", "sms::form::element:title::Plain text", "emaileditor::validationresult::headline::Error", "emaileditor::validationresult::headline::Success", "emaileditor::validationresult::info::Your email has been validated and successfully published.", "emaileditor::validationresult::info::Your email could not be published due to the following issues.", "emaileditor::validationresult::item::Unexpected error!", "emaileditor::validationresult::item::Note: Missing subscriber info link in html content.", "emaileditor::validationresult::item::description::We recommend to insert the subscriber info link to improve your delivery rate.", "emaileditor::validationresult::item::Missing signature in html content.", "emaileditor::validationresult::item::description::You can insert the signature element or include the following placeholders in your email.", "emaileditor::validationresult::item::Note: Missing subscriber info link in plain content.", "emaileditor::validationresult::item::Missing signature in plain content.", "emaileditor::validationresult::item::description::You can insert the signature placeholder or include the following placeholders in your email.", "emaileditor::validationresult::item::SpamScore exceeds the maximum allowed.", "emaileditor::validationresult::item::URL shortener found.", "emaileditor::validationresult::item::description::The following URLs use a shortener domain and cannot be used.", "emaileditor::validationresult::item::Blacklisted domain found.", "emaileditor::validationresult::item::description::The following URLs contain a blacklisted domain and cannot be used.", "emaileditor::flashmessage::Email saved.", "emaileditor::flashmessage::Email published.", "flowchart::state::statistic::Automation Started: @@value@@", "flowchart::state::statistic::Automation Finsihed: @@value@@", "flowchart::state::statistic::Email sent: @@value@@", "flowchart::state::statistic::Email opened: @@value@@ (@@percent@@%)", "flowchart::state::statistic::Email clicked: @@value@@ (@@percent@@%)", "flowchart::state::statistic::Goal Converted: @@value@@", "flowchart::state::statistic::Goal Conversion rate: @@value@@%", "flowchart::state::statistic::SMS sent: @@value@@", "flowchart::state::statistic::SMS clicked: @@value@@ (@@percent@@%)", "flowchart::message::Creating Splittest...", "flowchart::message::Copying Splittest...", "flowchart::message::Loading Splittest...", "flowchart::state::statistic::Splittest started: @@date@@", "flowchart::state::statistic::Splittest finished: @@date@@", "flowchart::tooltip::@@count@@ contacts executed this action.", "flowchart::tooltip::@@count@@ contacts converted this action.", "flowchart::message::Splittest has no variants.", "flowchart::message::Error loading Splittest.", "flowchart::state::statistic::Automation Active: @@value@@", "flowchart::state::statistic::Automation Finished: @@value@@", "flowchart::state::statistic::Automation Conversion: @@value@@ (@@rate@@%)", "general::option::All", "flowchart::confirm::Are you sure to delete this action?", "flowchart::confirm::Note: This action has @@count@@ waiting subscribers!", "flowchart::confirm::Note: All action in the NO path will also be deleted.", "entity::delete::Are you sure to delete @@name@@?", "entity::delete::Dependencies: Checking...", "entity::delete::Dependencies: None", "entity::delete::Dependencies: Yes, used in the following objects:", "statistics::message::Loading data...", "statistics::message::You do not have any Statistics created yet.", "statistics::create::Just enter a name above and click create.", "statistics::message::No Statistics found with current filter settings.", "statistics::create::You can click on create above to create a statistic with the name @@name@@", "statistics::message::No data available.", "statistics::button::Add a tag now", "statistics::label::Tags", "statistics::message::You do not have any tags selected yet.", "statistics::message::Loading tag data...", "statistics::message::You have reached the limit of 20 tags.", "email::spamscore::SpamScore", "email::button::Calculate SpamScores", "email::preview::Send all Gmail previews", "email::preview::You Gmail account is not connected to Klick-Tipp yet. [Connect now.](../user/me/gmail-api)", "email::button::Send gmail previews", "email::preview::Send all preview emails", "email::preview::Send all preview sms", "general::filter::View:", "general::filter::Type:", "email::preview::Send Gmail preview", "email::preview::Send preview email", "email::preview::Send preview sms", "general::option::View Settings", "general::label::None", "email::label::Loading emails and sms...", "email::label::This automation has no emails or sms.", "The action has no email.", "The action has no sms.", "automation::statistics::title::Conversion", "automation::statistics::link::upgrade2enterprise::[]()", "automation::statistics::link::findoutmore::[]()", "flowchart::message::This automation is active.", "flowchart::message::There are no todos. This automation is ready to go!", "flowchart::label::ToDo List", "calendar::popup::title::Entries", "calendar::message::This calendar has no entries.", "general::label::Searching...", "metasearch::message::No result", "metasearch::message::Search result: @@count@@", "general::label::Type", "general::label::Name", "general::label::Labels", "general::label::Pager Sizes", "metasearch::title::Bulk actions", "metasearch::title::Search Filter", "email::spamscore::Signature used for analysis: [@@name@@](@@href@@)", "translation::title::Languages", "translation::label::Code", "flowchart::link::Click [here](@@href@@) to set default action colors.", "general::button::Previous", "general::button::Next", "general::label::time::at", "general::form::element::title::Days of the month", "general::option::Clear", "form::error::The form has errors!", "entity::message::Loading data...", "general::form::element::title::Months", "general::label::time::oclock", "general::form::element::title::Weekdays", "flowchart::command::Add condition", "flowchart::command::Add segment", "flowchart::command::Add Splittest Variant", "flowchart::command::Insert here...", "flowchart::tooltip::Click to collapse this fork", "flowchart::tooltip::Click to expand this fork", "flowchart::label::Yes", "flowchart::label::No", "flowchart::command::Add action", "flowchart::label::Toggle Views", "flowchart::label::Zoom", "general::label::ID", "flowchart::tooltip::Drag to connect", "flowchart::tooltip::Click to disconnect", "flowchart::label::Actions", "flowchart::label::Conversions", "flowchart::message::There are no actions that match your filter.", "flowchart::message::Click on an icon to insert an action. Hold shift key to insert and edit.", "flowchart::button::Delete Action", "flowchart::command::Move", "flowchart::command::This action is not possible", "entity::delete::This object cannot be deleted. Please resolve all dependencies first.", "emaileditor::clearallcontent::message::Clear all content from email!", "emaileditor::clearallcontent::confirm::Are you sure you want to call all content? This can not be undone!", "emaileditor::clearallcontent::button::Yes", "emaileditor::clearallcontent::button::No", "emaileditor::toolbar::Preview", "emaileditor::toolbar::Structure", "emaileditor::toolbar::Import", "emaileditor::copyemails::Copy content from existing email.", "emaileditor::copyemails::Note: This will replace the current content of the email.", "emaileditor::copyemails::There are no emails to copy from yet.", "emaileditor::copyemails::After you created some emails with the new editor, you can use them as a template.", "emaileditor::toolbar::Clear All", "emaileditor::toolbar::Save Draft", "emaileditor::toolbar::Publish", "emaileditor::publish::What's next?", "emaileditor::publish::Send preview-email", "emaileditor::publish::View spamscore", "emaileditor::publish::Edit email settings", "emaileditor::publish::Create campaign", "emaileditor::publish::Choose campaign", "emaileditor::publish::Send newsletter", "emaileditor::publish::Start follow-up e-mail", "emaileditor::spamscore::Spamscore", "emaileditor::spamscore::Total"]

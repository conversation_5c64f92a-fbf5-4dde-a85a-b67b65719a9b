(function($) {
  window.dataLayer = window.dataLayer || [];

  let seekTolerance = 7.5;
  let settings = Drupal.settings;
  let start = new Date().getTime();
  let timer = {
    countdown: null,
    offer: null
  };

  let attachSeekEvents = function () {
    let handles = $('[data-seek]');

    handles.on('click', function (event) {
      event.preventDefault();

      let data = $(this).data();
      let player = Wistia.api(settings['WebinarStream']['StreamW']);

      player.time(data.seek);
      player.play();
    });
  };

  let attachWebinarEvents = function (player) {
    if (settings['Events']) player.bind('secondchange', triggerTaggingEvents);
    if (settings['WebinarLive']) player.bind('secondchange', triggerJavascriptEvents);

    if (settings['WebinarLive']) {
      let getLiveTime = function () {
        let current = new Date().getTime() + settings['WebinarLive']['CurrentTime'] - start;
        return (current - settings['WebinarLive']['Start']) / 1000;
      };

      player.bind('pause', player.play);

      player.bind('play', function () {
        player.unbind('play');
        player.volume(1);
        player.time(getLiveTime());

        $('.wistia_overlay').show();
      });

      player.bind('timechange', function (t) {
        let time = Math.floor(t * 10);
        let live = Math.floor(getLiveTime() * 10);

        if (time < live - seekTolerance || time > live + seekTolerance) {
          player.time(getLiveTime());
        }
      });

      player.bind('end', function () {
        player.unbind('pause');
        player.pause();
        document.location.replace(settings['WebinarLive']['Redirect']);
      });
    }
  };

  let initWebinarStream = function () {
    if (!settings['WebinarStream']) return;

    let options = {
      controlsVisibleOnLoad: true,
      playButton: true,
      settingsControl: false
    };

    if (settings['WebinarLive']) {
      _.extend(options, {
        autoPlay: true
      });
    }

    if (!settings['WebinarLive']) {
      _.extend(options, {
        playbar: true,
        smallPlayButton: true,
        volumeControl: true
      });
    }

    window._wq = window._wq || [];
    window._wq.push({
      id: settings['WebinarStream']['StreamW'],
      options: options,
      onReady: function (player) {
        attachWebinarEvents(player);
      }
    });
  };

  let triggerJavascriptEvents = function (seconds) {
    let callbacks = {
      WebinarGoogleTagging: function (trigger) {
        if (seconds >= trigger - 5 && seconds <= trigger + 5) {
          dataLayer.push({event: settings['WebinarLive']['GoogleTagging'][trigger]});
        }
      },
      WebinarLiveShowOffer: function () {
        $('.live-delay').show();
        $('#webinar-live-offer').show();
        $('#webinar-live-offer-badges').show();
        $('#webinar-download-notes').on('click', function (event) {event.preventDefault();})
      }
    };

    _.each(settings['WebinarLive']['EventsJavascript'], function (item, key) {
      if (callbacks[item] && key <= seconds) {
        callbacks[item].call(this, key);
        delete settings['WebinarLive']['EventsJavascript'][key];
      }
    });
  };

  let triggerTaggingEvents = function (seconds) {
    _.each(settings['Events']['tagging'], function (item, key) {
      if (key <= seconds && key >= (seconds - 240)) {
        $.ajax({type: 'POST', url: settings['Events']['tagging_callback'], data: {event: item}, dataType: 'json'});
        delete settings['Events']['tagging'][key];
      }
    });
  };

  let updateOfferCountdown = function () {
    let countdown = settings['Offer'];
    let current = new Date().getTime() + countdown['CurrentTime'] - start;
    let display = new Date(countdown['CountDownEnd'] - current);

    if (display.getTime() <= countdown['CountDownRedirectTime']) {
      timer.countdown = clearInterval(timer.offer);
      document.location.replace(countdown['CountDownRedirectURL']);
      return;
    }

    let days = Math.floor(display.getTime() / (1000 * 60 * 60 * 24));
    let hours = display.getUTCHours();
    let minutes = display.getUTCMinutes();
    let seconds = display.getUTCSeconds();

    let remaining = [];

    if (days) remaining.push(days + (days === 1 ? ' Tag' : ' Tage'));
    if (hours) remaining.push(hours + (hours === 1 ? ' Stunde' : ' Stunden'));
    if (minutes) remaining.push(minutes + (minutes === 1 ? ' Minute' : ' Minuten'));

    remaining.push(seconds + (seconds === 1 ? ' Sekunde' : ' Sekunden'));

    $('.countdown-timer').text(remaining.join(', '));
  };

  let updateWebinarCountdown = function () {
    let countdown = settings['CountDown'];
    let current = new Date().getTime() + countdown['CurrentTime'] - start;
    let display = new Date(countdown['CountDownEnd'] - current);

    if (display.getTime() <= countdown['CountDownRedirectTime']) {
      timer.countdown = clearInterval(timer.countdown);
      document.location.replace(countdown['CountDownRedirectURL']);
      return;
    }

    let hours = display.getUTCHours();
    let minutes = display.getUTCMinutes();
    let seconds = display.getUTCSeconds();

    $('.counter-digits').removeClass();
    $('.webinar-cd-label').removeClass('singular');

    $('#webinar-cd-hours-10').addClass('counter-digits digit-' + (Math.floor(hours / 10)));
    $('#webinar-cd-hours-1').addClass('counter-digits digit-' + (hours % 10));

    if (hours === 1) $('#webinar-cd-label-hours').addClass('singular');

    $('#webinar-cd-minutes-10').addClass('counter-digits digit-' + (Math.floor(minutes / 10)));
    $('#webinar-cd-minutes-1').addClass('counter-digits digit-' + (minutes % 10));

    if (minutes === 1) $('#webinar-cd-label-minutes').addClass('singular');

    $('#webinar-cd-seconds-10').addClass('counter-digits digit-' + (Math.floor(seconds / 10)));
    $('#webinar-cd-seconds-1').addClass('counter-digits digit-' + (seconds % 10));

    if (seconds === 1) $('#webinar-cd-label-seconds').addClass('singular');
  };

  $(function() {
    initWebinarStream();

    if (settings['WebinarStream']) attachSeekEvents();

    if (settings['CountDown'] && settings['CountDown']['Autostart']) {
      timer.countdown = setInterval(updateWebinarCountdown, 250);
      updateWebinarCountdown();
    }

    if (settings['Offer']) {
      timer.offer = setInterval(updateOfferCountdown, 250);
      updateOfferCountdown();
    }

    if ($('.webinar-social').length) {
      (function(d, s, id) {
        let js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {return;}
        js = d.createElement(s); js.id = id;
        js.src = '//connect.facebook.net/de_DE/all.js#xfbml=1';
        fjs.parentNode.insertBefore(js, fjs);
      }(document, 'script', 'facebook-jssdk'));

      $(document.body).append($('<script>', {type: 'text/javascript', src: 'https://apis.google.com/js/plusone.js'}));
    }
  });
})(jQuery);

window['WebinarChatStatus'] = function ( type ) {
  if ( !type ) {
    $('#webinar-chat-status').empty();
    return;
  }

  var messages = Drupal.settings['WebinarChat']['Messages'];

  var types = {
    'success': 'alert alert-success',
    'sending': 'alert alert-warning',
    'error': 'alert alert-danger',
    'empty': 'alert alert-danger',
    'empty_name': 'alert alert-danger'
  };

  var status = '<div class="' + types[type] + '"><button data-dismiss="alert" class="close" type="button">&times;</button>' + messages[type] + '</div>';

  $('#webinar-chat-status').html(status);
};

window['WebinarChatPostMessage'] = function () {
  if ( Drupal.settings['WebinarChat']['SubscriberID'] == '' ) {
    return;
  }

  var WebinarChatFirstname = $('#webinar-chat-firstname').val();
  var WebinarChatLastname = $('#webinar-chat-lastname').val();
  var WebinarChatPhoneNumber = $('#webinar-chat-phonenumber').val();
  var msg = $('#webinar-chat-post').val() || "";

  if ( WebinarChatLastname == '' || WebinarChatLastname == '' ) {
    window['WebinarChatStatus']('empty_name');
    return;
  }

  if ( msg == '' ) {
    window['WebinarChatStatus']('empty');
    return;
  }

  Drupal.settings['WebinarChat']['FirstName'] = WebinarChatFirstname;
  Drupal.settings['WebinarChat']['LastName'] = WebinarChatLastname;
  Drupal.settings['WebinarChat']['PhoneNumber'] = WebinarChatPhoneNumber;

  window['WebinarChatStatus']();

  $('#webinar-chat-feedback').hide();
  $('#webinar-chat-post').val('');

  var VideoTime = ( window['wistiaEmbed'] ) ? window['wistiaEmbed'].time() : 0;

  var data = {
    'ajax': true,
    'VideoTime': VideoTime,
    'SubscriberID': Drupal.settings['WebinarChat']['SubscriberID'],
    'FirstName': Drupal.settings['WebinarChat']['FirstName'],
    'LastName': Drupal.settings['WebinarChat']['LastName'],
    'PhoneNumber': Drupal.settings['WebinarChat']['PhoneNumber'],
    'Feedback': msg,
    'WebinarStart': Drupal.settings['WebinarChat']['WebinarStart']
  };

  window['WebinarChatStatus']('sending');

  $.post( Drupal.settings['WebinarChat']['Callback'], data, function( response ) {
    if ( !response || response.error ) {
      window['WebinarChatStatus']('error');
    }
    else {
      window['WebinarChatStatus']('success');
    }

    $('#webinar-chat-feedback').show();

  }, "json");
};

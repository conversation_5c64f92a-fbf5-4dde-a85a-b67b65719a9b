<html>
<head>
    <title>K<PERSON>-<PERSON>ipp UI Style Guide</title>
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,300i,400,400i,600,600i,700,700i" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.0/jquery.min.js"></script>
    <script type="text/javascript" src="../app/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="../app/js/libs/lodash.min.js"></script>
    <script type="text/javascript" src="../app/js/main.js"></script>
    <script type="text/javascript" src="../app/js/plugins/cm.toggle.js"></script>
    <script type="text/javascript" src="../app/js/plugins/cm.tabs.js"></script>
    <link rel="stylesheet" href="stylesheets/bootstrap.min.css">
    <link rel="stylesheet" href="stylesheets/style.css">
    <link rel="stylesheet" href="stylesheets/main.css">
    <link rel="stylesheet" href="stylesheets/styles.css">
</head>

<body>
    <div id="site-wrapper" class="styleguide">
		<header id="main-header" role="banner">
            <h1>Klick-Tipp UI Style Guide</h1>
		</header>
		<main id="main-content" class="">
            <header class="page-header">
            </header>
            <section class="page-content">
                <section class="page-section">
                    <section class="page-section-content">
                        <section class="ui-box ui-tabs">
                            <nav class="tab-navigation even">
                                <ul>
                                    <li data-type="cm-tab" data-target="ui-panel-01" class="tab-toggle glyph-action is-open">Aktion</li>
                                    <li data-type="cm-tab" data-target="ui-panel-02" class="tab-toggle glyph-choice">Wenn... Dann...</li>
                                    <li data-type="cm-tab" data-target="ui-panel-03" class="tab-toggle glyph-delay">Warte</li>
                                </ul>
                            </nav>
                            <article data-section="ui-panel-01" class="tab-panel is-open">
                                <header>
                                    <p class="type-instruction">Choose an action to apply to this step...</p>
                                </header>
                                <div class="grid columns-2 gutter-large">
                                    <a class="button-action glyph-email-full grid-6-12" href="">E-Mail Benachirichtigung</a>
                                    <a class="button-action glyph-bell-full grid-6-12" href="">E-Mail Benachirichtigung</a>
                                    <a class="button-action glyph-heart-full grid-6-12" href="">E-Mail Benachirichtigung</a>
                                    <a class="button-action glyph-star-full grid-6-12" href="">E-Mail Benachirichtigung</a>
                                    <a class="button-action glyph-document-full grid-6-12" href="">E-Mail Benachirichtigung</a>
                                </div>
                            </article>
                            <article data-section="ui-panel-02" class="tab-panel">
                                <header>
                                    <form>
                                        <input style="width: 100%;" type="text" value="" placeholder="Choose a title for this set of conditions...">
                                    </form>
                                </header>
                                <div class="ui-panel">
                                    <div class="panel-content">
                                        <header>
                                            <div class="ui-dropdown-filter">
                                                <div class="dropdown-toggle">Kontak has das Tag</div>
                                                <ul class="dropdown-menu">
                                                    <li class="dropdown-option selected">Kontak has das Tag</li>
                                                    <li class="dropdown-option">Another Option</li>
                                                    <li class="dropdown-option">Lorem Sit Amet Quosque Tandem</li>
                                                    <li class="dropdown-option">Lorem Ipsum</li>
                                                </ul>
                                            </div>
                                            <a class="action-icon glyph-cross panel-close">Remove Section</a>
                                        </header>
                                        <form>
                                            <fieldset>
                                                <div class="ui-select autocomplete">
                                                    <input type="text" placeholder="Choose option...">
                                                    <ul class="select-menu">
                                                        <li class="select-option">Option One</li>
                                                        <li class="select-option">Option Two</li>
                                                        <li class="select-option">Option Three</li>
                                                        <li class="select-option">Option Four</li>
                                                    </ul>
                                                </div>
                                            </fieldset>
                                            <fieldset>
                                                <input type="text">
                                            </fieldset>
                                        </form>
                                    </div>
                                    <div class="panel-content">
                                        <a class="action-icon glyph-cross panel-close">Remove Section</a>
                                        <form>
                                            <fieldset>
                                                <div class="ui-select autocomplete grid-4-12">
                                                    <input type="text" placeholder="Choose option...">
                                                    <ul class="select-menu">
                                                        <li class="select-option">Option One</li>
                                                        <li class="select-option">Option Two</li>
                                                        <li class="select-option">Option Three</li>
                                                        <li class="select-option">Option Four</li>
                                                    </ul>
                                                </div>
                                            </fieldset>
                                            <fieldset>
                                                <input type="text">
                                            </fieldset>
                                        </form>
                                    </div>
                                    <footer class="align-center">
                                        <a class="action-icon glyph-plus">Add Section</a>
                                    </footer>
                                </div>
                            </article>
                            <article data-section="ui-panel-03" class="tab-panel is-open">
                                <section class="section-content">
                                    <header>
                                        <h2>Verzogerung</h2>
                                    </header>
                                    <form>
                                        <fieldset>
                                            <div class="ui-select autocomplete grid-6-12 is-open">
                                                <input type="text" placeholder="Choose option...">
                                                <ul class="select-menu">
                                                    <li class="select-option">
                                                        <a href="">Option One</a>
                                                        <div class="ui-actions">
                                                            <a class="action-icon glyph-edit-2" href="" title="Edit">Edit</a>
                                                            <a class="action-icon glyph-cross-bold" title="Delete" href="">Delete</a>
                                                        </div>
                                                    </li>
                                                    <li class="select-option">
                                                        <a href="">Option Two</a>
                                                        <div class="ui-actions">
                                                            <a class="action-icon glyph-edit-2" href="" title="Edit">Edit</a>
                                                            <a class="action-icon glyph-cross-bold" title="Delete" href="">Delete</a>
                                                        </div>
                                                    </li>
                                                    <li class="select-option">
                                                        <a href="">Option Three</a>
                                                        <div class="ui-actions">
                                                            <a class="action-icon glyph-edit-2" href="" title="Edit">Edit</a>
                                                            <a class="action-icon glyph-cross-bold" title="Delete" href="">Delete</a>
                                                        </div>
                                                    </li>
                                                    <li class="select-option">
                                                        <a href="">Option Four</a>
                                                        <div class="ui-actions">
                                                            <a class="action-icon glyph-edit-2" href="" title="Edit">Edit</a>
                                                            <a class="action-icon glyph-cross-bold" title="Delete" href="">Delete</a>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </fieldset>
                                         <fieldset>
                                            <div class="ui-select autocomplete">
                                                <input type="text" placeholder="bitte wahlen...">
                                                <ul class="select-menu">
                                                    <li class="select-option">Option One</li>
                                                    <li class="select-option">Option Two</li>
                                                    <li class="select-option">Option Three</li>
                                                    <li class="select-option">Option Four</li>
                                                </ul>
                                            </div>
                                        </fieldset>
                                         <fieldset>
                                            <div class="ui-select autocomplete grid-3-12">
                                                <input type="text" placeholder="Choose option..." value="2">
                                                <ul class="select-menu">
                                                    <li class="select-option">Option One</li>
                                                    <li class="select-option">Option Two</li>
                                                    <li class="select-option">Option Three</li>
                                                    <li class="select-option">Option Four</li>
                                                </ul>
                                            </div>
                                        </fieldset>
                                    </form>
                                </section>
                                <section class="section-content">
                                    <header>
                                        <h2>Soll Klick-Tipp bestimmte Wochentage ausschlieBen?g</h2>
                                    </header>
<!--
                                    <ul class="option-bar">
                                        <li class="option">Mo</li>
                                        <li class="option">Di</li>
                                        <li class="option">Mi</li>
                                        <li class="option">Do</li>
                                        <li class="option">Fri</li>
                                        <li class="option">Sa</li>
                                        <li class="option">So</li>
                                    </ul>
-->
                                </section>
                            </article>
                        </section>
                        <section class="ui-box">
                            <article>
                                <header>
                                    <h3>UI Elements: Action Buttons</h3>
                                </header>
                                <a class="button-action glyph-book-2 grid-6-12" href="">Action Button</a>
                            </article>
                            <article>
                                <header>
                                    <h3>UI Elements: Action Box</h3>
                                </header>
                                <div class="ui-panel">
                                    <form>
                                        <input type="text" placeholder="Placeholder style...">
                                    </form>
                                </div>
                            </article>
                        </section>
                    </section>
                </section>
            </section>
		</main>
		<footer id="main-footer">
            
		</footer>
	</div>
</body>
</html>
<script>
</script>

##
# This file contains the KlickTipp variables referenced in the docker-compose.yml file
#
# *** Find help about Environment Variables and where to put them in the README.md ***
#
# If you need to change one of this Environment Variables please use the correspondig env file:
#   .env        - Variables referenced in the docker-compose.yml file and used to configure DOCKER-COMPOSE.
#                 This variables are NOT SHOWED INSIDE the containers!
#   .default.env - Default env vars used by all environments. This are the default and sane settings used by the Application.
#   local.env   - Specific overwrired env vars used only for LOCAL DEVELOPMENT
#
#################
# AWS Variables #
#################

AWS_ACCOUNT_ID=************
AWS_REGION=eu-west-1
AWS_REGISTRY_NAME=************.dkr.ecr.eu-west-1.amazonaws.com

###################################
# Docker Compose config variables #
###################################
#
# If you want to change the running Docker Container change this variables accordingly
#

# If set to ''true'' use docker compose container name separator underscore ''_''.
# However, the default standard is to use hypen ''-'', hence set to false.
COMPOSE_COMPATIBILITY=false

KLICKTIPP_BASE_HOSTNAME=ktlocal.com

#
# Docker image is created with: "${KLICKTIPP_KLICKTIPP_DOCKER_IMAGE}:${KLICKTIPP_KLICKTIPP_DOCKER_IMAGE_TAG}"
#
KLICKTIPP_KLICKTIPP_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/klicktipp/backend/php
KLICKTIPP_KLICKTIPP_DOCKER_IMAGE_TAG=local

# Format: ${PHP_VERSION}-${IMAGE_VERSION}
KLICKTIPP_PHP_BASE_DOCKER_IMAGE_TAG=8.3-master

KLICKTIPP_OPENRESTY_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/klicktipp/backend/nginx
KLICKTIPP_OPENRESTY_DOCKER_IMAGE_TAG=local

KLICKTIPP_FRONTEND_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/klicktipp/frontend/angular
KLICKTIPP_FRONTEND_DOCKER_IMAGE_TAG=local

APACHE_BASE_REPO=************.dkr.ecr.eu-west-1.amazonaws.com/klicktipp/backend/apache
APACHE_BASE_TAG=0.3.0

KLICKTIPP_PERCONA_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/local_percona
KLICKTIPP_PERCONA_DOCKER_IMAGE_VERSION=0.3.2

KLICKTIPP_DATABASE_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/bitnami/mysql
KLICKTIPP_DATABASE_DOCKER_IMAGE_VERSION=5.7.43-debian-11-r73

# ElastiCache v7.1 is compatible with OSS Redis v7.0.
# https://docs.aws.amazon.com/AmazonElastiCache/latest/red-ug/supported-engine-versions.html
KLICKTIPP_REDIS_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/bitnami/redis
KLICKTIPP_REDIS_DOCKER_IMAGE_VERSION=7.0

KLICKTIPP_GCT_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/container_test
KLICKTIPP_GCT_DOCKER_IMAGE_VERSION=

KLICKTIPP_LINTER_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/lint_php7_and_drupal
KLICKTIPP_LINTER_DOCKER_IMAGE_VERSION=1.1

KLICKTIPP_PHPMYADMIN_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/phpmyadmin/phpmyadmin
KLICKTIPP_PHPMYADMIN_DOCKER_IMAGE_VERSION=latest

KLICKTIPP_REDISCOMMANDER_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/rediscommander/redis-commander
KLICKTIPP_REDISCOMMANDER_DOCKER_IMAGE_VERSION=latest

KLICKTIPP_BEANSTALKD_CONSOLE_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/schickling/beanstalkd-console
KLICKTIPP_BEANSTALKD_CONSOLE_DOCKER_IMAGE_VERSION=latest

KLICKTIPP_NODE_RED_DOCKER_BASE_IMAGE_TAG=4.0.5
KLICKTIPP_NODE_RED_DOCKER_BASE_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/nodered/node-red

KLICKTIPP_BOTDETECTION_DOCKER_IMAGE=************.dkr.ecr.eu-west-1.amazonaws.com/kt-botdetection-go
KLICKTIPP_BOTDETECTION_DOCKER_IMAGE_VERSION=1.0.0rc3
